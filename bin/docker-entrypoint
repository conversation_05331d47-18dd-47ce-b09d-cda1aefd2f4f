#!/bin/bash -e

# Enable jemalloc for reduced memory usage and latency.
if [ -z "${LD_PRELOAD+x}" ]; then
    LD_PRELOAD=$(find /usr/lib -name libjemalloc.so.2 -print -quit)
    export LD_PRELOAD
fi

# If running the rails server then create or migrate existing database
if [ "${@: -2:1}" == "./bin/rails" ] && [ "${@: -1:1}" == "server" ]; then
  # Only run db:prepare if DATABASE_URL is set
  if [ -n "${DATABASE_URL}" ]; then
    echo "Running database migrations..."
    ./bin/rails db:prepare
  else
    echo "DATABASE_URL not set, skipping database setup"
  fi
fi

exec "${@}"
