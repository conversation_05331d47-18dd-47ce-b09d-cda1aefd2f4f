#!/bin/bash

# FormFlow Pro Setup Script
# This script sets up the FormFlow Pro development environment

set -e

echo "🚀 FormFlow Pro Setup Script"
echo "============================"
echo ""

# Check for required tools
echo "📋 Checking requirements..."

check_command() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 is not installed. Please install $1 first."
        exit 1
    else
        echo "✅ $1 is installed"
    fi
}

check_command ruby
check_command bundle
check_command rails
check_command psql
check_command redis-cli
check_command node
check_command yarn

echo ""
echo "📦 Installing dependencies..."
bundle install
yarn install

echo ""
echo "🔧 Setting up environment..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ Created .env file from .env.example"
    echo "⚠️  Please update .env with your configuration"
else
    echo "✅ .env file already exists"
fi

echo ""
echo "🗄️  Setting up database..."
echo "Creating database..."
rails db:create

echo "Running migrations..."
rails db:migrate

echo "Seeding database..."
rails db:seed

echo ""
echo "📊 Setting up test database..."
RAILS_ENV=test rails db:create
RAILS_ENV=test rails db:migrate

echo ""
echo "🧪 Running tests..."
bundle exec rspec --format progress

echo ""
echo "🎨 Building CSS..."
rails tailwindcss:build

echo ""
echo "✨ Setup complete!"
echo ""
echo "To start the development server, run:"
echo "  ./bin/dev"
echo ""
echo "Or start services individually:"
echo "  rails server          # Web server on http://localhost:3000"
echo "  bundle exec sidekiq   # Background jobs"
echo "  redis-server          # Redis (if not already running)"
echo ""
echo "📚 Documentation:"
echo "  - README.md: General documentation"
echo "  - PRD.md: Product requirements"
echo "  - CONTRIBUTING.md: Contribution guidelines"
echo ""
echo "Happy coding! 🎉"
