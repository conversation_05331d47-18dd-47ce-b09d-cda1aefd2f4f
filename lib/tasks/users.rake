namespace :users do
  desc "Confirm all unconfirmed users"
  task confirm_all: :environment do
    unconfirmed = User.where(confirmed_at: nil)
    count = unconfirmed.count

    unconfirmed.update_all(
      confirmed_at: Time.current,
      confirmation_sent_at: Time.current
    )

    puts "Confirmed #{count} user(s)"
  end

  desc "Create a test user"
  task :create_test, [ :email, :password ] => :environment do |t, args|
    email = args[:email] || "<EMAIL>"
    password = args[:password] || "password123"

    user = User.find_or_initialize_by(email: email)
    user.password = password
    user.password_confirmation = password
    user.first_name = "Test"
    user.last_name = "User"
    user.confirmed_at = Time.current
    user.save!

    puts "Created/updated user: #{email} with password: #{password}"
  end
end
