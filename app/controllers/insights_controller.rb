class InsightsController < ApplicationController
  before_action :authenticate_user!
  before_action :require_account!

  def index
    @account = Current.account
    @severity_filter = params[:severity] || "all"
    @status_filter = params[:status] || "all"
    @form_filter = params[:form_id]

    # Base insights query
    @insights = insights_query

    # Get insights grouped by severity for overview
    @insights_by_severity = {
      critical: @insights.where(severity: "critical").count,
      high: @insights.where(severity: "high").count,
      medium: @insights.where(severity: "medium").count,
      low: @insights.where(severity: "low").count
    }

    # Paginated insights for main list
    @paginated_insights = @insights.includes(form: :website)
                                   .order("insights.created_at DESC")
                                   .page(params[:page])
                                   .per(20)

    # Forms for filter dropdown
    @forms_for_filter = @account.forms.includes(:website)
                                      .order("websites.name, forms.name")

    # Recent activity insights
    @recent_insights = insights_query.where("insights.created_at > ?", 7.days.ago)
                                    .order("insights.created_at DESC")
                                    .limit(5)

    # Performance impact insights
    @high_impact_insights = insights_query.where(severity: [ "critical", "high" ])
                                         .where(resolved_at: nil)
                                         .includes(form: :website)
                                         .limit(10)

    # Insight categories for overview
    @insight_categories = calculate_insight_categories
  end

  def show
    @insight = find_insight
    @similar_insights = find_similar_insights
    @impact_analysis = calculate_impact_analysis
    @recommendations = generate_recommendations
  end

  def resolve
    @insight = find_insight

    if @insight.update(resolved_at: Time.current, resolved_by: current_user)
      redirect_to insights_path, notice: "Insight marked as resolved."
    else
      redirect_to insights_path, alert: "Unable to resolve insight."
    end
  end

  def bulk_resolve
    insight_ids = params[:insight_ids] || []

    if insight_ids.any?
      insights = current_account_insights.where(id: insight_ids)
      insights.update_all(
        resolved_at: Time.current,
        resolved_by_id: current_user.id
      )

      redirect_to insights_path, notice: "#{insights.count} insights resolved."
    else
      redirect_to insights_path, alert: "No insights selected."
    end
  end

  def dismiss
    @insight = find_insight

    if @insight.update(dismissed_at: Time.current, dismissed_by: current_user)
      redirect_to insights_path, notice: "Insight dismissed."
    else
      redirect_to insights_path, alert: "Unable to dismiss insight."
    end
  end

  def export
    @insights = insights_query.includes(form: :website)

    respond_to do |format|
      format.csv { send_csv_export }
      format.pdf { send_pdf_export }
    end
  end

  private

  def insights_query
    base_query = current_account_insights

    # Apply severity filter
    base_query = base_query.where(severity: @severity_filter) unless @severity_filter == "all"

    # Apply status filter
    case @status_filter
    when "unresolved"
      base_query = base_query.where(resolved_at: nil, dismissed_at: nil)
    when "resolved"
      base_query = base_query.where.not(resolved_at: nil)
    when "dismissed"
      base_query = base_query.where.not(dismissed_at: nil)
    end

    # Apply form filter
    base_query = base_query.where(form_id: @form_filter) if @form_filter.present?

    base_query
  end

  def current_account_insights
    Insight.joins(form: { website: :account })
           .where(websites: { account_id: @account.id })
  end

  def find_insight
    current_account_insights.find(params[:id])
  end

  def find_similar_insights
    return [] unless @insight.present?

    current_account_insights.where(type: @insight.type)
                           .where.not(id: @insight.id)
                           .limit(5)
  end

  def calculate_impact_analysis
    return {} unless @insight.present?

    form = @insight.form

    {
      affected_sessions: form.form_sessions.where("created_at > ?", @insight.created_at).count,
      potential_lost_conversions: calculate_potential_lost_conversions(form, @insight),
      severity_score: calculate_severity_score(@insight),
      time_since_detection: time_ago_in_words(@insight.created_at)
    }
  end

  def calculate_potential_lost_conversions(form, insight)
    # This would be more sophisticated in a real implementation
    sessions_since = form.form_sessions.where("created_at > ?", insight.created_at).count
    baseline_conversion_rate = form.conversion_rate || 5.0

    case insight.severity
    when "critical"
      (sessions_since * (baseline_conversion_rate * 0.8) / 100).round
    when "high"
      (sessions_since * (baseline_conversion_rate * 0.5) / 100).round
    when "medium"
      (sessions_since * (baseline_conversion_rate * 0.2) / 100).round
    else
      (sessions_since * (baseline_conversion_rate * 0.1) / 100).round
    end
  end

  def calculate_severity_score(insight)
    base_scores = {
      "critical" => 90,
      "high" => 70,
      "medium" => 50,
      "low" => 20
    }

    score = base_scores[insight.severity] || 20

    # Adjust based on how long it's been unresolved
    days_open = (Time.current - insight.created_at) / 1.day
    score += (days_open * 2).to_i

    [ score, 100 ].min
  end

  def generate_recommendations
    return [] unless @insight.present?

    case @insight.type
    when "high_abandonment_field"
      [
        "Consider making this field optional if possible",
        "Add helpful placeholder text or examples",
        "Reduce the number of required fields",
        "Add field validation with clear error messages",
        "Consider splitting complex fields into multiple simpler ones"
      ]
    when "slow_form_completion"
      [
        "Optimize form layout for better usability",
        "Add a progress indicator to show completion status",
        "Remove unnecessary fields to reduce friction",
        "Add auto-save functionality",
        "Consider multi-step form design"
      ]
    when "validation_errors"
      [
        "Improve real-time field validation",
        "Make error messages more descriptive",
        "Add visual indicators for required fields",
        "Implement inline validation feedback",
        "Consider progressive disclosure for complex forms"
      ]
    else
      [
        "Review form analytics for patterns",
        "Test form usability with real users",
        "Monitor form performance regularly",
        "Implement A/B testing for improvements"
      ]
    end
  end

  def calculate_insight_categories
    insights = insights_query

    {
      "Abandonment Issues" => insights.where(type: [ "high_abandonment_field", "form_abandonment" ]).count,
      "Performance Issues" => insights.where(type: [ "slow_form_completion", "field_completion_time" ]).count,
      "Validation Issues" => insights.where(type: [ "validation_errors", "field_errors" ]).count,
      "User Experience" => insights.where(type: [ "usability_issue", "navigation_issue" ]).count,
      "Technical Issues" => insights.where(type: [ "javascript_error", "tracking_issue" ]).count
    }
  end

  def send_csv_export
    require "csv"

    csv_string = CSV.generate do |csv|
      csv << [ "Form", "Website", "Insight Type", "Severity", "Title", "Description", "Created", "Status" ]

      @insights.each do |insight|
        status = if insight.resolved_at
          "Resolved"
        elsif insight.dismissed_at
          "Dismissed"
        else
          "Open"
        end

        csv << [
          insight.form.name,
          insight.form.website.name,
          insight.type,
          insight.severity,
          insight.title,
          insight.description,
          insight.created_at.strftime("%Y-%m-%d"),
          status
        ]
      end
    end

    send_data csv_string,
              filename: "insights-#{Date.current}.csv",
              type: "text/csv"
  end

  def send_pdf_export
    # PDF export would be implemented here using a library like Prawn or WickedPDF
    redirect_to insights_path, alert: "PDF export not yet implemented."
  end
end
