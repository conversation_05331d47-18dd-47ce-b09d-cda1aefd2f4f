class AnalyticsController < ApplicationController
  before_action :authenticate_user!
  before_action :require_account!

  def index
    @account = Current.account
    @date_range = params[:range] || "30"
    @start_date = @date_range.to_i.days.ago.beginning_of_day
    @end_date = Time.current.end_of_day

    # Core metrics
    @total_sessions = form_sessions_in_range.count
    @total_submissions = form_submissions_in_range.count
    @conversion_rate = calculate_conversion_rate
    @abandonment_rate = 100 - @conversion_rate

    # Time-based data for charts
    @sessions_over_time = calculate_sessions_over_time
    @submissions_over_time = calculate_submissions_over_time
    @conversion_funnel = calculate_conversion_funnel

    # Form performance
    @form_performance = calculate_form_performance
    @field_analytics = calculate_field_analytics

    # Traffic sources
    @traffic_sources = calculate_traffic_sources

    # Device & browser analytics
    @device_breakdown = calculate_device_breakdown
    @browser_breakdown = calculate_browser_breakdown

    # Comparison with previous period
    @previous_period_comparison = calculate_previous_period_comparison
  end

  def export
    # Handle CSV/PDF export functionality
    respond_to do |format|
      format.csv { send_csv_export }
      format.pdf { send_pdf_export }
    end
  end

  private

  def form_sessions_in_range
    @form_sessions_in_range ||= FormSession.joins(form: { website: :account })
                                          .where(websites: { account_id: @account.id })
                                          .where(created_at: @start_date..@end_date)
  end

  def form_submissions_in_range
    @form_submissions_in_range ||= FormSubmission.joins(form: { website: :account })
                                                 .where(websites: { account_id: @account.id })
                                                 .where(created_at: @start_date..@end_date)
  end

  def calculate_conversion_rate
    return 0 if @total_sessions == 0
    ((@total_submissions.to_f / @total_sessions) * 100).round(2)
  end

  def calculate_sessions_over_time
    begin
      case @date_range
      when "7"
        group_by_day
      when "30"
        group_by_day
      when "90"
        group_by_week
      else
        group_by_day
      end
    rescue => e
      Rails.logger.error "Error calculating sessions over time: #{e.message}"
      fallback_time_data
    end
  end

  def calculate_submissions_over_time
    begin
      form_submissions_in_range.group_by_day(:created_at, range: @start_date..@end_date)
                               .count
    rescue => e
      Rails.logger.error "Error calculating submissions over time: #{e.message}"
      fallback_time_data
    end
  end

  def calculate_conversion_funnel
    forms = @account.forms.includes(:form_sessions, :form_submissions)

    {
      total_visitors: form_sessions_in_range.select(:session_id).distinct.count,
      form_started: form_sessions_in_range.where(status: [ "active", "completed", "abandoned" ]).count,
      form_completed: form_sessions_in_range.where(status: "completed").count,
      form_submitted: @total_submissions
    }
  end

  def calculate_form_performance
    @account.forms.includes(:form_sessions, :form_submissions)
            .map do |form|
      sessions = form.form_sessions.where(created_at: @start_date..@end_date)
      submissions = form.form_submissions.where(created_at: @start_date..@end_date)

      {
        name: form.name,
        website: form.website.name,
        sessions: sessions.count,
        submissions: submissions.count,
        conversion_rate: sessions.count > 0 ? ((submissions.count.to_f / sessions.count) * 100).round(2) : 0,
        avg_completion_time: calculate_avg_completion_time(sessions.where(status: "completed"))
      }
    end.sort_by { |f| -f[:conversion_rate] }
  end

  def calculate_field_analytics
    FieldEvent.joins(:form_field, form_session: { form: { website: :account } })
              .where(websites: { account_id: @account.id })
              .where(created_at: @start_date..@end_date)
              .group("form_fields.name", :event_type)
              .count
              .group_by { |key, _| key[0] } # Group by field name
              .transform_values do |events|
                total_interactions = events.sum { |_, count| count }
                focus_events = events.find { |key, _| key[1] == "focus" }&.last || 0
                blur_events = events.find { |key, _| key[1] == "blur" }&.last || 0

                {
                  total_interactions: total_interactions,
                  focus_rate: total_interactions > 0 ? ((focus_events.to_f / total_interactions) * 100).round(2) : 0,
                  completion_rate: total_interactions > 0 ? ((blur_events.to_f / total_interactions) * 100).round(2) : 0
                }
              end
  end

  def calculate_traffic_sources
    # This would integrate with your tracking data to show referrer sources
    # For now, returning mock data structure
    {
      "Direct" => 45,
      "Search Engines" => 30,
      "Social Media" => 15,
      "Referrals" => 10
    }
  end

  def calculate_device_breakdown
    # Mock data - would integrate with actual device tracking
    {
      "Desktop" => 60,
      "Mobile" => 35,
      "Tablet" => 5
    }
  end

  def calculate_browser_breakdown
    # Mock data - would integrate with actual browser tracking
    {
      "Chrome" => 65,
      "Safari" => 20,
      "Firefox" => 10,
      "Edge" => 5
    }
  end

  def calculate_previous_period_comparison
    previous_start = (@start_date - (@end_date - @start_date)).beginning_of_day
    previous_end = @start_date.end_of_day

    previous_sessions = FormSession.joins(form: { website: :account })
                                  .where(websites: { account_id: @account.id })
                                  .where(created_at: previous_start..previous_end)
                                  .count

    previous_submissions = FormSubmission.joins(form: { website: :account })
                                        .where(websites: { account_id: @account.id })
                                        .where(created_at: previous_start..previous_end)
                                        .count

    {
      sessions_change: calculate_percentage_change(@total_sessions, previous_sessions),
      submissions_change: calculate_percentage_change(@total_submissions, previous_submissions),
      conversion_change: calculate_percentage_change(@conversion_rate,
        previous_sessions > 0 ? ((previous_submissions.to_f / previous_sessions) * 100).round(2) : 0)
    }
  end

  def calculate_percentage_change(current, previous)
    return 0 if previous == 0
    (((current - previous).to_f / previous) * 100).round(2)
  end

  def calculate_avg_completion_time(completed_sessions)
    durations = completed_sessions.where.not(session_duration: nil).pluck(:session_duration)
    return 0 if durations.empty?
    (durations.sum / durations.count).round(2)
  end

  def group_by_day
    form_sessions_in_range.group_by_day(:created_at, range: @start_date..@end_date).count
  end

  def group_by_week
    form_sessions_in_range.group_by_week(:created_at, range: @start_date..@end_date).count
  end

  def fallback_time_data
    # Generate fallback data when groupdate fails
    days_count = (@end_date.to_date - @start_date.to_date).to_i + 1
    result = {}

    days_count.times do |i|
      date = @start_date.to_date + i.days
      result[date] = 0
    end

    result
  end
end
