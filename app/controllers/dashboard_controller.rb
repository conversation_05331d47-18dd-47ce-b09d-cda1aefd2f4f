class DashboardController < ApplicationController
  before_action :authenticate_user!
  before_action :require_account!

  def index
    @account = Current.account
    @websites = @account.websites.includes(:forms)
    @total_forms = @account.forms.count
    @total_sessions = @account.form_submissions.where("form_submissions.created_at > ?", 30.days.ago).count

    # Recent activity
    @recent_submissions = @account.form_submissions
                                  .includes(form: :website)
                                  .order(submitted_at: :desc)
                                  .limit(10)

    # Performance metrics
    @avg_conversion_rate = @account.forms.average(:conversion_rate)&.round(2) || 0
    @avg_abandonment_rate = @account.forms.average(:abandonment_rate)&.round(2) || 0

    # Critical insights
    @critical_insights = Insight.joins(form: { website: :account })
                               .where(websites: { account_id: @account.id })
                               .where(severity: "critical")
                               .where(resolved_at: nil)
                               .limit(5)

    # Additional metrics for new dashboard
    @total_conversions = @account.form_submissions.count
    @active_forms = @account.forms.count

    # Top performing forms
    @top_forms = @account.forms
                        .where("conversion_rate > ?", 0)
                        .order(conversion_rate: :desc)
                        .limit(3)

    # Form engagement breakdown (for donut chart)
    @engagement_breakdown = calculate_engagement_breakdown

    # Weekly trends
    @weekly_data = calculate_weekly_trends

    # Recent field events for activity feed
    @recent_events = FieldEvent.joins(form_session: { form: { website: :account } })
                              .where(websites: { account_id: @account.id })
                              .order(created_at: :desc)
                              .limit(5)
  end

  def monitoring
    @account = Current.account
    @websites = @account.websites.includes(:forms)
    
    # Real-time monitoring data
    @active_sessions = FormSession.joins(form: { website: :account })
                                 .where(websites: { account_id: @account.id })
                                 .where(status: "active")
                                 .count
    
    # Last 24 hours activity
    @recent_sessions = FormSession.joins(form: { website: :account })
                                 .where(websites: { account_id: @account.id })
                                 .where("form_sessions.created_at > ?", 24.hours.ago)
                                 .order(created_at: :desc)
                                 .limit(20)
    
    # Error rates
    @error_events = FieldEvent.joins(form_session: { form: { website: :account } })
                             .where(websites: { account_id: @account.id })
                             .where(event_type: "error")
                             .where("field_events.created_at > ?", 1.hour.ago)
                             .count
    
    # Performance metrics
    @avg_session_duration = FormSession.joins(form: { website: :account })
                                      .where(websites: { account_id: @account.id })
                                      .where("form_sessions.created_at > ?", 24.hours.ago)
                                      .average(:time_spent)&.round || 0
  end

  private

  def calculate_engagement_breakdown
    # Using form_sessions for status tracking
    total_sessions = FormSession.joins(form: { website: :account })
                               .where(websites: { account_id: @account.id })
                               .count
    return { completed: 0, partial: 0, abandoned: 0 } if total_sessions == 0

    completed = FormSession.joins(form: { website: :account })
                          .where(websites: { account_id: @account.id })
                          .where(status: "completed")
                          .count
    abandoned = FormSession.joins(form: { website: :account })
                          .where(websites: { account_id: @account.id })
                          .where(status: "abandoned")
                          .count
    partial = total_sessions - completed - abandoned

    {
      completed: ((completed.to_f / total_sessions) * 100).round,
      partial: ((partial.to_f / total_sessions) * 100).round,
      abandoned: ((abandoned.to_f / total_sessions) * 100).round
    }
  end

  def calculate_weekly_trends
    7.downto(0).map do |days_ago|
      date = days_ago.days.ago.to_date
      {
        date: date.strftime("%a"),
        submissions: @account.form_submissions.where(created_at: date.all_day).count,
        conversions: @account.form_submissions.where(created_at: date.all_day).count
      }
    end
  end
end
