class SessionReplaysController < ApplicationController
  before_action :authenticate_user!
  before_action :require_account!
  before_action :set_form
  before_action :set_session, only: [ :show ]

  def index
    @date_range = params[:range] || "7"
    @start_date = @date_range.to_i.days.ago.beginning_of_day
    @end_date = Time.current.end_of_day

    # Get form sessions with their events for replay
    @sessions = @form.form_sessions
                    .includes(:field_events)
                    .where(started_at: @start_date..@end_date)
                    .where.not(field_events: { id: nil })
                    .order(started_at: :desc)
                    .page(params[:page])
                    .per(20)

    # Session statistics
    @session_stats = calculate_session_statistics

    # Filter options
    @status_filter = params[:status] || "all"
    @sessions = apply_status_filter(@sessions, @status_filter) if @status_filter != "all"
  end

  def show
    @field_events = @session.field_events
                           .includes(:form_field)
                           .order(:created_at)

    # Calculate session metadata
    @session_metadata = {
      duration: calculate_session_duration,
      total_events: @field_events.count,
      error_events: @field_events.where(event_type: "error").count,
      correction_events: @field_events.where(event_type: "correction").count,
      completion_rate: calculate_completion_rate,
      problematic_fields: identify_problematic_fields
    }

    # Prepare replay data for JavaScript
    @replay_data = prepare_replay_data
  end

  private

  def set_form
    @form = Current.account.forms.find(params[:form_id])
  end

  def set_session
    @session = @form.form_sessions.find(params[:id])
  end

  def calculate_session_statistics
    sessions_in_range = @form.form_sessions.where(started_at: @start_date..@end_date)

    {
      total_sessions: sessions_in_range.count,
      completed_sessions: sessions_in_range.where(status: "completed").count,
      abandoned_sessions: sessions_in_range.where(status: "abandoned").count,
      avg_duration: calculate_avg_session_duration(sessions_in_range),
      bounce_rate: calculate_bounce_rate(sessions_in_range)
    }
  end

  def apply_status_filter(sessions, status)
    case status
    when "completed"
      sessions.where(status: "completed")
    when "abandoned"
      sessions.where(status: "abandoned")
    when "active"
      sessions.where(status: "active")
    else
      sessions
    end
  end

  def calculate_session_duration
    return 0 unless @session.ended_at && @session.started_at
    (@session.ended_at - @session.started_at).round(2)
  end

  def calculate_completion_rate
    return 0 if @form.form_fields.count == 0

    interacted_fields = @field_events.select(:form_field_id).distinct.count
    (interacted_fields.to_f / @form.form_fields.count * 100).round(2)
  end

  def identify_problematic_fields
    @field_events.where(event_type: [ "error", "correction" ])
                 .group(:form_field_id)
                 .count
                 .map { |field_id, count| [ @form.form_fields.find(field_id).name, count ] }
                 .sort_by { |_, count| -count }
                 .first(3)
  end

  def calculate_avg_session_duration(sessions)
    durations = sessions.where.not(ended_at: nil)
                       .where.not(started_at: nil)
                       .pluck(:started_at, :ended_at)
                       .map { |start, finish| finish - start }

    return 0 if durations.empty?
    (durations.sum / durations.count).round(2)
  end

  def calculate_bounce_rate(sessions)
    return 0 if sessions.count == 0

    # Sessions with very few interactions (bounce)
    bounced = sessions.joins(:field_events)
                     .group("form_sessions.id")
                     .having("COUNT(field_events.id) <= 2")
                     .count
                     .keys
                     .count

    (bounced.to_f / sessions.count * 100).round(2)
  end

  def prepare_replay_data
    events_data = @field_events.map do |event|
      {
        id: event.id,
        field_id: event.form_field_id,
        field_name: event.form_field.name,
        field_type: event.form_field.field_type,
        event_type: event.event_type,
        timestamp: event.created_at.to_i,
        duration: event.duration,
        value: event.value,
        error_message: event.error_message,
        metadata: event.metadata
      }
    end

    {
      session_id: @session.id,
      form_fields: @form.form_fields.ordered.map do |field|
        {
          id: field.id,
          name: field.name,
          field_type: field.field_type,
          position: field.position,
          required: field.required
        }
      end,
      events: events_data,
      session_start: @session.started_at.to_i,
      session_end: @session.ended_at&.to_i || Time.current.to_i
    }
  end
end
