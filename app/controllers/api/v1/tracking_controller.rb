module Api
  module V1
    class TrackingController < BaseController
      before_action :find_website_by_tracking_id, except: [ :script ]

      # Track form session start
      def session_start
        form = find_or_create_form
        session = create_form_session(form)

        render json: {
          session_id: session.session_id,
          form_id: form.id
        }
      end

      # Track field interactions
      def field_event
        form = @website.forms.find_by(id: params[:form_id])
        session = form&.form_sessions&.find_by(session_id: params[:session_id])

        if form && session
          field = find_or_create_field(form)

          event = session.field_events.create!(
            form_field: field,
            event_type: params[:event_type],
            timestamp: Time.at(params[:timestamp].to_i / 1000),
            duration: params[:duration],
            value: params[:value],
            value_length: params[:value]&.length,
            error_message: params[:error_message]
          )

          render json: { success: true, event_id: event.id }
        else
          render json: { error: "Invalid form or session" }, status: :unprocessable_entity
        end
      end

      # Track form submission
      def submit
        form = @website.forms.find_by(id: params[:form_id])
        session = form&.form_sessions&.find_by(session_id: params[:session_id])

        if form && session
          session.complete!

          submission = form.form_submissions.create!(
            form_session: session,
            submitted_at: Time.current,
            form_data: params[:form_data] || {},
            completion_time: session.calculate_time_spent,
            visitor_id: params[:visitor_id],
            ip_address: request.remote_ip,
            user_agent: request.user_agent
          )

          render json: { success: true, submission_id: submission.id }
        else
          render json: { error: "Invalid form or session" }, status: :unprocessable_entity
        end
      end

      # Track form abandonment
      def abandon
        form = @website.forms.find_by(id: params[:form_id])
        session = form&.form_sessions&.find_by(session_id: params[:session_id])

        if session
          session.abandon!
          render json: { success: true }
        else
          render json: { error: "Invalid session" }, status: :unprocessable_entity
        end
      end

      # Batch event processing
      def batch
        form = @website.forms.find_by(id: params[:form_id]) if params[:form_id]

        events = params[:events] || []
        processed_count = 0
        errors = []

        events.each do |event|
          begin
            processed = case event[:type]
            when "field_focus", "field_blur", "field_change", "field_error"
              process_field_event(event)
              true
            when "form_submit"
              process_form_submission(event)
              true
            when "form_abandon"
              process_form_abandonment(event)
              true
            when "page_view", "scroll_depth", "rage_click"
              # Store these for analytics but don't process now
              store_analytics_event(event)
              true
            else
              false
            end

            if processed
              processed_count += 1
            else
              errors << { event: event[:type], error: "Unknown event type" }
            end
          rescue => e
            errors << { event: event[:type], error: e.message }
          end
        end

        render json: {
          success: true,
          processed: processed_count,
          total: events.length,
          errors: errors
        }
      end

      # Get tracking script
      def script
        @website = Website.find_by(tracking_id: params[:id])

        if @website
          render js: tracking_script_content
        else
          render js: "console.error('FormFlow Pro: Invalid tracking ID');"
        end
      end

      # Handle CORS preflight requests
      def options
        head :ok
      end

      private

      def process_field_event(event)
        form = @website.forms.find_by(id: event[:form_id])
        session = form&.form_sessions&.find_by(session_id: event[:session_id])

        return unless form && session

        field = find_or_create_field(form, event[:field])

        session.field_events.create!(
          form_field: field,
          event_type: event[:type].sub("field_", ""),
          timestamp: Time.at(event[:timestamp].to_i / 1000),
          duration: event[:duration],
          value: event[:value],
          error_message: event[:error]
        )
      end

      def process_form_submission(event)
        form = @website.forms.find_by(id: event[:form_id])
        session = form&.form_sessions&.find_by(session_id: event[:session_id])

        return unless form && session

        session.complete!

        form.form_submissions.create!(
          form_session: session,
          submitted_at: Time.at(event[:timestamp].to_i / 1000),
          completion_time: event[:completionTime],
          visitor_id: event[:visitor_id],
          ip_address: request.remote_ip,
          user_agent: request.user_agent
        )
      end

      def process_form_abandonment(event)
        form = @website.forms.find_by(id: event[:form_id])
        session = form&.form_sessions&.find_by(session_id: event[:session_id])

        return unless session

        session.abandon!
        session.update(
          last_field_interacted: event[:lastField],
          time_spent: event[:timeSpent]
        )
      end

      def store_analytics_event(event)
        # Store in a separate analytics table or send to analytics service
        Rails.logger.info "Analytics Event: #{event.to_json}"
      end

      def find_or_create_field(form, field_name = nil)
        field_name ||= params[:field_name]
        form.form_fields.find_or_create_by!(name: field_name) do |field|
          field.field_type = params[:field_type]
          field.label = params[:field_label]
          field.field_identifier = params[:field_identifier]
          field.required = params[:required] || false
          field.position = params[:position] || 0
        end
      end

      def find_or_create_form
        @website.forms.find_or_create_by!(
          form_identifier: params[:form_identifier]
        ) do |form|
          form.name = params[:form_name] || params[:form_identifier]
          form.url = params[:url]
          form.first_seen_at = Time.current
          form.last_seen_at = Time.current
        end
      end


      def create_form_session(form)
        session = form.form_sessions.create!(
          visitor_id: params[:visitor_id] || generate_visitor_id,
          session_id: SecureRandom.uuid,
          started_at: Time.current,
          status: "in_progress",
          device_type: detect_device_type,
          browser: detect_browser,
          operating_system: detect_os,
          referrer: params[:referrer],
          user_agent: request.user_agent,
          ip_address: request.remote_ip,
          country: detect_country,
          metadata: {
            screen_resolution: params[:screen_resolution],
            viewport_size: params[:viewport_size],
            color_depth: params[:color_depth]
          }
        )

        # Broadcast real-time update
        broadcast_session_start(form, session)

        session
      end

      def broadcast_session_start(form, session)
        ActionCable.server.broadcast(
          "form_analytics_#{form.id}",
          {
            type: "session_started",
            form_id: form.id,
            session_id: session.session_id,
            device_type: session.device_type,
            timestamp: session.started_at.to_i
          }
        )

        # Also broadcast to website channel
        ActionCable.server.broadcast(
          "website_analytics_#{form.website_id}",
          {
            type: "session_started",
            form_id: form.id,
            form_name: form.name,
            timestamp: session.started_at.to_i
          }
        )
      end

      def generate_visitor_id
        "visitor_#{SecureRandom.hex(16)}"
      end

      def detect_device_type
        user_agent = request.user_agent.to_s.downcase
        case user_agent
        when /mobile|android|iphone/
          "mobile"
        when /tablet|ipad/
          "tablet"
        else
          "desktop"
        end
      end

      def detect_browser
        user_agent = request.user_agent.to_s
        case user_agent
        when /Chrome/
          "Chrome"
        when /Safari/
          "Safari"
        when /Firefox/
          "Firefox"
        when /Edge/
          "Edge"
        else
          "Other"
        end
      end

      def detect_os
        user_agent = request.user_agent.to_s
        case user_agent
        when /Windows/
          "Windows"
        when /Mac OS/
          "macOS"
        when /Linux/
          "Linux"
        when /Android/
          "Android"
        when /iOS|iPhone|iPad/
          "iOS"
        else
          "Other"
        end
      end

      def detect_country
        # In production, use a geo-IP service
        "US"
      end

      def tracking_script_content
        <<~JS
          (function() {
            var FormFlowPro = {
              trackingId: '#{@website.tracking_id}',
              apiUrl: '#{request.protocol}#{request.host_with_port}/api/v1',
              visitorId: null,
              sessions: {},
          #{'    '}
              init: function() {
                this.visitorId = this.getVisitorId();
                this.trackForms();
              },
          #{'    '}
              getVisitorId: function() {
                var id = localStorage.getItem('ffp_visitor_id');
                if (!id) {
                  id = 'visitor_' + Math.random().toString(36).substr(2, 16);
                  localStorage.setItem('ffp_visitor_id', id);
                }
                return id;
              },
          #{'    '}
              trackForms: function() {
                var forms = document.querySelectorAll('form');
                forms.forEach(function(form, index) {
                  var formId = form.id || form.name || 'form_' + index;
                  FormFlowPro.trackForm(form, formId);
                });
              },
          #{'    '}
              trackForm: function(form, formId) {
                var self = this;
                var fields = form.querySelectorAll('input, textarea, select');
                var sessionId = null;
                var formDbId = null;
          #{'      '}
                // Start session on first interaction
                var sessionStarted = false;
                fields.forEach(function(field) {
                  ['focus', 'blur', 'change', 'input'].forEach(function(eventType) {
                    field.addEventListener(eventType, function(e) {
                      if (!sessionStarted) {
                        self.startSession(formId, function(data) {
                          sessionId = data.session_id;
                          formDbId = data.form_id;
                          sessionStarted = true;
                        });
                      }
          #{'            '}
                      if (sessionId) {
                        self.trackFieldEvent(formDbId, sessionId, field, eventType);
                      }
                    });
                  });
                });
          #{'      '}
                // Track form submission
                form.addEventListener('submit', function(e) {
                  if (sessionId) {
                    var formData = {};
                    fields.forEach(function(field) {
                      if (field.name) {
                        formData[field.name] = field.value;
                      }
                    });
          #{'          '}
                    self.trackSubmission(formDbId, sessionId, formData);
                  }
                });
              },
          #{'    '}
              startSession: function(formId, callback) {
                this.sendRequest('/tracking/session_start', {
                  tracking_id: this.trackingId,
                  form_identifier: formId,
                  url: window.location.href,
                  referrer: document.referrer,
                  visitor_id: this.visitorId,
                  screen_resolution: window.screen.width + 'x' + window.screen.height,
                  viewport_size: window.innerWidth + 'x' + window.innerHeight
                }, callback);
              },
          #{'    '}
              trackFieldEvent: function(formId, sessionId, field, eventType) {
                this.sendRequest('/tracking/field_event', {
                  tracking_id: this.trackingId,
                  form_id: formId,
                  session_id: sessionId,
                  field_name: field.name || field.id,
                  field_type: field.type,
                  field_label: this.getFieldLabel(field),
                  event_type: eventType,
                  timestamp: Date.now(),
                  value: field.value
                });
              },
          #{'    '}
              trackSubmission: function(formId, sessionId, formData) {
                this.sendRequest('/tracking/submit', {
                  tracking_id: this.trackingId,
                  form_id: formId,
                  session_id: sessionId,
                  form_data: formData,
                  visitor_id: this.visitorId
                });
              },
          #{'    '}
              getFieldLabel: function(field) {
                var label = document.querySelector('label[for="' + field.id + '"]');
                return label ? label.textContent : field.placeholder || field.name;
              },
          #{'    '}
              sendRequest: function(endpoint, data, callback) {
                var xhr = new XMLHttpRequest();
                xhr.open('POST', this.apiUrl + endpoint, true);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.setRequestHeader('X-Tracking-ID', this.trackingId);
          #{'      '}
                xhr.onreadystatechange = function() {
                  if (xhr.readyState === 4 && xhr.status === 200) {
                    if (callback) {
                      callback(JSON.parse(xhr.responseText));
                    }
                  }
                };
          #{'      '}
                xhr.send(JSON.stringify(data));
              }
            };
          #{'  '}
            // Initialize when DOM is ready
            if (document.readyState === 'loading') {
              document.addEventListener('DOMContentLoaded', function() {
                FormFlowPro.init();
              });
            } else {
              FormFlowPro.init();
            }
          })();
        JS
      end
    end
  end
end
