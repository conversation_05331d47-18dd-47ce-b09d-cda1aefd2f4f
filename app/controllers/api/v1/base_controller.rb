module Api
  module V1
    class BaseController < ActionController::API
      # ActionController::API doesn't have verify_authenticity_token
      # so we don't need to skip it

      before_action :set_cors_headers

      private

      def set_cors_headers
        headers["Access-Control-Allow-Origin"] = "*"
        headers["Access-Control-Allow-Methods"] = "POST, GET, OPTIONS"
        headers["Access-Control-Allow-Headers"] = "Content-Type, X-Tracking-ID"
      end

      def find_website_by_tracking_id
        tracking_id = params[:tracking_id] || request.headers["X-Tracking-ID"]
        @website = Website.find_by(tracking_id: tracking_id)

        unless @website&.can_track?
          render json: { error: "Invalid tracking ID or website not verified" }, status: :unauthorized
        end
      end
    end
  end
end
