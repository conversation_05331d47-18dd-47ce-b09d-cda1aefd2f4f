class FormsController < ApplicationController
  before_action :authenticate_user!
  before_action :require_account!
  before_action :set_form

  def show
    @website = @form.website
    @form_fields = @form.form_fields.ordered
    @insights = @form.insights.order(severity: :desc, created_at: :desc).limit(10)

    # Sessions data for chart
    @sessions_by_day = @form.form_sessions
                            .where("started_at > ?", 30.days.ago)
                            .group_by_day(:started_at)
                            .count

    @submissions_by_day = @form.form_submissions
                               .where("submitted_at > ?", 30.days.ago)
                               .group_by_day(:submitted_at)
                               .count

    # Field performance
    @field_performance = @form.field_performance_data

    # Recent sessions
    @recent_sessions = @form.form_sessions
                           .includes(:field_events)
                           .order(started_at: :desc)
                           .limit(20)
  end

  def heatmap
    @website = @form.website
    @date_range = params[:range] || "30"
    @start_date = @date_range.to_i.days.ago.beginning_of_day
    @end_date = Time.current.end_of_day

    # Calculate heatmap data for form fields
    @heatmap_data = calculate_field_heatmap_data

    # Field interaction summary
    @field_summary = calculate_field_interaction_summary

    # Most problematic fields
    @problematic_fields = @form.form_fields
                               .select { |field| field.problematic? }
                               .sort_by { |field| -field.error_rate }
                               .first(5)

    # Interaction patterns by time of day
    @hourly_interactions = calculate_hourly_interactions
  end

  def session_replay
    @website = @form.website
    
    if params[:session_id].present?
      @session = @form.form_sessions.find(params[:session_id])
      @events = @session.field_events.order(:created_at)
    else
      # Get the most recent session for demo
      @session = @form.form_sessions.order(started_at: :desc).first
      @events = @session&.field_events&.order(:created_at) || []
    end
  end

  private

  def set_form
    @form = Current.account.forms.find(params[:id])
  end

  def calculate_field_heatmap_data
    @form.form_fields.includes(:field_events).map do |field|
      events = field.field_events.where(created_at: @start_date..@end_date)

      {
        field_id: field.id,
        name: field.name,
        position: field.position,
        field_type: field.field_type,
        total_interactions: events.count,
        focus_events: events.where(event_type: "focus").count,
        blur_events: events.where(event_type: "blur").count,
        error_events: events.where(event_type: "error").count,
        correction_events: events.where(event_type: "correction").count,
        avg_time_spent: calculate_avg_field_time(events),
        interaction_intensity: calculate_interaction_intensity(events.count),
        abandonment_rate: field.abandonment_rate,
        error_rate: field.error_rate,
        required: field.required
      }
    end
  end

  def calculate_field_interaction_summary
    total_events = FieldEvent.joins(form_field: :form)
                            .where(forms: { id: @form.id })
                            .where(created_at: @start_date..@end_date)
                            .count

    {
      total_interactions: total_events,
      focus_events: FieldEvent.joins(form_field: :form)
                             .where(forms: { id: @form.id })
                             .where(created_at: @start_date..@end_date)
                             .where(event_type: "focus")
                             .count,
      error_events: FieldEvent.joins(form_field: :form)
                             .where(forms: { id: @form.id })
                             .where(created_at: @start_date..@end_date)
                             .where(event_type: "error")
                             .count,
      avg_interactions_per_field: total_events.to_f / [ @form.form_fields.count, 1 ].max
    }
  end

  def calculate_hourly_interactions
    FieldEvent.joins(form_field: :form)
              .where(forms: { id: @form.id })
              .where(created_at: @start_date..@end_date)
              .group_by_hour_of_day(:created_at)
              .count
  end

  def calculate_avg_field_time(events)
    focus_blur_pairs = []
    current_focus = nil

    events.order(:created_at).each do |event|
      if event.event_type == "focus"
        current_focus = event.created_at
      elsif event.event_type == "blur" && current_focus
        focus_blur_pairs << (event.created_at - current_focus)
        current_focus = nil
      end
    end

    return 0 if focus_blur_pairs.empty?
    (focus_blur_pairs.sum / focus_blur_pairs.length).round(2)
  end

  def calculate_interaction_intensity(interaction_count)
    # Normalize interaction count to intensity scale (0-100)
    max_interactions = @form.form_fields.maximum("(SELECT COUNT(*) FROM field_events WHERE form_field_id = form_fields.id)") || 1
    ((interaction_count.to_f / max_interactions) * 100).round(2)
  end
end
