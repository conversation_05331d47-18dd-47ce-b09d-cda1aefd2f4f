class WebsitesController < ApplicationController
  before_action :authenticate_user!
  before_action :require_account!
  before_action :set_website, only: [ :show, :edit, :update, :destroy, :verify ]

  def index
    @websites = Current.account.websites.includes(:forms)
  end

  def show
    @forms = @website.forms.includes(:form_fields)
    @recent_submissions = @website.form_submissions
                                  .includes(:form)
                                  .order(submitted_at: :desc)
                                  .limit(20)

    # Analytics data
    @total_sessions = @website.forms.sum(:total_sessions)
    @total_submissions = @website.forms.sum(:total_submissions)
    @avg_conversion_rate = @website.forms.average(:conversion_rate)&.round(2) || 0
  end

  def new
    @website = Current.account.websites.build
  end

  def create
    @website = Current.account.websites.build(website_params)

    if @website.save
      redirect_to @website, notice: "Website was successfully created. Add the tracking code to your site."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @website.update(website_params)
      redirect_to @website, notice: "Website was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @website.destroy
    redirect_to websites_url, notice: "Website was successfully removed."
  end

  def verify
    @website.verify!
    redirect_to @website, notice: "Website has been verified."
  end

  private

  def set_website
    @website = Current.account.websites.find(params[:id])
  end

  def website_params
    params.require(:website).permit(:domain, :name)
  end
end
