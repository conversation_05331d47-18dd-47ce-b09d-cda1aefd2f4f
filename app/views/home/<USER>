<div data-controller="smooth-scroll">
<!-- Hero Section with Dark Purple Design -->
<section class="min-h-screen relative overflow-hidden bg-gradient-to-b from-black to-purple-950">
  <!-- Background effects with darker overlay -->
  <div class="absolute inset-0 bg-black/50"></div>
  <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-purple-800/20 to-transparent"></div>
  <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-purple-900/20 to-transparent"></div>
  
  <!-- Navigation -->
  <nav class="relative z-10 px-4 sm:px-6 lg:px-8 pt-8" data-controller="mobile-menu">
    <div class="max-w-7xl mx-auto">
      <div class="flex justify-between items-center">
        <!-- Logo -->
        <div class="flex items-center space-x-2">
          <div class="w-10 h-10 bg-gradient-to-r from-blue-400 to-purple-400 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <span class="text-white font-bold text-xl">FormFlow Pro</span>
        </div>
        
        <!-- Desktop Menu -->
        <div class="hidden md:flex items-center space-x-8">
          <a href="#features" class="text-gray-300 hover:text-white transition">Features</a>
          <a href="#how-it-works" class="text-gray-300 hover:text-white transition">How It Works</a>
          <a href="#pricing" class="text-gray-300 hover:text-white transition">Pricing</a>
          <%= link_to "About", about_path, class: "text-gray-300 hover:text-white transition" %>
          <%= link_to "Contact", contact_path, class: "text-gray-300 hover:text-white transition" %>
          <%= link_to "Sign In", new_user_session_path, class: "text-gray-300 hover:text-white transition" %>
          <%= link_to "Start Free Trial", new_user_registration_path, class: "bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-2.5 rounded-full font-semibold hover:shadow-lg transition" %>
        </div>
        
        <!-- Mobile Menu Button -->
        <div class="md:hidden">
          <button data-action="click->mobile-menu#toggle" class="text-white p-2">
            <!-- Hamburger Icon -->
            <svg data-mobile-menu-target="open" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
            <!-- Close Icon -->
            <svg data-mobile-menu-target="close" class="w-6 h-6 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
      
      <!-- Mobile Menu -->
      <div data-mobile-menu-target="menu" class="md:hidden mt-4 py-4 px-2 bg-black/50 backdrop-blur-lg rounded-xl border border-purple-800/30 hidden">
        <div class="flex flex-col space-y-4">
          <a href="#features" data-action="click->mobile-menu#close" class="text-gray-300 hover:text-white transition px-4 py-2 hover:bg-purple-800/20 rounded-lg">Features</a>
          <a href="#how-it-works" data-action="click->mobile-menu#close" class="text-gray-300 hover:text-white transition px-4 py-2 hover:bg-purple-800/20 rounded-lg">How It Works</a>
          <a href="#pricing" data-action="click->mobile-menu#close" class="text-gray-300 hover:text-white transition px-4 py-2 hover:bg-purple-800/20 rounded-lg">Pricing</a>
          <%= link_to "About", about_path, 
              class: "text-gray-300 hover:text-white transition px-4 py-2 hover:bg-purple-800/20 rounded-lg",
              data: { action: "click->mobile-menu#close" } %>
          <%= link_to "Contact", contact_path, 
              class: "text-gray-300 hover:text-white transition px-4 py-2 hover:bg-purple-800/20 rounded-lg",
              data: { action: "click->mobile-menu#close" } %>
          <div class="border-t border-purple-800/30 pt-4 flex flex-col space-y-3">
            <%= link_to "Sign In", new_user_session_path, 
                class: "text-center text-gray-300 hover:text-white transition px-4 py-2 hover:bg-purple-800/20 rounded-lg",
                data: { action: "click->mobile-menu#close" } %>
            <%= link_to "Start Free Trial", new_user_registration_path, 
                class: "text-center bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-lg font-semibold hover:shadow-lg transition",
                data: { action: "click->mobile-menu#close" } %>
          </div>
        </div>
      </div>
    </div>
  </nav>
  
  <!-- Hero Content -->
  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-32">
    <!-- Status Badge -->
    <div class="flex justify-center mb-8">
      <div class="inline-flex items-center space-x-2 bg-green-500/20 backdrop-blur-sm rounded-full px-4 py-2 border border-green-500/30">
        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
        <span class="text-green-300 text-sm font-medium">3 slots available for December</span>
      </div>
    </div>
    
    <!-- Main Headline -->
    <div class="text-center">
      <h1 class="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
        Stop Losing Customers at
        <br />
        <span class="relative">
          <span class="relative z-10 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Your Forms</span>
          <div class="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 to-purple-400 transform skew-x-12"></div>
        </span>
      </h1>
      
      <p class="text-xl md:text-2xl text-gray-300 mb-4 max-w-3xl mx-auto">
        Understand exactly where users abandon your forms and fix it with AI-powered insights
      </p>
      
      <p class="text-gray-400 mb-12 text-lg">
        Trusted by 500+ SaaS Companies and E-commerce Brands
      </p>
      
      <!-- CTA Button -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <%= link_to new_user_registration_path, 
            class: "group relative bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold py-4 px-8 rounded-xl hover:shadow-2xl hover:shadow-purple-500/25 transition-all duration-300 transform hover:-translate-y-0.5" do %>
          <span class="relative z-10">Start Free Trial</span>
        <% end %>
        
        <a href="#demo" class="text-gray-300 font-medium py-4 px-8 hover:text-white transition flex items-center gap-2">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Watch 2-min Demo
        </a>
      </div>
      
      <!-- Scroll Indicator -->
      <a href="#features" class="mt-20 flex flex-col items-center group cursor-pointer">
        <span class="text-gray-400 text-sm mb-2 group-hover:text-white transition">Scroll down</span>
        <svg class="w-6 h-6 text-gray-400 animate-bounce group-hover:text-white transition" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
      </a>
    </div>
  </div>
</section>

<!-- Features Section with Dark Theme -->
<section id="features" class="py-24 bg-gray-900 relative">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <span class="text-purple-400 font-semibold text-sm uppercase tracking-wider">Features</span>
      <h2 class="text-4xl md:text-5xl font-bold text-white mb-4 mt-2">
        Everything You Need to Fix Your Forms
      </h2>
      <p class="text-xl text-gray-400 max-w-3xl mx-auto">
        Powerful analytics and insights that help you understand why users abandon your forms
      </p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- Feature 1 -->
      <div class="group relative bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700 hover:border-purple-500/50 transition-all duration-300">
        <div class="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
        <div class="relative z-10">
          <div class="w-14 h-14 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center mb-6">
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-white mb-3">Real-Time Analytics</h3>
          <p class="text-gray-400 leading-relaxed">
            Track every click, focus, and hesitation as it happens. Get instant alerts when conversion rates drop.
          </p>
        </div>
      </div>
      
      <!-- Feature 2 -->
      <div class="group relative bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700 hover:border-purple-500/50 transition-all duration-300">
        <div class="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
        <div class="relative z-10">
          <div class="w-14 h-14 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center mb-6">
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-white mb-3">Heatmap Visualization</h3>
          <p class="text-gray-400 leading-relaxed">
            See exactly where users click, scroll, and abandon. Visual heatmaps show problem areas instantly.
          </p>
        </div>
      </div>
      
      <!-- Feature 3 -->
      <div class="group relative bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700 hover:border-purple-500/50 transition-all duration-300">
        <div class="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
        <div class="relative z-10">
          <div class="w-14 h-14 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center mb-6">
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-white mb-3">AI-Powered Insights</h3>
          <p class="text-gray-400 leading-relaxed">
            Get actionable recommendations on what to fix first. Our AI analyzes patterns and suggests improvements.
          </p>
        </div>
      </div>
      
      <!-- Feature 4 -->
      <div class="group relative bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700 hover:border-purple-500/50 transition-all duration-300">
        <div class="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
        <div class="relative z-10">
          <div class="w-14 h-14 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center mb-6">
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-white mb-3">Session Replays</h3>
          <p class="text-gray-400 leading-relaxed">
            Watch actual user sessions to understand their frustration. See every mouse movement and form interaction.
          </p>
        </div>
      </div>
      
      <!-- Feature 5 -->
      <div class="group relative bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700 hover:border-purple-500/50 transition-all duration-300">
        <div class="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
        <div class="relative z-10">
          <div class="w-14 h-14 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center mb-6">
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-white mb-3">Field-Level Analytics</h3>
          <p class="text-gray-400 leading-relaxed">
            Track time spent, error rates, and abandonment for each field. Know exactly which fields cause problems.
          </p>
        </div>
      </div>
      
      <!-- Feature 6 -->
      <div class="group relative bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700 hover:border-purple-500/50 transition-all duration-300">
        <div class="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
        <div class="relative z-10">
          <div class="w-14 h-14 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center mb-6">
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-white mb-3">5-Minute Setup</h3>
          <p class="text-gray-400 leading-relaxed">
            One line of JavaScript. Works with React, Vue, Angular, or plain HTML. No developers needed.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- How It Works Section with Dark Theme -->
<section id="how-it-works" class="py-24 bg-black relative">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <span class="text-purple-400 font-semibold text-sm uppercase tracking-wider">How It Works</span>
      <h2 class="text-4xl md:text-5xl font-bold text-white mb-4 mt-2">
        Set Up in Less Than 5 Minutes
      </h2>
      <p class="text-xl text-gray-400 max-w-3xl mx-auto">
        No coding required. Works with any website or framework.
      </p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-12">
      <div class="relative text-center">
        <!-- Connecting Line (hidden on mobile) -->
        <div class="hidden md:block absolute top-12 left-1/2 w-full h-0.5 bg-gradient-to-r from-purple-500 to-transparent"></div>
        
        <div class="relative z-10">
          <div class="w-20 h-20 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-2xl flex items-center justify-center mx-auto mb-6 text-3xl font-bold shadow-2xl shadow-purple-500/25">
            1
          </div>
          <h3 class="text-xl font-bold text-white mb-3">Sign Up & Add Website</h3>
          <p class="text-gray-400">
            Create your account and add your website URL. Takes 30 seconds.
          </p>
        </div>
      </div>
      
      <div class="relative text-center">
        <!-- Connecting Line (hidden on mobile) -->
        <div class="hidden md:block absolute top-12 left-1/2 w-full h-0.5 bg-gradient-to-r from-transparent via-purple-500 to-transparent"></div>
        
        <div class="relative z-10">
          <div class="w-20 h-20 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-2xl flex items-center justify-center mx-auto mb-6 text-3xl font-bold shadow-2xl shadow-purple-500/25">
            2
          </div>
          <h3 class="text-xl font-bold text-white mb-3">Copy & Paste Script</h3>
          <p class="text-gray-400">
            Add one line of JavaScript to your site. Works with any platform.
          </p>
        </div>
      </div>
      
      <div class="relative text-center">
        <div class="relative z-10">
          <div class="w-20 h-20 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-2xl flex items-center justify-center mx-auto mb-6 text-3xl font-bold shadow-2xl shadow-purple-500/25">
            3
          </div>
          <h3 class="text-xl font-bold text-white mb-3">Get Instant Insights</h3>
          <p class="text-gray-400">
            See real-time data flowing in. Start fixing forms immediately.
          </p>
        </div>
      </div>
    </div>
    
    <!-- Code Example -->
    <div class="mt-16 max-w-3xl mx-auto">
      <div class="bg-gray-900 rounded-2xl p-8 border border-gray-800">
        <div class="flex items-center justify-between mb-4">
          <span class="text-gray-400 text-sm">Installation Script</span>
          <button class="text-purple-400 hover:text-purple-300 text-sm font-medium transition">Copy Code</button>
        </div>
        <pre class="text-green-400 text-sm overflow-x-auto"><code>&lt;script src="https://formflowpro.com/track.js" 
        data-site-id="your-unique-id"&gt;&lt;/script&gt;</code></pre>
      </div>
    </div>
  </div>
</section>

<!-- Social Proof Section -->
<section class="py-24 bg-gray-900 border-t border-gray-800">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-white mb-4">
        Trusted by Leading Companies
      </h2>
      <p class="text-xl text-gray-400">
        Join 500+ companies improving their form conversion rates
      </p>
    </div>
    
    <div class="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
      <!-- Placeholder for company logos -->
      <div class="flex items-center justify-center p-8 bg-gray-800/50 rounded-xl">
        <span class="text-gray-500 font-semibold">TechCorp</span>
      </div>
      <div class="flex items-center justify-center p-8 bg-gray-800/50 rounded-xl">
        <span class="text-gray-500 font-semibold">StartupX</span>
      </div>
      <div class="flex items-center justify-center p-8 bg-gray-800/50 rounded-xl">
        <span class="text-gray-500 font-semibold">SaaSify</span>
      </div>
      <div class="flex items-center justify-center p-8 bg-gray-800/50 rounded-xl">
        <span class="text-gray-500 font-semibold">CloudBase</span>
      </div>
    </div>
    
    <!-- Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <div class="text-center">
        <div class="text-5xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent mb-2">
          47%
        </div>
        <p class="text-gray-400">Average increase in form completions</p>
      </div>
      <div class="text-center">
        <div class="text-5xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent mb-2">
          2.3M+
        </div>
        <p class="text-gray-400">Form sessions analyzed daily</p>
      </div>
      <div class="text-center">
        <div class="text-5xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent mb-2">
          500+
        </div>
        <p class="text-gray-400">Happy customers worldwide</p>
      </div>
    </div>
  </div>
</section>

<!-- Pricing Section -->
<section id="pricing" class="py-24 bg-black relative">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <span class="text-purple-400 font-semibold text-sm uppercase tracking-wider">Pricing</span>
      <h2 class="text-4xl md:text-5xl font-bold text-white mb-4 mt-2">
        Simple, Transparent Pricing
      </h2>
      <p class="text-xl text-gray-400 max-w-3xl mx-auto">
        Choose the plan that fits your needs. All plans include a 14-day free trial.
      </p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
      <!-- Starter Plan -->
      <div class="relative bg-gray-900 rounded-2xl p-8 border border-gray-800 hover:border-purple-500/50 transition-all duration-300">
        <div class="mb-8">
          <h3 class="text-2xl font-bold text-white mb-2">Starter</h3>
          <p class="text-gray-400">Perfect for small businesses</p>
        </div>
        
        <div class="mb-8">
          <span class="text-5xl font-bold text-white">$29</span>
          <span class="text-gray-400">/month</span>
        </div>
        
        <ul class="space-y-4 mb-8">
          <li class="flex items-start gap-3">
            <svg class="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-gray-300">Up to 5,000 form submissions/month</span>
          </li>
          <li class="flex items-start gap-3">
            <svg class="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-gray-300">3 websites</span>
          </li>
          <li class="flex items-start gap-3">
            <svg class="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-gray-300">Basic analytics & heatmaps</span>
          </li>
          <li class="flex items-start gap-3">
            <svg class="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-gray-300">Email support</span>
          </li>
        </ul>
        
        <%= link_to "Start Free Trial", new_user_registration_path, 
            class: "block w-full text-center bg-gray-800 text-white py-3 px-6 rounded-lg hover:bg-gray-700 transition font-semibold" %>
      </div>
      
      <!-- Professional Plan (Popular) -->
      <div class="relative bg-gradient-to-b from-purple-900/50 to-gray-900 rounded-2xl p-8 border-2 border-purple-500 transform scale-105">
        <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
          <span class="bg-gradient-to-r from-purple-500 to-blue-500 text-white text-sm font-bold px-4 py-1 rounded-full">MOST POPULAR</span>
        </div>
        
        <div class="mb-8">
          <h3 class="text-2xl font-bold text-white mb-2">Professional</h3>
          <p class="text-gray-400">For growing businesses</p>
        </div>
        
        <div class="mb-8">
          <span class="text-5xl font-bold text-white">$79</span>
          <span class="text-gray-400">/month</span>
        </div>
        
        <ul class="space-y-4 mb-8">
          <li class="flex items-start gap-3">
            <svg class="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-gray-300">Up to 50,000 form submissions/month</span>
          </li>
          <li class="flex items-start gap-3">
            <svg class="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-gray-300">Unlimited websites</span>
          </li>
          <li class="flex items-start gap-3">
            <svg class="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-gray-300">Session recordings</span>
          </li>
          <li class="flex items-start gap-3">
            <svg class="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-gray-300">AI-powered insights</span>
          </li>
          <li class="flex items-start gap-3">
            <svg class="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-gray-300">Priority support</span>
          </li>
        </ul>
        
        <%= link_to "Start Free Trial", new_user_registration_path, 
            class: "block w-full text-center bg-gradient-to-r from-purple-500 to-blue-500 text-white py-3 px-6 rounded-lg hover:shadow-xl hover:shadow-purple-500/25 transition font-semibold" %>
      </div>
      
      <!-- Enterprise Plan -->
      <div class="relative bg-gray-900 rounded-2xl p-8 border border-gray-800 hover:border-purple-500/50 transition-all duration-300">
        <div class="mb-8">
          <h3 class="text-2xl font-bold text-white mb-2">Enterprise</h3>
          <p class="text-gray-400">For large organizations</p>
        </div>
        
        <div class="mb-8">
          <span class="text-5xl font-bold text-white">Custom</span>
        </div>
        
        <ul class="space-y-4 mb-8">
          <li class="flex items-start gap-3">
            <svg class="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-gray-300">Unlimited everything</span>
          </li>
          <li class="flex items-start gap-3">
            <svg class="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-gray-300">Custom integrations</span>
          </li>
          <li class="flex items-start gap-3">
            <svg class="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-gray-300">Dedicated account manager</span>
          </li>
          <li class="flex items-start gap-3">
            <svg class="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-gray-300">SLA guarantee</span>
          </li>
          <li class="flex items-start gap-3">
            <svg class="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-gray-300">24/7 phone support</span>
          </li>
        </ul>
        
        <%= link_to "Contact Sales", "mailto:<EMAIL>", 
            class: "block w-full text-center bg-gray-800 text-white py-3 px-6 rounded-lg hover:bg-gray-700 transition font-semibold" %>
      </div>
    </div>
  </div>
</section>

<!-- Demo Section -->
<section id="demo" class="py-24 bg-gray-900 relative">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <span class="text-purple-400 font-semibold text-sm uppercase tracking-wider">See It In Action</span>
      <h2 class="text-4xl md:text-5xl font-bold text-white mb-4 mt-2">
        Powerful Analytics Dashboard
      </h2>
      <p class="text-xl text-gray-400 max-w-3xl mx-auto">
        Get deep insights into every form interaction with our intuitive dashboard
      </p>
    </div>
    
    <!-- Dashboard Preview -->
    <div class="relative max-w-6xl mx-auto">
      <div class="relative bg-gray-800 rounded-2xl shadow-2xl overflow-hidden border border-gray-700">
        <!-- Browser Window Frame -->
        <div class="bg-gray-900 px-4 py-3 flex items-center gap-2">
          <div class="flex gap-2">
            <div class="w-3 h-3 rounded-full bg-red-500"></div>
            <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
            <div class="w-3 h-3 rounded-full bg-green-500"></div>
          </div>
          <div class="flex-1 mx-4">
            <div class="bg-gray-800 rounded px-3 py-1 text-gray-400 text-sm">
              app.formflowpro.com/dashboard
            </div>
          </div>
        </div>
        
        <!-- Dashboard Content (Placeholder) -->
        <div class="p-8 bg-gradient-to-br from-gray-900 to-gray-800">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-gray-800/50 backdrop-blur rounded-xl p-6 border border-gray-700">
              <p class="text-gray-400 text-sm mb-2">Total Form Views</p>
              <p class="text-3xl font-bold text-white">24,589</p>
              <p class="text-green-400 text-sm mt-2">↑ 12% from last week</p>
            </div>
            <div class="bg-gray-800/50 backdrop-blur rounded-xl p-6 border border-gray-700">
              <p class="text-gray-400 text-sm mb-2">Completion Rate</p>
              <p class="text-3xl font-bold text-white">67.3%</p>
              <p class="text-green-400 text-sm mt-2">↑ 5% from last week</p>
            </div>
            <div class="bg-gray-800/50 backdrop-blur rounded-xl p-6 border border-gray-700">
              <p class="text-gray-400 text-sm mb-2">Avg. Time to Complete</p>
              <p class="text-3xl font-bold text-white">2m 34s</p>
              <p class="text-red-400 text-sm mt-2">↑ 8s from last week</p>
            </div>
          </div>
          
          <!-- Chart Placeholder -->
          <div class="bg-gray-800/50 backdrop-blur rounded-xl p-6 border border-gray-700">
            <p class="text-white font-semibold mb-4">Form Completion Funnel</p>
            <div class="space-y-3">
              <div class="flex items-center gap-4">
                <span class="text-gray-400 text-sm w-20">Started</span>
                <div class="flex-1 bg-gray-700 rounded-full h-8 relative overflow-hidden">
                  <div class="absolute inset-y-0 left-0 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full" style="width: 100%"></div>
                  <span class="absolute inset-0 flex items-center justify-center text-white text-sm font-semibold">100%</span>
                </div>
              </div>
              <div class="flex items-center gap-4">
                <span class="text-gray-400 text-sm w-20">Field 1</span>
                <div class="flex-1 bg-gray-700 rounded-full h-8 relative overflow-hidden">
                  <div class="absolute inset-y-0 left-0 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full" style="width: 85%"></div>
                  <span class="absolute inset-0 flex items-center justify-center text-white text-sm font-semibold">85%</span>
                </div>
              </div>
              <div class="flex items-center gap-4">
                <span class="text-gray-400 text-sm w-20">Field 2</span>
                <div class="flex-1 bg-gray-700 rounded-full h-8 relative overflow-hidden">
                  <div class="absolute inset-y-0 left-0 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full" style="width: 73%"></div>
                  <span class="absolute inset-0 flex items-center justify-center text-white text-sm font-semibold">73%</span>
                </div>
              </div>
              <div class="flex items-center gap-4">
                <span class="text-gray-400 text-sm w-20">Completed</span>
                <div class="flex-1 bg-gray-700 rounded-full h-8 relative overflow-hidden">
                  <div class="absolute inset-y-0 left-0 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full" style="width: 67%"></div>
                  <span class="absolute inset-0 flex items-center justify-center text-white text-sm font-semibold">67%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Floating Feature Badges -->
      <div class="absolute -top-4 -right-4 bg-gradient-to-r from-purple-500 to-blue-500 text-white px-4 py-2 rounded-lg shadow-xl transform rotate-3">
        <span class="font-semibold">Real-Time Data</span>
      </div>
      <div class="absolute -bottom-4 -left-4 bg-gradient-to-r from-green-500 to-teal-500 text-white px-4 py-2 rounded-lg shadow-xl transform -rotate-3">
        <span class="font-semibold">AI Insights</span>
      </div>
    </div>
  </div>
</section>

<!-- Testimonials Section -->
<section class="py-24 bg-black relative">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <span class="text-purple-400 font-semibold text-sm uppercase tracking-wider">Testimonials</span>
      <h2 class="text-4xl md:text-5xl font-bold text-white mb-4 mt-2">
        Loved by Businesses Worldwide
      </h2>
      <p class="text-xl text-gray-400 max-w-3xl mx-auto">
        See what our customers say about FormFlow Pro
      </p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- Testimonial 1 -->
      <div class="bg-gray-900 rounded-2xl p-8 border border-gray-800 hover:border-purple-500/50 transition-all duration-300">
        <div class="flex mb-4">
          <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
          <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
          <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
          <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
          <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
        </div>
        <p class="text-gray-300 mb-6 italic">
          "FormFlow Pro helped us increase our form completion rate by 52% in just 2 months. The insights are invaluable!"
        </p>
        <div class="flex items-center gap-4">
          <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full"></div>
          <div>
            <p class="text-white font-semibold">Sarah Johnson</p>
            <p class="text-gray-400 text-sm">CEO at TechStartup</p>
          </div>
        </div>
      </div>
      
      <!-- Testimonial 2 -->
      <div class="bg-gray-900 rounded-2xl p-8 border border-gray-800 hover:border-purple-500/50 transition-all duration-300">
        <div class="flex mb-4">
          <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
          <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
          <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
          <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
          <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
        </div>
        <p class="text-gray-300 mb-6 italic">
          "The session recordings feature is a game-changer. We can see exactly where users struggle and fix it immediately."
        </p>
        <div class="flex items-center gap-4">
          <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-full"></div>
          <div>
            <p class="text-white font-semibold">Michael Chen</p>
            <p class="text-gray-400 text-sm">Product Manager at SaaSCo</p>
          </div>
        </div>
      </div>
      
      <!-- Testimonial 3 -->
      <div class="bg-gray-900 rounded-2xl p-8 border border-gray-800 hover:border-purple-500/50 transition-all duration-300">
        <div class="flex mb-4">
          <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
          <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
          <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
          <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
          <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
        </div>
        <p class="text-gray-300 mb-6 italic">
          "Best investment we've made this year. ROI within the first month. The AI insights saved us hours of analysis."
        </p>
        <div class="flex items-center gap-4">
          <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-full"></div>
          <div>
            <p class="text-white font-semibold">Emily Rodriguez</p>
            <p class="text-gray-400 text-sm">Marketing Director at EcomBrand</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-24 bg-gray-900 relative">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <span class="text-purple-400 font-semibold text-sm uppercase tracking-wider">FAQ</span>
      <h2 class="text-4xl md:text-5xl font-bold text-white mb-4 mt-2">
        Frequently Asked Questions
      </h2>
      <p class="text-xl text-gray-400">
        Everything you need to know about FormFlow Pro
      </p>
    </div>
    
    <div class="space-y-6">
      <!-- FAQ Item 1 -->
      <div class="bg-gray-800/50 backdrop-blur rounded-xl p-6 border border-gray-700">
        <h3 class="text-lg font-semibold text-white mb-3">How does FormFlow Pro work?</h3>
        <p class="text-gray-400">
          FormFlow Pro works by adding a simple JavaScript snippet to your website. This script automatically detects and tracks all form interactions, sending the data to our secure servers where it's processed and analyzed in real-time. You can then view insights and recommendations in your dashboard.
        </p>
      </div>
      
      <!-- FAQ Item 2 -->
      <div class="bg-gray-800/50 backdrop-blur rounded-xl p-6 border border-gray-700">
        <h3 class="text-lg font-semibold text-white mb-3">Does it work with my website platform?</h3>
        <p class="text-gray-400">
          Yes! FormFlow Pro works with any website or platform including WordPress, Shopify, Webflow, React, Vue, Angular, and plain HTML. If you can add a JavaScript snippet to your site, FormFlow Pro will work.
        </p>
      </div>
      
      <!-- FAQ Item 3 -->
      <div class="bg-gray-800/50 backdrop-blur rounded-xl p-6 border border-gray-700">
        <h3 class="text-lg font-semibold text-white mb-3">Is my data secure?</h3>
        <p class="text-gray-400">
          Absolutely. We use bank-level encryption for all data transmission and storage. We're GDPR and CCPA compliant, and we never store personally identifiable information (PII). All data is encrypted at rest and in transit using industry-standard protocols.
        </p>
      </div>
      
      <!-- FAQ Item 4 -->
      <div class="bg-gray-800/50 backdrop-blur rounded-xl p-6 border border-gray-700">
        <h3 class="text-lg font-semibold text-white mb-3">Can I try it before buying?</h3>
        <p class="text-gray-400">
          Yes! All plans come with a 14-day free trial. No credit card required. You'll have full access to all features during the trial period, and you can cancel anytime if you're not satisfied.
        </p>
      </div>
      
      <!-- FAQ Item 5 -->
      <div class="bg-gray-800/50 backdrop-blur rounded-xl p-6 border border-gray-700">
        <h3 class="text-lg font-semibold text-white mb-3">How long does setup take?</h3>
        <p class="text-gray-400">
          Setup takes less than 5 minutes. Simply sign up, add your website, copy the tracking code, and paste it into your website's header. You'll start seeing data flowing in immediately.
        </p>
      </div>
      
      <!-- FAQ Item 6 -->
      <div class="bg-gray-800/50 backdrop-blur rounded-xl p-6 border border-gray-700">
        <h3 class="text-lg font-semibold text-white mb-3">What kind of support do you offer?</h3>
        <p class="text-gray-400">
          We offer email support for all plans, with priority support for Professional plans and 24/7 phone support for Enterprise customers. We also have comprehensive documentation, video tutorials, and a knowledge base to help you get the most out of FormFlow Pro.
        </p>
      </div>
    </div>
  </div>
</section>

<!-- Final CTA Section -->
<section class="py-24 bg-gradient-to-t from-purple-950 to-black relative overflow-hidden">
  <!-- Background effects with darker purple tint -->
  <div class="absolute inset-0 bg-black/50"></div>
  <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-purple-800/10 to-transparent"></div>
  
  <div class="relative z-10 max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
    <h2 class="text-4xl md:text-6xl font-bold text-white mb-6">
      Stop Guessing. Start Knowing.
    </h2>
    <p class="text-xl md:text-2xl mb-12 text-gray-300">
      Get the insights you need to fix your forms and boost conversions
    </p>
    
    <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
      <%= link_to new_user_registration_path, 
          class: "group relative bg-white text-purple-900 font-bold py-5 px-12 rounded-xl hover:shadow-2xl hover:shadow-white/25 transition-all duration-300 transform hover:-translate-y-0.5 text-lg" do %>
        <span class="relative z-10">Start Free 14-Day Trial</span>
      <% end %>
    </div>
    
    <div class="flex flex-col sm:flex-row gap-6 justify-center text-gray-400 text-sm">
      <div class="flex items-center justify-center gap-2">
        <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
        <span>No credit card required</span>
      </div>
      <div class="flex items-center justify-center gap-2">
        <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
        <span>5-minute setup</span>
      </div>
      <div class="flex items-center justify-center gap-2">
        <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
        <span>Cancel anytime</span>
      </div>
    </div>
  </div>
</section>

<!-- Footer -->
<footer class="bg-black border-t border-gray-800">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
      <!-- Company Info -->
      <div class="col-span-1 md:col-span-2">
        <div class="flex items-center space-x-2 mb-4">
          <div class="w-10 h-10 bg-gradient-to-r from-blue-400 to-purple-400 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <span class="text-white font-bold text-xl">FormFlow Pro</span>
        </div>
        <p class="text-gray-400 mb-4 max-w-md">
          The intelligent form analytics platform that helps you understand and optimize your forms for maximum conversions.
        </p>
        <div class="flex space-x-4">
          <a href="https://facebook.com/formflowpro" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
            </svg>
          </a>
          <a href="https://twitter.com/formflowpro" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
            </svg>
          </a>
          <a href="https://linkedin.com/company/formflowpro" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
            </svg>
          </a>
        </div>
      </div>
      
      <!-- Quick Links -->
      <div>
        <h3 class="text-white font-semibold mb-4">Product</h3>
        <ul class="space-y-2">
          <li><a href="#features" class="text-gray-400 hover:text-white transition">Features</a></li>
          <li><a href="#pricing" class="text-gray-400 hover:text-white transition">Pricing</a></li>
          <li><a href="#demo" class="text-gray-400 hover:text-white transition">Demo</a></li>
          <li><%= link_to "API Docs", api_documentation_path, class: "text-gray-400 hover:text-white transition" %></li>
        </ul>
      </div>
      
      <!-- Company -->
      <div>
        <h3 class="text-white font-semibold mb-4">Company</h3>
        <ul class="space-y-2">
          <li><%= link_to "About", about_path, class: "text-gray-400 hover:text-white transition" %></li>
          <li><%= link_to "Contact", contact_path, class: "text-gray-400 hover:text-white transition" %></li>
          <li><a href="https://blog.formflowpro.com" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition">Blog</a></li>
          <li><a href="https://careers.formflowpro.com" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition">Careers</a></li>
        </ul>
      </div>
      
      <!-- Support -->
      <div>
        <h3 class="text-white font-semibold mb-4">Support</h3>
        <ul class="space-y-2">
          <li><a href="https://help.formflowpro.com" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition">Help Center</a></li>
          <li><a href="https://status.formflowpro.com" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition">Status</a></li>
          <li><a href="/terms" class="text-gray-400 hover:text-white transition">Terms of Service</a></li>
          <li><a href="/privacy" class="text-gray-400 hover:text-white transition">Privacy Policy</a></li>
        </ul>
      </div>
    </div>
    
    <div class="mt-12 pt-8 border-t border-gray-800 text-center">
      <p class="text-gray-400">
        © 2024 FormFlow Pro. All rights reserved.
      </p>
    </div>
  </div>
</footer>
</div> <!-- End of smooth-scroll controller -->
