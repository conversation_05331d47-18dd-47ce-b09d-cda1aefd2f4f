<!-- Navigation -->
<nav class="relative z-10 px-4 sm:px-6 lg:px-8 pt-8" data-controller="mobile-menu">
  <div class="max-w-7xl mx-auto">
    <div class="flex justify-between items-center">
      <!-- Logo -->
      <div class="flex items-center space-x-2">
        <%= link_to root_path, class: "flex items-center space-x-2" do %>
          <div class="w-10 h-10 bg-gradient-to-r from-blue-400 to-purple-400 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <span class="text-white font-bold text-xl">FormFlow Pro</span>
        <% end %>
      </div>
      
      <!-- Desktop Menu -->
      <div class="hidden md:flex items-center space-x-8">
        <%= link_to "Features", root_path(anchor: "features"), class: "text-gray-300 hover:text-white transition" %>
        <%= link_to "How It Works", root_path(anchor: "how-it-works"), class: "text-gray-300 hover:text-white transition" %>
        <%= link_to "Pricing", root_path(anchor: "pricing"), class: "text-gray-300 hover:text-white transition" %>
        <%= link_to "About", about_path, class: "text-gray-300 hover:text-white transition" %>
        <%= link_to "Contact", contact_path, class: "text-gray-300 hover:text-white transition" %>
        <%= link_to "Sign In", new_user_session_path, class: "text-gray-300 hover:text-white transition" %>
        <%= link_to "Start Free Trial", new_user_registration_path, class: "bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-2.5 rounded-full font-semibold hover:shadow-lg transition" %>
      </div>
      
      <!-- Mobile Menu Button -->
      <div class="md:hidden">
        <button data-action="click->mobile-menu#toggle" class="text-white p-2">
          <!-- Hamburger Icon -->
          <svg data-mobile-menu-target="open" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
          <!-- Close Icon -->
          <svg data-mobile-menu-target="close" class="w-6 h-6 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>
    
    <!-- Mobile Menu -->
    <div data-mobile-menu-target="menu" class="md:hidden mt-4 py-4 px-2 bg-black/50 backdrop-blur-lg rounded-xl border border-purple-800/30 hidden">
      <div class="flex flex-col space-y-4">
        <%= link_to "Features", root_path(anchor: "features"), 
            class: "text-gray-300 hover:text-white transition px-4 py-2 hover:bg-purple-800/20 rounded-lg",
            data: { action: "click->mobile-menu#close" } %>
        <%= link_to "How It Works", root_path(anchor: "how-it-works"), 
            class: "text-gray-300 hover:text-white transition px-4 py-2 hover:bg-purple-800/20 rounded-lg",
            data: { action: "click->mobile-menu#close" } %>
        <%= link_to "Pricing", root_path(anchor: "pricing"), 
            class: "text-gray-300 hover:text-white transition px-4 py-2 hover:bg-purple-800/20 rounded-lg",
            data: { action: "click->mobile-menu#close" } %>
        <%= link_to "About", about_path, 
            class: "text-gray-300 hover:text-white transition px-4 py-2 hover:bg-purple-800/20 rounded-lg",
            data: { action: "click->mobile-menu#close" } %>
        <%= link_to "Contact", contact_path, 
            class: "text-gray-300 hover:text-white transition px-4 py-2 hover:bg-purple-800/20 rounded-lg",
            data: { action: "click->mobile-menu#close" } %>
        <div class="border-t border-purple-800/30 pt-4 flex flex-col space-y-3">
          <%= link_to "Sign In", new_user_session_path, 
              class: "text-center text-gray-300 hover:text-white transition px-4 py-2 hover:bg-purple-800/20 rounded-lg",
              data: { action: "click->mobile-menu#close" } %>
          <%= link_to "Start Free Trial", new_user_registration_path, 
              class: "text-center bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-3 rounded-lg font-semibold hover:shadow-lg transition",
              data: { action: "click->mobile-menu#close" } %>
        </div>
      </div>
    </div>
  </div>
</nav>