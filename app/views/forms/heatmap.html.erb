<!-- Form Heatmap Header -->
<header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-8 py-4">
  <div class="flex items-center justify-between">
    <div>
      <nav class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 mb-2">
        <%= link_to "Dashboard", dashboard_path, class: "hover:text-gray-700 dark:hover:text-gray-200" %> / 
        <%= link_to @website.name, website_path(@website), class: "hover:text-gray-700 dark:hover:text-gray-200" %> / 
        <%= link_to @form.name, form_path(@form), class: "hover:text-gray-700 dark:hover:text-gray-200" %> /
        <span class="text-gray-900 dark:text-white">Heatmap</span>
      </nav>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white"><%= @form.name %> Field Heatmap</h1>
      <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Visual analysis of form field interactions and user behavior patterns</p>
    </div>
    
    <!-- Time Range Filter -->
    <div class="flex items-center gap-4">
      <%= form_with url: heatmap_form_path(@form), method: :get, local: true, class: "flex items-center gap-3" do |f| %>
        <%= f.select :range, options_for_select([
          ['Last 7 days', '7'],
          ['Last 30 days', '30'],
          ['Last 90 days', '90']
        ], @date_range), {}, { 
          class: "px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-violet-500",
          onchange: "this.form.submit();"
        } %>
      <% end %>
      
      <div class="flex items-center gap-2">
        <%= link_to form_path(@form), class: "px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors" do %>
          <svg class="w-4 h-4 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 15l-3-3m0 0l3-3m-3 3h8M3 12a9 9 0 1118 0 9 9 0 01-18 0z"></path>
          </svg>
          Back to Form
        <% end %>
      </div>
    </div>
  </div>
</header>

<!-- Heatmap Content -->
<div class="flex-1 overflow-auto bg-gray-50 dark:bg-gray-900">
  <div class="p-8">
    
    <!-- Field Interaction Summary -->
    <div class="grid grid-cols-4 gap-6 mb-8">
      <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Interactions</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= number_with_delimiter(@field_summary[:total_interactions]) %></p>
          </div>
          <div class="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2-5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.122 2.122"></path>
            </svg>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Focus Events</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= number_with_delimiter(@field_summary[:focus_events]) %></p>
          </div>
          <div class="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Error Events</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= number_with_delimiter(@field_summary[:error_events]) %></p>
          </div>
          <div class="p-3 bg-red-100 dark:bg-red-900/30 rounded-xl">
            <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Avg per Field</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= @field_summary[:avg_interactions_per_field].round(1) %></p>
          </div>
          <div class="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-xl">
            <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-3 gap-8">
      <!-- Field Heatmap Visualization -->
      <div class="col-span-2">
        <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Form Field Heatmap</h2>
            <div class="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
              <div class="flex items-center gap-2">
                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                <span>High Activity</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <span>Medium Activity</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                <span>Low Activity</span>
              </div>
            </div>
          </div>
          
          <!-- Form Field Visualization -->
          <div class="space-y-4">
            <% @heatmap_data.sort_by { |field| field[:position] || 0 }.each do |field| %>
              <% 
                intensity = field[:interaction_intensity]
                color_class = case
                when intensity >= 70
                  "bg-red-500"
                when intensity >= 40
                  "bg-yellow-500"  
                else
                  "bg-green-500"
                end
                
                width_percentage = [(intensity / 100.0 * 100).round, 5].max
              %>
              
              <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center gap-3">
                    <h3 class="font-medium text-gray-900 dark:text-white"><%= field[:name] %></h3>
                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                      <%= field[:field_type] %>
                    </span>
                    <% if field[:required] %>
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400">
                        Required
                      </span>
                    <% end %>
                  </div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">
                    <%= number_with_delimiter(field[:total_interactions]) %> interactions
                  </div>
                </div>
                
                <!-- Intensity Bar -->
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-3">
                  <div class="<%= color_class %> h-3 rounded-full transition-all duration-500 opacity-75" 
                       style="width: <%= width_percentage %>%"></div>
                </div>
                
                <!-- Field Metrics -->
                <div class="grid grid-cols-4 gap-4 text-sm">
                  <div>
                    <span class="text-gray-500 dark:text-gray-400">Focus Events:</span>
                    <span class="font-medium text-gray-900 dark:text-white ml-1"><%= number_with_delimiter(field[:focus_events]) %></span>
                  </div>
                  <div>
                    <span class="text-gray-500 dark:text-gray-400">Errors:</span>
                    <span class="font-medium text-red-600 dark:text-red-400 ml-1"><%= number_with_delimiter(field[:error_events]) %></span>
                  </div>
                  <div>
                    <span class="text-gray-500 dark:text-gray-400">Avg Time:</span>
                    <span class="font-medium text-gray-900 dark:text-white ml-1"><%= field[:avg_time_spent].round(1) %>s</span>
                  </div>
                  <div>
                    <span class="text-gray-500 dark:text-gray-400">Abandonment:</span>
                    <span class="font-medium text-yellow-600 dark:text-yellow-400 ml-1"><%= field[:abandonment_rate].round(1) %>%</span>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
      
      <!-- Sidebar Analytics -->
      <div class="space-y-6">
        <!-- Most Problematic Fields -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Problematic Fields</h2>
          <% if @problematic_fields.any? %>
            <div class="space-y-3">
              <% @problematic_fields.each do |field| %>
                <div class="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                  <div>
                    <p class="font-medium text-gray-900 dark:text-white text-sm"><%= field.name %></p>
                    <p class="text-xs text-gray-500 dark:text-gray-400"><%= field.field_type %></p>
                  </div>
                  <div class="text-right">
                    <p class="text-sm font-medium text-red-600 dark:text-red-400"><%= field.error_rate.round(1) %>% errors</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400"><%= field.abandonment_rate.round(1) %>% abandon</p>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <p class="text-sm text-gray-500 dark:text-gray-400">No problematic fields detected</p>
          <% end %>
        </div>
        
        <!-- Hourly Interaction Patterns -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Interaction by Hour</h2>
          <div class="space-y-2">
            <% 24.times do |hour| %>
              <% 
                count = @hourly_interactions[hour] || 0
                max_count = @hourly_interactions.values.max || 1
                percentage = ((count.to_f / max_count) * 100).round
              %>
              <div class="flex items-center gap-3">
                <div class="text-xs font-medium text-gray-500 dark:text-gray-400 w-8">
                  <%= sprintf('%02d:00', hour) %>
                </div>
                <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div class="bg-violet-500 h-2 rounded-full transition-all duration-300" 
                       style="width: <%= percentage %>%"></div>
                </div>
                <div class="text-xs font-medium text-gray-900 dark:text-white w-8">
                  <%= count %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
        
        <!-- Field Type Distribution -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Field Types</h2>
          <div class="space-y-2">
            <% field_types = @heatmap_data.group_by { |f| f[:field_type] }.transform_values(&:count) %>
            <% field_types.each do |type, count| %>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400"><%= type.humanize %></span>
                <span class="text-sm font-medium text-gray-900 dark:text-white"><%= count %></span>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>