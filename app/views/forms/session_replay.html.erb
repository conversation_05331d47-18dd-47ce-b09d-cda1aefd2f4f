<% content_for :title, "Session Replay - FormFlow Pro" %>

<div class="min-h-screen bg-gray-950">
  <!-- Header -->
  <div class="bg-black border-b border-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <%= link_to form_path(@form), class: "text-gray-400 hover:text-white transition-colors" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          <% end %>
          <div>
            <h1 class="text-2xl font-bold text-white">Session Replay</h1>
            <p class="text-gray-400 text-sm mt-1">
              Session #<%= params[:session_id] || 'Live' %> • <%= @form.name || "Form #{@form.form_identifier}" %>
            </p>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <select class="bg-gray-800 text-white text-sm rounded-lg px-4 py-2 border border-gray-700">
            <option>Select Session...</option>
            <option selected>Session #abc123 - 2 minutes ago</option>
            <option>Session #def456 - 15 minutes ago</option>
            <option>Session #ghi789 - 1 hour ago</option>
            <option>Session #jkl012 - 3 hours ago</option>
          </select>
          <button class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            <span>Export</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="flex h-[calc(100vh-73px)]">
    <!-- Left Panel - Form Replay -->
    <div class="flex-1 bg-gray-900 p-6 overflow-auto">
      <div class="max-w-2xl mx-auto">
        <!-- Session Info Bar -->
        <div class="bg-gray-800 rounded-lg p-4 mb-6 flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span class="text-sm text-gray-300">Duration: <span class="text-white font-medium">3m 42s</span></span>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <span class="text-sm text-gray-300">Device: <span class="text-white font-medium">Desktop Chrome</span></span>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span class="text-sm text-gray-300">Location: <span class="text-white font-medium">New York, US</span></span>
            </div>
          </div>
          <span class="text-xs text-orange-400 bg-orange-500/20 px-3 py-1 rounded">Abandoned at 75%</span>
        </div>

        <!-- Form Visualization -->
        <div class="bg-white rounded-lg p-8" id="formReplay">
          <h2 class="text-2xl font-bold text-gray-900 mb-6">Contact Form</h2>
          
          <div class="space-y-6">
            <!-- Name Field -->
            <div class="form-field" data-field="name">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Full Name <span class="text-red-500">*</span>
              </label>
              <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" value="John Doe" readonly>
              <div class="mt-1 flex items-center space-x-2 text-xs">
                <span class="text-green-600">✓ Completed in 4s</span>
              </div>
            </div>

            <!-- Email Field -->
            <div class="form-field relative" data-field="email">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Email Address <span class="text-red-500">*</span>
              </label>
              <input type="email" class="w-full px-4 py-2 border border-red-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" value="john.doe@" readonly>
              <div class="absolute right-2 top-9 text-red-500">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div class="mt-1 flex items-center space-x-2 text-xs">
                <span class="text-red-600">⚠ 3 corrections made • 12s spent</span>
              </div>
            </div>

            <!-- Phone Field -->
            <div class="form-field" data-field="phone">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Phone Number
              </label>
              <input type="tel" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" value="(*************" readonly>
              <div class="mt-1 flex items-center space-x-2 text-xs">
                <span class="text-green-600">✓ Completed in 6s</span>
              </div>
            </div>

            <!-- Company Field -->
            <div class="form-field animate-pulse" data-field="company">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Company Name
              </label>
              <input type="text" class="w-full px-4 py-2 border-2 border-purple-500 rounded-lg bg-purple-50" placeholder="Currently focused..." readonly>
              <div class="mt-1 flex items-center space-x-2 text-xs">
                <span class="text-purple-600">● Currently active • 8s elapsed</span>
              </div>
            </div>

            <!-- Message Field -->
            <div class="form-field opacity-50" data-field="message">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Message <span class="text-red-500">*</span>
              </label>
              <textarea class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" rows="4" readonly disabled></textarea>
              <div class="mt-1 flex items-center space-x-2 text-xs">
                <span class="text-gray-400">Not reached</span>
              </div>
            </div>

            <!-- Submit Button -->
            <div class="opacity-50">
              <button class="w-full py-3 bg-purple-600 text-white rounded-lg font-medium" disabled>
                Submit Form
              </button>
            </div>
          </div>
        </div>

        <!-- Playback Controls -->
        <div class="bg-gray-800 rounded-lg p-4 mt-6">
          <div class="flex items-center space-x-4">
            <!-- Play/Pause Button -->
            <button class="p-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors" id="playPauseBtn">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </button>

            <!-- Timeline -->
            <div class="flex-1">
              <div class="relative">
                <div class="w-full bg-gray-700 rounded-full h-2">
                  <div class="bg-purple-600 h-2 rounded-full relative" style="width: 45%">
                    <div class="absolute right-0 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-white rounded-full border-2 border-purple-600"></div>
                  </div>
                </div>
                <!-- Event Markers -->
                <div class="absolute top-0 w-full h-2">
                  <div class="absolute bg-green-500 w-1 h-2" style="left: 10%"></div>
                  <div class="absolute bg-red-500 w-1 h-2" style="left: 25%"></div>
                  <div class="absolute bg-red-500 w-1 h-2" style="left: 30%"></div>
                  <div class="absolute bg-green-500 w-1 h-2" style="left: 40%"></div>
                  <div class="absolute bg-yellow-500 w-1 h-2" style="left: 45%"></div>
                </div>
              </div>
              <div class="flex justify-between mt-2 text-xs text-gray-400">
                <span>0:00</span>
                <span class="text-white">1:40</span>
                <span>3:42</span>
              </div>
            </div>

            <!-- Speed Control -->
            <select class="bg-gray-700 text-white text-sm rounded px-3 py-1 border border-gray-600">
              <option>0.5x</option>
              <option selected>1x</option>
              <option>2x</option>
              <option>4x</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Panel - Events Timeline -->
    <div class="w-96 bg-black border-l border-gray-800 overflow-auto">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-white mb-4">Event Timeline</h3>
        
        <!-- Event Filters -->
        <div class="flex flex-wrap gap-2 mb-6">
          <button class="px-3 py-1 bg-purple-600 text-white text-xs rounded-lg">All Events</button>
          <button class="px-3 py-1 bg-gray-800 text-gray-400 text-xs rounded-lg hover:bg-gray-700">Interactions</button>
          <button class="px-3 py-1 bg-gray-800 text-gray-400 text-xs rounded-lg hover:bg-gray-700">Errors</button>
          <button class="px-3 py-1 bg-gray-800 text-gray-400 text-xs rounded-lg hover:bg-gray-700">Navigation</button>
        </div>

        <!-- Timeline Events -->
        <div class="space-y-3">
          <!-- Session Start -->
          <div class="flex items-start space-x-3">
            <div class="flex-shrink-0 w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-sm text-white">Session Started</p>
              <p class="text-xs text-gray-400">Form loaded • 0:00</p>
            </div>
          </div>

          <!-- Field Focus -->
          <div class="flex items-start space-x-3">
            <div class="flex-shrink-0 w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-sm text-white">Focused: Full Name</p>
              <p class="text-xs text-gray-400">Started typing • 0:02</p>
            </div>
          </div>

          <!-- Field Complete -->
          <div class="flex items-start space-x-3">
            <div class="flex-shrink-0 w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-sm text-white">Completed: Full Name</p>
              <p class="text-xs text-gray-400">Value: "John Doe" • 0:06</p>
            </div>
          </div>

          <!-- Field Error -->
          <div class="flex items-start space-x-3 bg-red-500/10 rounded-lg p-2">
            <div class="flex-shrink-0 w-8 h-8 bg-red-500/20 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-sm text-white">Validation Error: Email</p>
              <p class="text-xs text-gray-400">Invalid format • 0:15</p>
              <p class="text-xs text-red-400 mt-1">Attempted: "john.doe@"</p>
            </div>
          </div>

          <!-- Field Correction -->
          <div class="flex items-start space-x-3">
            <div class="flex-shrink-0 w-8 h-8 bg-yellow-500/20 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-sm text-white">Field Corrected: Email</p>
              <p class="text-xs text-gray-400">3 attempts • 0:18</p>
            </div>
          </div>

          <!-- Hesitation -->
          <div class="flex items-start space-x-3">
            <div class="flex-shrink-0 w-8 h-8 bg-orange-500/20 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-sm text-white">Hesitation Detected</p>
              <p class="text-xs text-gray-400">Company field • 8s pause • 1:40</p>
            </div>
          </div>

          <!-- Abandonment -->
          <div class="flex items-start space-x-3 bg-orange-500/10 rounded-lg p-2">
            <div class="flex-shrink-0 w-8 h-8 bg-orange-500/20 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-sm text-white">Session Abandoned</p>
              <p class="text-xs text-gray-400">User left at Company field • 3:42</p>
              <p class="text-xs text-orange-400 mt-1">75% completed</p>
            </div>
          </div>
        </div>

        <!-- Session Summary -->
        <div class="mt-6 p-4 bg-gray-900 rounded-lg border border-gray-800">
          <h4 class="text-sm font-medium text-white mb-3">Session Summary</h4>
          <div class="space-y-2">
            <div class="flex justify-between text-xs">
              <span class="text-gray-400">Total Time</span>
              <span class="text-white">3m 42s</span>
            </div>
            <div class="flex justify-between text-xs">
              <span class="text-gray-400">Fields Completed</span>
              <span class="text-white">3 / 5</span>
            </div>
            <div class="flex justify-between text-xs">
              <span class="text-gray-400">Errors Encountered</span>
              <span class="text-red-400">2</span>
            </div>
            <div class="flex justify-between text-xs">
              <span class="text-gray-400">Corrections Made</span>
              <span class="text-yellow-400">3</span>
            </div>
            <div class="flex justify-between text-xs">
              <span class="text-gray-400">Hesitations</span>
              <span class="text-orange-400">1</span>
            </div>
          </div>
        </div>

        <!-- AI Insights -->
        <div class="mt-6 p-4 bg-purple-900/20 rounded-lg border border-purple-800">
          <h4 class="text-sm font-medium text-white mb-3 flex items-center">
            <svg class="w-4 h-4 mr-2 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
            AI Insights
          </h4>
          <ul class="space-y-2 text-xs">
            <li class="text-gray-300">• Email field validation too strict - 40% of users encounter errors</li>
            <li class="text-gray-300">• Company field lacks helper text - causes hesitation</li>
            <li class="text-gray-300">• Consider making Company field optional</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Simple playback control
  let isPlaying = false;
  const playPauseBtn = document.getElementById('playPauseBtn');
  
  playPauseBtn.addEventListener('click', function() {
    isPlaying = !isPlaying;
    if (isPlaying) {
      playPauseBtn.innerHTML = `
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      `;
      // Start playback animation
    } else {
      playPauseBtn.innerHTML = `
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      `;
      // Pause playback
    }
  });
</script>