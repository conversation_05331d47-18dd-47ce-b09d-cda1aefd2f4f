<% content_for :title, "#{@form.name || 'Form'} Analytics - FormFlow Pro" %>

<div class="min-h-screen bg-gray-950">
  <!-- Header -->
  <div class="bg-black border-b border-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <%= link_to website_path(@website), class: "text-gray-400 hover:text-white transition-colors" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          <% end %>
          <div>
            <h1 class="text-2xl font-bold text-white"><%= @form.name || "Form #{@form.form_identifier}" %></h1>
            <p class="text-gray-400 text-sm mt-1"><%= @form.url %></p>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <%= link_to session_replay_form_path(@form), class: "px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Session Replay</span>
          <% end %>
          <%= link_to heatmap_form_path(@form), class: "px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center space-x-2" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <span>Heatmap</span>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Key Metrics -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
      <!-- Conversion Rate -->
      <div class="bg-gray-900 rounded-xl p-6 border border-gray-800">
        <div class="flex items-center justify-between mb-2">
          <span class="text-gray-400 text-sm">Conversion Rate</span>
          <div class="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        </div>
        <div class="text-2xl font-bold text-white"><%= @form.conversion_rate %>%</div>
        <div class="text-xs text-gray-500 mt-1">
          <%= @form.total_submissions %> / <%= @form.total_sessions %> sessions
        </div>
      </div>

      <!-- Abandonment Rate -->
      <div class="bg-gray-900 rounded-xl p-6 border border-gray-800">
        <div class="flex items-center justify-between mb-2">
          <span class="text-gray-400 text-sm">Abandonment Rate</span>
          <div class="w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
        </div>
        <div class="text-2xl font-bold text-white"><%= @form.abandonment_rate %>%</div>
        <div class="text-xs text-gray-500 mt-1">
          <%= (@form.total_sessions - @form.total_submissions) %> abandoned
        </div>
      </div>

      <!-- Avg Completion Time -->
      <div class="bg-gray-900 rounded-xl p-6 border border-gray-800">
        <div class="flex items-center justify-between mb-2">
          <span class="text-gray-400 text-sm">Avg Completion</span>
          <div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        </div>
        <div class="text-2xl font-bold text-white">
          <%= @form.avg_completion_time ? "#{(@form.avg_completion_time / 60).round(1)}m" : "N/A" %>
        </div>
        <div class="text-xs text-gray-500 mt-1">
          <%= @form.avg_completion_time ? "#{@form.avg_completion_time}s average" : "No data" %>
        </div>
      </div>

      <!-- Field Count -->
      <div class="bg-gray-900 rounded-xl p-6 border border-gray-800">
        <div class="flex items-center justify-between mb-2">
          <span class="text-gray-400 text-sm">Total Fields</span>
          <div class="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
        </div>
        <div class="text-2xl font-bold text-white"><%= @form.field_count %></div>
        <div class="text-xs text-gray-500 mt-1">
          <%= @form_fields.select(&:required).count %> required
        </div>
      </div>

      <!-- Last Activity -->
      <div class="bg-gray-900 rounded-xl p-6 border border-gray-800">
        <div class="flex items-center justify-between mb-2">
          <span class="text-gray-400 text-sm">Last Activity</span>
          <div class="w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        </div>
        <div class="text-2xl font-bold text-white">
          <%= @form.last_seen_at ? time_ago_in_words(@form.last_seen_at) : "Never" %>
        </div>
        <div class="text-xs text-gray-500 mt-1">
          <%= @form.active? ? "Active" : "Inactive" %>
        </div>
      </div>
    </div>

    <!-- Conversion Funnel & Activity Chart -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Conversion Funnel -->
      <div class="bg-gray-900 rounded-xl p-6 border border-gray-800">
        <h3 class="text-lg font-semibold text-white mb-4">Conversion Funnel</h3>
        <div class="space-y-4">
          <div class="relative">
            <div class="flex justify-between mb-2">
              <span class="text-sm text-gray-400">Form Loads</span>
              <span class="text-sm text-white font-medium">100%</span>
            </div>
            <div class="w-full bg-gray-800 rounded-full h-8">
              <div class="bg-gradient-to-r from-blue-500 to-blue-600 h-8 rounded-full flex items-center justify-end pr-3" style="width: 100%">
                <span class="text-xs text-white font-medium"><%= @form.total_sessions %></span>
              </div>
            </div>
          </div>

          <div class="relative">
            <div class="flex justify-between mb-2">
              <span class="text-sm text-gray-400">Started Filling</span>
              <span class="text-sm text-white font-medium">
                <%= @form.total_sessions > 0 ? (((@form.total_sessions - (@form.total_sessions * 0.2)) / @form.total_sessions.to_f * 100).round(1)) : 0 %>%
              </span>
            </div>
            <div class="w-full bg-gray-800 rounded-full h-8">
              <div class="bg-gradient-to-r from-purple-500 to-purple-600 h-8 rounded-full flex items-center justify-end pr-3" style="width: 80%">
                <span class="text-xs text-white font-medium"><%= (@form.total_sessions * 0.8).round %></span>
              </div>
            </div>
          </div>

          <div class="relative">
            <div class="flex justify-between mb-2">
              <span class="text-sm text-gray-400">Reached 50%</span>
              <span class="text-sm text-white font-medium">
                <%= @form.total_sessions > 0 ? (((@form.total_sessions * 0.6) / @form.total_sessions.to_f * 100).round(1)) : 0 %>%
              </span>
            </div>
            <div class="w-full bg-gray-800 rounded-full h-8">
              <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 h-8 rounded-full flex items-center justify-end pr-3" style="width: 60%">
                <span class="text-xs text-white font-medium"><%= (@form.total_sessions * 0.6).round %></span>
              </div>
            </div>
          </div>

          <div class="relative">
            <div class="flex justify-between mb-2">
              <span class="text-sm text-gray-400">Completed</span>
              <span class="text-sm text-white font-medium"><%= @form.conversion_rate %>%</span>
            </div>
            <div class="w-full bg-gray-800 rounded-full h-8">
              <div class="bg-gradient-to-r from-green-500 to-green-600 h-8 rounded-full flex items-center justify-end pr-3" style="width: <%= @form.conversion_rate %>%">
                <span class="text-xs text-white font-medium"><%= @form.total_submissions %></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Activity Chart -->
      <div class="bg-gray-900 rounded-xl p-6 border border-gray-800">
        <h3 class="text-lg font-semibold text-white mb-4">Activity Over Time</h3>
        <canvas id="activityChart" class="w-full" height="200"></canvas>
      </div>
    </div>

    <!-- Field Performance -->
    <div class="bg-gray-900 rounded-xl p-6 border border-gray-800 mb-8">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-white">Field Performance</h3>
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-400">Sort by:</span>
          <select class="bg-gray-800 text-white text-sm rounded-lg px-3 py-1 border border-gray-700">
            <option>Drop-off Rate</option>
            <option>Error Rate</option>
            <option>Time Spent</option>
            <option>Position</option>
          </select>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="text-left border-b border-gray-800">
              <th class="pb-3 text-sm font-medium text-gray-400">Field Name</th>
              <th class="pb-3 text-sm font-medium text-gray-400">Type</th>
              <th class="pb-3 text-sm font-medium text-gray-400">Interactions</th>
              <th class="pb-3 text-sm font-medium text-gray-400">Avg Time</th>
              <th class="pb-3 text-sm font-medium text-gray-400">Errors</th>
              <th class="pb-3 text-sm font-medium text-gray-400">Drop-off</th>
              <th class="pb-3 text-sm font-medium text-gray-400">Health</th>
            </tr>
          </thead>
          <tbody>
            <% @field_performance.each do |field| %>
              <tr class="border-b border-gray-800 hover:bg-gray-800/50 transition-colors">
                <td class="py-3">
                  <div class="flex items-center space-x-2">
                    <span class="text-white font-medium"><%= field[:name] %></span>
                    <% if field[:required] %>
                      <span class="text-xs text-red-400 bg-red-500/20 px-2 py-0.5 rounded">Required</span>
                    <% end %>
                  </div>
                </td>
                <td class="py-3 text-gray-400 text-sm"><%= field[:type] %></td>
                <td class="py-3 text-gray-300 text-sm"><%= field[:interaction_count] %></td>
                <td class="py-3 text-gray-300 text-sm"><%= field[:avg_time_spent] ? "#{field[:avg_time_spent]}s" : "-" %></td>
                <td class="py-3">
                  <span class="text-sm <%= field[:error_count] > 0 ? 'text-red-400' : 'text-gray-300' %>">
                    <%= field[:error_count] %>
                  </span>
                </td>
                <td class="py-3">
                  <span class="text-sm <%= field[:abandonment_rate] > 30 ? 'text-orange-400' : 'text-gray-300' %>">
                    <%= field[:abandonment_rate] %>%
                  </span>
                </td>
                <td class="py-3">
                  <div class="flex items-center space-x-1">
                    <% health_score = 100 - field[:abandonment_rate] - (field[:error_count] > 0 ? 10 : 0) %>
                    <% if health_score >= 80 %>
                      <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span class="text-xs text-green-400">Good</span>
                    <% elsif health_score >= 60 %>
                      <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                      <span class="text-xs text-yellow-400">Fair</span>
                    <% else %>
                      <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span class="text-xs text-red-400">Poor</span>
                    <% end %>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Recent Sessions & Insights -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Recent Sessions -->
      <div class="bg-gray-900 rounded-xl p-6 border border-gray-800">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-white">Recent Sessions</h3>
          <%= link_to "View All", "#", class: "text-sm text-purple-400 hover:text-purple-300" %>
        </div>
        <div class="space-y-3">
          <% @recent_sessions.first(5).each do |session| %>
            <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg hover:bg-gray-800 transition-colors cursor-pointer">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <div>
                  <p class="text-sm text-white">Session #<%= session.id.last(8) %></p>
                  <p class="text-xs text-gray-400"><%= time_ago_in_words(session.started_at) %> ago</p>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <% if session.completed %>
                  <span class="text-xs text-green-400 bg-green-500/20 px-2 py-1 rounded">Completed</span>
                <% else %>
                  <span class="text-xs text-orange-400 bg-orange-500/20 px-2 py-1 rounded">Abandoned</span>
                <% end %>
                <%= link_to session_replay_form_path(@form, session_id: session.id), class: "text-purple-400 hover:text-purple-300" do %>
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
      </div>

      <!-- AI Insights -->
      <div class="bg-gray-900 rounded-xl p-6 border border-gray-800">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-white">AI Insights</h3>
          <%= link_to "Generate New", "#", class: "text-sm text-purple-400 hover:text-purple-300" %>
        </div>
        <div class="space-y-3">
          <% @insights.first(5).each do |insight| %>
            <div class="p-3 bg-gray-800/50 rounded-lg">
              <div class="flex items-start space-x-2">
                <% case insight.severity %>
                <% when 'critical' %>
                  <div class="w-6 h-6 bg-red-500/20 rounded flex items-center justify-center flex-shrink-0 mt-0.5">
                    <svg class="w-3 h-3 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                <% when 'warning' %>
                  <div class="w-6 h-6 bg-yellow-500/20 rounded flex items-center justify-center flex-shrink-0 mt-0.5">
                    <svg class="w-3 h-3 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                <% else %>
                  <div class="w-6 h-6 bg-blue-500/20 rounded flex items-center justify-center flex-shrink-0 mt-0.5">
                    <svg class="w-3 h-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                <% end %>
                <div class="flex-1">
                  <p class="text-sm text-white mb-1"><%= insight.title %></p>
                  <p class="text-xs text-gray-400"><%= insight.description %></p>
                  <% if insight.recommendation %>
                    <p class="text-xs text-purple-400 mt-2">→ <%= insight.recommendation %></p>
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  // Activity Chart
  const ctx = document.getElementById('activityChart').getContext('2d');
  const dates = <%= @sessions_by_day.keys.map { |d| d.strftime('%b %d') }.to_json.html_safe %>;
  const sessions = <%= @sessions_by_day.values.to_json %>;
  const submissions = <%= @submissions_by_day.values.to_json %>;

  new Chart(ctx, {
    type: 'line',
    data: {
      labels: dates,
      datasets: [{
        label: 'Sessions',
        data: sessions,
        borderColor: 'rgb(147, 51, 234)',
        backgroundColor: 'rgba(147, 51, 234, 0.1)',
        tension: 0.4
      }, {
        label: 'Submissions',
        data: submissions,
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.4
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: true,
          position: 'top',
          labels: {
            color: '#9CA3AF',
            boxWidth: 12,
            padding: 15
          }
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          grid: {
            color: '#374151',
            drawBorder: false
          },
          ticks: {
            color: '#9CA3AF'
          }
        },
        x: {
          grid: {
            display: false
          },
          ticks: {
            color: '#9CA3AF'
          }
        }
      }
    }
  });
</script>