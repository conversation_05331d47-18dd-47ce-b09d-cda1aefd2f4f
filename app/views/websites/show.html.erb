<div class="min-h-screen bg-gray-50">
  <!-- <PERSON> Header -->
  <div class="bg-white shadow-sm border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900"><%= @website.name %></h1>
          <p class="text-sm text-gray-600 mt-1"><%= @website.domain %></p>
        </div>
        <div class="flex space-x-3">
          <% unless @website.verified? %>
            <%= button_to "Verify Website", verify_website_path(@website), method: :post, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500" %>
          <% end %>
          <%= link_to "Edit", edit_website_path(@website), class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
          <%= link_to "Delete", website_path(@website), method: :delete, data: { confirm: "Are you sure?" }, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" %>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Installation Instructions -->
    <% unless @website.verified? %>
      <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-yellow-800">Website not verified</h3>
            <p class="mt-2 text-sm text-yellow-700">Install the tracking code below on your website to start tracking forms.</p>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Tracking Code Installation -->
    <div class="bg-white shadow rounded-lg mb-6">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Tracking Code Installation</h3>
        <p class="text-sm text-gray-600 mb-4">Add this code to your website's HTML, preferably just before the closing &lt;/body&gt; tag:</p>
        
        <div class="relative">
          <pre class="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm"><code>&lt;!-- FormFlow Pro Tracking Code --&gt;
&lt;script&gt;
  (function() {
    var script = document.createElement('script');
    script.src = '<%= api_v1_tracking_script_url(@website.tracking_id, format: :js, host: request.host_with_port, protocol: request.protocol) %>';
    script.async = true;
    script.setAttribute('data-tracking-id', '<%= @website.tracking_id %>');
    document.head.appendChild(script);
  })();
&lt;/script&gt;
&lt;!-- End FormFlow Pro Tracking Code --&gt;</code></pre>
          <button onclick="copyToClipboard(this)" data-code="<!-- FormFlow Pro Tracking Code -->
<script>
  (function() {
    var script = document.createElement('script');
    script.src = '<%= api_v1_tracking_script_url(@website.tracking_id, format: :js, host: request.host_with_port, protocol: request.protocol) %>';
    script.async = true;
    script.setAttribute('data-tracking-id', '<%= @website.tracking_id %>');
    document.head.appendChild(script);
  })();
</script>
<!-- End FormFlow Pro Tracking Code -->" class="absolute top-2 right-2 px-3 py-1 text-xs text-gray-100 bg-gray-700 hover:bg-gray-600 rounded">
            Copy
          </button>
        </div>
      </div>
    </div>

    <!-- Analytics Overview -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3 mb-6">
      <!-- Total Sessions -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Sessions</dt>
                <dd class="text-2xl font-semibold text-gray-900"><%= @total_sessions %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Total Submissions -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Submissions</dt>
                <dd class="text-2xl font-semibold text-gray-900"><%= @total_submissions %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Avg Conversion Rate -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Avg Conversion Rate</dt>
                <dd class="text-2xl font-semibold text-gray-900"><%= @avg_conversion_rate %>%</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Forms List -->
    <div class="bg-white shadow rounded-lg mb-6">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Tracked Forms</h3>
        <% if @forms.any? %>
          <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Form Name</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fields</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sessions</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submissions</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conversion Rate</th>
                  <th scope="col" class="relative px-6 py-3"><span class="sr-only">View</span></th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <% @forms.each do |form| %>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      <%= form.name %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= form.form_fields.count %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= form.total_sessions %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= form.total_submissions %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= form.conversion_rate %>%
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <%= link_to "View Analytics", form_path(form), class: "text-indigo-600 hover:text-indigo-900" %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        <% else %>
          <p class="text-gray-500 text-sm">No forms tracked yet. Install the tracking code to start collecting data.</p>
        <% end %>
      </div>
    </div>

    <!-- Recent Submissions -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Submissions</h3>
        <% if @recent_submissions.any? %>
          <div class="space-y-3">
            <% @recent_submissions.each do |submission| %>
              <div class="border-l-4 border-green-400 pl-3">
                <div class="flex justify-between">
                  <div>
                    <p class="text-sm font-medium text-gray-900">
                      <%= submission.form.name %>
                    </p>
                    <p class="text-xs text-gray-500">
                      Completion time: <%= submission.completion_time ? "#{submission.completion_time}s" : "N/A" %>
                    </p>
                  </div>
                  <p class="text-xs text-gray-400">
                    <%= time_ago_in_words(submission.submitted_at) %> ago
                  </p>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <p class="text-gray-500 text-sm">No submissions yet.</p>
        <% end %>
      </div>
    </div>
  </div>
</div>

<script>
function copyToClipboard(button) {
  const code = button.getAttribute('data-code');
  navigator.clipboard.writeText(code).then(function() {
    const originalText = button.innerText;
    button.innerText = 'Copied!';
    button.classList.add('bg-green-600');
    setTimeout(function() {
      button.innerText = originalText;
      button.classList.remove('bg-green-600');
    }, 2000);
  });
}
</script>
