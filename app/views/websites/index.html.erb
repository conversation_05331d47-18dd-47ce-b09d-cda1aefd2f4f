<!-- <PERSON> Header -->
<header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 sm:px-6 lg:px-8 py-4">
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
    <div>
      <h1 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Websites</h1>
      <p class="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mt-1">Manage your tracked websites and view analytics</p>
    </div>
    <div>
      <%= link_to new_website_path, class: "px-5 py-2.5 bg-gradient-to-r from-violet-500 to-indigo-500 text-white rounded-lg font-medium hover:from-violet-600 hover:to-indigo-600 transition-all shadow-lg flex items-center gap-2" do %>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
        Add New Website
      <% end %>
    </div>
  </div>
</header>

<!-- Main Content -->
<div class="flex-1 overflow-auto bg-gray-50 dark:bg-gray-900">
  <div class="p-4 sm:p-6 lg:p-8">
    <% if @websites.any? %>
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <% @websites.each do |website| %>
          <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
            <div class="flex items-center justify-between mb-4">
              <div class="flex-1">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                  <%= link_to website.name, website_path(website), class: "hover:text-violet-600 dark:hover:text-violet-400" %>
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400"><%= website.domain %></p>
              </div>
              <div>
                <% if website.verified? %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400">
                    Verified
                  </span>
                <% else %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400">
                    Pending
                  </span>
                <% end %>
              </div>
            </div>

            <!-- Stats -->
            <div class="grid grid-cols-2 gap-4 mb-6">
              <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Forms</p>
                <p class="text-2xl font-semibold text-gray-900 dark:text-white"><%= website.forms.count %></p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Sessions</p>
                <p class="text-2xl font-semibold text-gray-900 dark:text-white"><%= website.forms.sum(:total_sessions) %></p>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex space-x-3">
              <%= link_to "View Details", website_path(website), class: "flex-1 text-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors" %>
              <% unless website.verified? %>
                <%= button_to "Verify", verify_website_path(website), method: :post, class: "flex-1 text-center px-3 py-2 text-sm font-medium rounded-lg text-white bg-gradient-to-r from-violet-500 to-indigo-500 hover:from-violet-600 hover:to-indigo-600 transition-all" %>
              <% else %>
                <%= link_to "Edit", edit_website_path(website), class: "flex-1 text-center px-3 py-2 text-sm font-medium rounded-lg text-violet-700 dark:text-violet-400 bg-violet-100 dark:bg-violet-900/30 hover:bg-violet-200 dark:hover:bg-violet-900/50 transition-colors" %>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <!-- Empty State -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700">
        <div class="p-12 text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
          </svg>
          <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">No websites yet</h3>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Get started by adding your first website to track form analytics and user interactions.</p>
          <div class="mt-6">
            <%= link_to new_website_path, class: "px-5 py-2.5 bg-gradient-to-r from-violet-500 to-indigo-500 text-white rounded-lg font-medium hover:from-violet-600 hover:to-indigo-600 transition-all shadow-lg inline-flex items-center gap-2" do %>
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
              Add New Website
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
