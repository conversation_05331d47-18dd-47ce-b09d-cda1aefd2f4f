<!-- Top Bar -->
<header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 sm:px-6 lg:px-8 py-4">
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
    <div>
      <h1 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Welcome back, <%= current_user.first_name %>!</h1>
      <p class="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mt-1">Here's what's happening with your forms today</p>
    </div>
    
    <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
      <!-- Date Range Selector -->
      <div class="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1 overflow-x-auto">
        <button class="px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-600 rounded-md transition-colors whitespace-nowrap">
          Today
        </button>
        <button class="px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium bg-white dark:bg-gray-600 text-gray-900 dark:text-white rounded-md shadow-sm whitespace-nowrap">
          Last 7 days
        </button>
        <button class="px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-600 rounded-md transition-colors whitespace-nowrap">
          Last 30 days
        </button>
      </div>
      
      <!-- Add Website Button -->
      <%= link_to new_website_path, class: "px-4 sm:px-5 py-2 sm:py-2.5 bg-gradient-to-r from-violet-500 to-indigo-500 text-white rounded-lg font-medium hover:from-violet-600 hover:to-indigo-600 transition-all shadow-lg text-center" do %>
        <span class="flex items-center justify-center gap-2">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          <span class="text-sm sm:text-base">Add Website</span>
        </span>
      <% end %>
    </div>
  </div>
</header>

<!-- Dashboard Content -->
<div class="flex-1 overflow-auto bg-gray-50 dark:bg-gray-900">
  <div class="p-4 sm:p-6 lg:p-8">
          
          <!-- Key Metrics Grid -->
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
            <!-- Total Sessions Card -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
              <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
                  <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                  </svg>
                </div>
                <span class="text-xs font-medium text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30 px-2 py-1 rounded-full">
                  +12.5%
                </span>
              </div>
              <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><%= number_with_delimiter(@total_sessions) %></h3>
              <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Total Sessions</p>
            </div>
            
            <!-- Conversion Rate Card -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
              <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
                  <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                  </svg>
                </div>
                <span class="text-xs font-medium text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30 px-2 py-1 rounded-full">
                  +5.2%
                </span>
              </div>
              <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><%= @avg_conversion_rate %>%</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Avg Conversion Rate</p>
            </div>
            
            <!-- Total Forms Card -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
              <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-xl">
                  <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                </div>
                <span class="text-xs font-medium text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900/30 px-2 py-1 rounded-full">
                  Active
                </span>
              </div>
              <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><%= @total_forms %></h3>
              <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Active Forms</p>
            </div>
            
            <!-- Abandonment Rate Card -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
              <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-red-100 dark:bg-red-900/30 rounded-xl">
                  <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                  </svg>
                </div>
                <span class="text-xs font-medium text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 px-2 py-1 rounded-full">
                  -2.1%
                </span>
              </div>
              <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><%= @avg_abandonment_rate %>%</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Abandonment Rate</p>
            </div>
          </div>
          
          <!-- Main Content Grid -->
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6">
            
            <!-- Activity Chart (Left - 2 columns) -->
            <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
              <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-4">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Form Activity Overview</h2>
                <div class="flex gap-4 flex-wrap">
                  <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-violet-500 rounded-full"></div>
                    <span class="text-xs sm:text-sm text-gray-600 dark:text-gray-400">Submissions</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-indigo-500 rounded-full"></div>
                    <span class="text-xs sm:text-sm text-gray-600 dark:text-gray-400">Completions</span>
                  </div>
                </div>
              </div>
              
              <!-- Chart Area -->
              <div class="overflow-x-auto -mx-4 sm:mx-0 px-4 sm:px-0">
                <div class="h-64 flex items-end gap-2 sm:gap-4 min-w-[500px]">
                  <% @weekly_data.each do |day| %>
                    <div class="flex-1 flex flex-col items-center gap-2">
                      <div class="w-full flex flex-col justify-end h-48 gap-1">
                        <div class="bg-violet-500 rounded-t-lg transition-all hover:bg-violet-600" 
                             style="height: <%= day[:submissions] > 0 ? (day[:submissions].to_f / (@weekly_data.map { |d| d[:submissions] }.max || 1) * 100) : 5 %>%"
                             title="<%= day[:submissions] %> submissions">
                        </div>
                        <div class="bg-indigo-500 rounded-b-lg transition-all hover:bg-indigo-600" 
                             style="height: <%= day[:conversions] > 0 ? (day[:conversions].to_f / (@weekly_data.map { |d| d[:submissions] }.max || 1) * 100) : 5 %>%"
                             title="<%= day[:conversions] %> completions">
                        </div>
                      </div>
                      <span class="text-xs text-gray-500 dark:text-gray-400"><%= day[:date] %></span>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
            
            <!-- Right Side Content -->
            <div class="space-y-6">
              
              <!-- Engagement Breakdown -->
              <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
                <h2 class="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4">Engagement Breakdown</h2>
                
                <!-- Mini Donut Chart -->
                <div class="flex items-center justify-center mb-4">
                  <div class="relative">
                    <svg class="w-32 h-32 transform -rotate-90">
                      <circle cx="64" cy="64" r="56" stroke="currentColor" stroke-width="16" fill="none" class="text-gray-200 dark:text-gray-700"></circle>
                      <circle cx="64" cy="64" r="56" stroke="currentColor" stroke-width="16" fill="none" 
                              stroke-dasharray="<%= @engagement_breakdown[:completed] * 3.52 %> 352"
                              class="text-green-500"></circle>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                      <div class="text-center">
                        <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= @engagement_breakdown[:completed] %>%</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">Completed</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Stats -->
                <div class="space-y-3">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                      <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span class="text-sm text-gray-600 dark:text-gray-400">Completed</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white"><%= @engagement_breakdown[:completed] %>%</span>
                  </div>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                      <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                      <span class="text-sm text-gray-600 dark:text-gray-400">Partial</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white"><%= @engagement_breakdown[:partial] %>%</span>
                  </div>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                      <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span class="text-sm text-gray-600 dark:text-gray-400">Abandoned</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white"><%= @engagement_breakdown[:abandoned] %>%</span>
                  </div>
                </div>
              </div>
              
              <!-- Critical Insights -->
              <% if @critical_insights.any? %>
                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-2xl p-4 sm:p-6">
                  <div class="flex items-center gap-2 mb-4">
                    <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                    <h2 class="text-lg font-semibold text-red-900 dark:text-red-200">Critical Insights</h2>
                  </div>
                  <div class="space-y-2">
                    <% @critical_insights.first(2).each do |insight| %>
                      <div class="p-3 bg-white dark:bg-gray-800 rounded-lg">
                        <p class="text-sm font-medium text-gray-900 dark:text-white"><%= insight.title %></p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1"><%= insight.form.name %></p>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
          
          <!-- Bottom Section -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6 mt-6">
            
            <!-- Top Forms -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
              <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Top Performing Forms</h2>
                <%= link_to "View all", websites_path, class: "text-sm text-violet-600 dark:text-violet-400 hover:underline" %>
              </div>
              
              <div class="space-y-4">
                <% if @top_forms.any? %>
                  <% @top_forms.each_with_index do |form, index| %>
                    <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                      <div class="flex items-center gap-4">
                        <div class="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-violet-500 to-indigo-500 text-white rounded-lg font-bold text-sm">
                          <%= index + 1 %>
                        </div>
                        <div>
                          <p class="font-medium text-gray-900 dark:text-white"><%= form.name %></p>
                          <p class="text-sm text-gray-500 dark:text-gray-400"><%= form.website.name %></p>
                        </div>
                      </div>
                      <div class="text-right">
                        <p class="font-semibold text-green-600 dark:text-green-400"><%= form.conversion_rate %>%</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">conversion</p>
                      </div>
                    </div>
                  <% end %>
                <% else %>
                  <p class="text-center text-gray-500 dark:text-gray-400 py-8">No form data available yet</p>
                <% end %>
              </div>
            </div>
            
            <!-- Recent Activity -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
              <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Activity</h2>
              
              <div class="space-y-3">
                <% if @recent_submissions.any? %>
                  <% @recent_submissions.first(5).each do |submission| %>
                    <div class="flex items-start gap-3">
                      <div class="mt-1.5">
                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                      </div>
                      <div class="flex-1">
                        <p class="text-sm text-gray-900 dark:text-white">
                          New submission on <span class="font-medium"><%= submission.form.name %></span>
                        </p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          <%= submission.form.website.name %> • <%= time_ago_in_words(submission.submitted_at) %> ago
                        </p>
                      </div>
                    </div>
                  <% end %>
                <% else %>
                  <p class="text-center text-gray-500 dark:text-gray-400 py-8">No recent activity</p>
                <% end %>
              </div>
            </div>
          </div>
          
        </div>
      </div>