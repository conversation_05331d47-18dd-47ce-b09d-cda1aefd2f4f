<% content_for :title, "Real-time Monitoring - FormFlow Pro" %>

<div class="min-h-screen bg-gray-900">
  <!-- Header -->
  <div class="bg-gray-800 border-b border-gray-700">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-4">
        <div>
          <h1 class="text-2xl font-bold text-white">Real-time Monitoring</h1>
          <p class="text-sm text-gray-400 mt-1">Live form activity and performance monitoring</p>
        </div>
        <div class="flex items-center space-x-3">
          <!-- Auto-refresh Toggle -->
          <div class="flex items-center">
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" checked class="sr-only peer" data-controller="auto-refresh">
              <div class="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-500"></div>
              <span class="ml-3 text-sm font-medium text-gray-300">Auto-refresh</span>
            </label>
          </div>
          
          <!-- Refresh Rate -->
          <select class="px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-sm text-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500">
            <option>5 seconds</option>
            <option>10 seconds</option>
            <option>30 seconds</option>
            <option>1 minute</option>
          </select>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- Live Metrics Row -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <!-- Active Users -->
      <div class="bg-gray-800 rounded-xl p-4 border border-gray-700">
        <div class="flex items-center justify-between mb-2">
          <span class="text-xs font-medium text-gray-400 uppercase tracking-wider">Active Users</span>
          <div class="flex items-center">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
            <span class="text-xs text-green-500">LIVE</span>
          </div>
        </div>
        <div class="flex items-end justify-between">
          <h3 class="text-3xl font-bold text-white">247</h3>
          <div class="flex flex-col items-end">
            <span class="text-xs text-green-500">↑ 12</span>
            <span class="text-xs text-gray-500">vs 5 min ago</span>
          </div>
        </div>
        <div class="mt-3 h-16">
          <canvas id="activeUsersSparkline"></canvas>
        </div>
      </div>

      <!-- Forms/Minute -->
      <div class="bg-gray-800 rounded-xl p-4 border border-gray-700">
        <div class="flex items-center justify-between mb-2">
          <span class="text-xs font-medium text-gray-400 uppercase tracking-wider">Forms/Min</span>
          <div class="flex items-center">
            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse mr-2"></div>
            <span class="text-xs text-blue-500">LIVE</span>
          </div>
        </div>
        <div class="flex items-end justify-between">
          <h3 class="text-3xl font-bold text-white">34</h3>
          <div class="flex flex-col items-end">
            <span class="text-xs text-red-500">↓ 3</span>
            <span class="text-xs text-gray-500">vs 5 min ago</span>
          </div>
        </div>
        <div class="mt-3 h-16">
          <canvas id="formsPerMinuteSparkline"></canvas>
        </div>
      </div>

      <!-- Live Conversion Rate -->
      <div class="bg-gray-800 rounded-xl p-4 border border-gray-700">
        <div class="flex items-center justify-between mb-2">
          <span class="text-xs font-medium text-gray-400 uppercase tracking-wider">Conv. Rate</span>
          <div class="flex items-center">
            <div class="w-2 h-2 bg-purple-500 rounded-full animate-pulse mr-2"></div>
            <span class="text-xs text-purple-500">LIVE</span>
          </div>
        </div>
        <div class="flex items-end justify-between">
          <h3 class="text-3xl font-bold text-white">72.3%</h3>
          <div class="flex flex-col items-end">
            <span class="text-xs text-green-500">↑ 2.1%</span>
            <span class="text-xs text-gray-500">vs avg</span>
          </div>
        </div>
        <div class="mt-3 h-16">
          <canvas id="conversionRateSparkline"></canvas>
        </div>
      </div>

      <!-- Error Rate -->
      <div class="bg-gray-800 rounded-xl p-4 border border-gray-700">
        <div class="flex items-center justify-between mb-2">
          <span class="text-xs font-medium text-gray-400 uppercase tracking-wider">Error Rate</span>
          <div class="flex items-center">
            <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse mr-2"></div>
            <span class="text-xs text-red-500">ALERT</span>
          </div>
        </div>
        <div class="flex items-end justify-between">
          <h3 class="text-3xl font-bold text-white">4.2%</h3>
          <div class="flex flex-col items-end">
            <span class="text-xs text-red-500">↑ 1.5%</span>
            <span class="text-xs text-gray-500">critical</span>
          </div>
        </div>
        <div class="mt-3 h-16">
          <canvas id="errorRateSparkline"></canvas>
        </div>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Live Activity Feed (2 columns) -->
      <div class="lg:col-span-2 bg-gray-800 rounded-xl border border-gray-700">
        <div class="p-4 border-b border-gray-700">
          <div class="flex justify-between items-center">
            <h2 class="text-lg font-semibold text-white">Live Activity Feed</h2>
            <div class="flex items-center space-x-2">
              <button class="px-3 py-1 text-xs bg-gray-700 text-gray-300 rounded hover:bg-gray-600">All Events</button>
              <button class="px-3 py-1 text-xs text-gray-400 hover:bg-gray-700 rounded">Errors Only</button>
              <button class="px-3 py-1 text-xs text-gray-400 hover:bg-gray-700 rounded">Completions</button>
            </div>
          </div>
        </div>
        
        <div class="p-4 space-y-3 max-h-96 overflow-y-auto" data-controller="live-feed">
          <!-- Live Event Item -->
          <div class="flex items-start space-x-3 p-3 bg-gray-700/50 rounded-lg animate-slideIn">
            <div class="flex-shrink-0">
              <div class="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <p class="text-sm font-medium text-white">Form Completed</p>
                <span class="text-xs text-gray-500">just now</span>
              </div>
              <p class="text-xs text-gray-400 mt-1">Contact Form • Session #a3b4c5</p>
              <div class="flex items-center mt-2 space-x-4 text-xs text-gray-500">
                <span>Duration: 1m 23s</span>
                <span>Fields: 5/5</span>
                <span>Device: Mobile</span>
              </div>
            </div>
          </div>

          <div class="flex items-start space-x-3 p-3 bg-gray-700/50 rounded-lg">
            <div class="flex-shrink-0">
              <div class="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <p class="text-sm font-medium text-white">Form Started</p>
                <span class="text-xs text-gray-500">2s ago</span>
              </div>
              <p class="text-xs text-gray-400 mt-1">Registration Form • Session #d5e6f7</p>
              <div class="flex items-center mt-2 space-x-4 text-xs text-gray-500">
                <span>Location: New York, US</span>
                <span>Browser: Chrome</span>
              </div>
            </div>
          </div>

          <div class="flex items-start space-x-3 p-3 bg-red-900/20 border border-red-800 rounded-lg">
            <div class="flex-shrink-0">
              <div class="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <p class="text-sm font-medium text-red-400">Validation Error</p>
                <span class="text-xs text-gray-500">5s ago</span>
              </div>
              <p class="text-xs text-gray-400 mt-1">Checkout Form • Session #g8h9i0</p>
              <div class="flex items-center mt-2 space-x-4 text-xs text-gray-500">
                <span class="text-red-400">Field: Email</span>
                <span>Error: Invalid format</span>
                <span>Attempts: 3</span>
              </div>
            </div>
          </div>

          <div class="flex items-start space-x-3 p-3 bg-gray-700/50 rounded-lg">
            <div class="flex-shrink-0">
              <div class="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <p class="text-sm font-medium text-white">Form Abandoned</p>
                <span class="text-xs text-gray-500">8s ago</span>
              </div>
              <p class="text-xs text-gray-400 mt-1">Survey Form • Session #j1k2l3</p>
              <div class="flex items-center mt-2 space-x-4 text-xs text-gray-500">
                <span>Progress: 60%</span>
                <span>Last Field: Phone Number</span>
                <span>Time Spent: 3m 45s</span>
              </div>
            </div>
          </div>

          <!-- More events... -->
        </div>
      </div>

      <!-- Right Column -->
      <div class="space-y-6">
        <!-- Active Forms -->
        <div class="bg-gray-800 rounded-xl border border-gray-700 p-4">
          <h3 class="text-sm font-semibold text-white mb-4">Most Active Forms</h3>
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
                  <span class="text-xs font-bold text-green-500">1</span>
                </div>
                <div>
                  <p class="text-sm font-medium text-white">Contact Form</p>
                  <p class="text-xs text-gray-500">87 active users</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-xs text-green-500">+15%</p>
              </div>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                  <span class="text-xs font-bold text-blue-500">2</span>
                </div>
                <div>
                  <p class="text-sm font-medium text-white">Registration</p>
                  <p class="text-xs text-gray-500">62 active users</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-xs text-gray-500">+2%</p>
              </div>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center">
                  <span class="text-xs font-bold text-purple-500">3</span>
                </div>
                <div>
                  <p class="text-sm font-medium text-white">Checkout</p>
                  <p class="text-xs text-gray-500">41 active users</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-xs text-red-500">-5%</p>
              </div>
            </div>
          </div>
        </div>

        <!-- System Health -->
        <div class="bg-gray-800 rounded-xl border border-gray-700 p-4">
          <h3 class="text-sm font-semibold text-white mb-4">System Health</h3>
          <div class="space-y-3">
            <div>
              <div class="flex justify-between items-center mb-1">
                <span class="text-xs text-gray-400">API Response Time</span>
                <span class="text-xs text-green-500">45ms</span>
              </div>
              <div class="w-full bg-gray-700 rounded-full h-2">
                <div class="bg-green-500 h-2 rounded-full" style="width: 15%"></div>
              </div>
            </div>

            <div>
              <div class="flex justify-between items-center mb-1">
                <span class="text-xs text-gray-400">Database Load</span>
                <span class="text-xs text-yellow-500">67%</span>
              </div>
              <div class="w-full bg-gray-700 rounded-full h-2">
                <div class="bg-yellow-500 h-2 rounded-full" style="width: 67%"></div>
              </div>
            </div>

            <div>
              <div class="flex justify-between items-center mb-1">
                <span class="text-xs text-gray-400">Memory Usage</span>
                <span class="text-xs text-green-500">42%</span>
              </div>
              <div class="w-full bg-gray-700 rounded-full h-2">
                <div class="bg-green-500 h-2 rounded-full" style="width: 42%"></div>
              </div>
            </div>

            <div>
              <div class="flex justify-between items-center mb-1">
                <span class="text-xs text-gray-400">Error Rate</span>
                <span class="text-xs text-red-500">4.2%</span>
              </div>
              <div class="w-full bg-gray-700 rounded-full h-2">
                <div class="bg-red-500 h-2 rounded-full" style="width: 4.2%"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Alerts -->
        <div class="bg-gray-800 rounded-xl border border-gray-700 p-4">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-sm font-semibold text-white">Recent Alerts</h3>
            <span class="px-2 py-1 text-xs bg-red-500/20 text-red-400 rounded-full">3 active</span>
          </div>
          <div class="space-y-2">
            <div class="p-2 bg-red-900/20 border border-red-800 rounded-lg">
              <div class="flex items-start space-x-2">
                <svg class="w-4 h-4 text-red-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <div class="flex-1">
                  <p class="text-xs font-medium text-red-400">High Error Rate</p>
                  <p class="text-xs text-gray-500 mt-1">Password field validation failing at 23% rate</p>
                  <p class="text-xs text-gray-600 mt-1">2 min ago</p>
                </div>
              </div>
            </div>

            <div class="p-2 bg-yellow-900/20 border border-yellow-800 rounded-lg">
              <div class="flex items-start space-x-2">
                <svg class="w-4 h-4 text-yellow-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <div class="flex-1">
                  <p class="text-xs font-medium text-yellow-400">Performance Warning</p>
                  <p class="text-xs text-gray-500 mt-1">Form load time exceeding 3s threshold</p>
                  <p class="text-xs text-gray-600 mt-1">5 min ago</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Geographic Distribution -->
    <div class="mt-6 bg-gray-800 rounded-xl border border-gray-700 p-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold text-white">Geographic Activity</h2>
        <div class="flex items-center space-x-2">
          <span class="text-xs text-gray-500">Showing:</span>
          <select class="px-2 py-1 bg-gray-700 border border-gray-600 rounded text-xs text-gray-300">
            <option>Active Sessions</option>
            <option>Conversions</option>
            <option>Errors</option>
          </select>
        </div>
      </div>
      
      <!-- Placeholder for map -->
      <div class="h-64 bg-gray-900 rounded-lg flex items-center justify-center">
        <p class="text-gray-500">Geographic heatmap visualization</p>
      </div>
      
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
        <div class="text-center">
          <p class="text-2xl font-bold text-white">42%</p>
          <p class="text-xs text-gray-500">United States</p>
        </div>
        <div class="text-center">
          <p class="text-2xl font-bold text-white">18%</p>
          <p class="text-xs text-gray-500">United Kingdom</p>
        </div>
        <div class="text-center">
          <p class="text-2xl font-bold text-white">15%</p>
          <p class="text-xs text-gray-500">Canada</p>
        </div>
        <div class="text-center">
          <p class="text-2xl font-bold text-white">25%</p>
          <p class="text-xs text-gray-500">Others</p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Chart.js for sparklines -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  // Initialize sparkline charts
  document.addEventListener('DOMContentLoaded', function() {
    // Common sparkline configuration
    const sparklineConfig = {
      type: 'line',
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: { display: false },
          tooltip: { enabled: false }
        },
        scales: {
          x: { display: false },
          y: { display: false }
        },
        elements: {
          point: { radius: 0 },
          line: { borderWidth: 2 }
        }
      }
    };

    // Active Users Sparkline
    const activeUsersCtx = document.getElementById('activeUsersSparkline');
    if (activeUsersCtx) {
      new Chart(activeUsersCtx, {
        ...sparklineConfig,
        data: {
          labels: Array(20).fill(''),
          datasets: [{
            data: [230, 235, 232, 238, 240, 235, 242, 245, 243, 247],
            borderColor: 'rgb(34, 197, 94)',
            tension: 0.4
          }]
        }
      });
    }

    // Forms/Minute Sparkline
    const formsPerMinuteCtx = document.getElementById('formsPerMinuteSparkline');
    if (formsPerMinuteCtx) {
      new Chart(formsPerMinuteCtx, {
        ...sparklineConfig,
        data: {
          labels: Array(20).fill(''),
          datasets: [{
            data: [32, 35, 33, 37, 36, 35, 34, 33, 35, 34],
            borderColor: 'rgb(59, 130, 246)',
            tension: 0.4
          }]
        }
      });
    }

    // Conversion Rate Sparkline
    const conversionRateCtx = document.getElementById('conversionRateSparkline');
    if (conversionRateCtx) {
      new Chart(conversionRateCtx, {
        ...sparklineConfig,
        data: {
          labels: Array(20).fill(''),
          datasets: [{
            data: [70.1, 71.2, 70.8, 71.5, 72.0, 71.8, 72.1, 72.5, 72.2, 72.3],
            borderColor: 'rgb(168, 85, 247)',
            tension: 0.4
          }]
        }
      });
    }

    // Error Rate Sparkline
    const errorRateCtx = document.getElementById('errorRateSparkline');
    if (errorRateCtx) {
      new Chart(errorRateCtx, {
        ...sparklineConfig,
        data: {
          labels: Array(20).fill(''),
          datasets: [{
            data: [2.5, 2.8, 3.1, 2.9, 3.3, 3.5, 3.8, 4.0, 4.1, 4.2],
            borderColor: 'rgb(239, 68, 68)',
            tension: 0.4
          }]
        }
      });
    }
  });
</script>

<style>
  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .animate-slideIn {
    animation: slideIn 0.3s ease-out;
  }
</style>