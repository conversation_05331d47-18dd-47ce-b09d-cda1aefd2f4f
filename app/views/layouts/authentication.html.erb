<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for?(:title) ? yield(:title) : "FormFlow Pro" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_include_tag "application", "data-turbo-track": "reload", defer: true %>
    
    <style>
      @keyframes float {
        0% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
        100% { transform: translateY(0px); }
      }
      
      .float-animation {
        animation: float 6s ease-in-out infinite;
      }
      
      .float-animation-delay-1 {
        animation-delay: 1s;
      }
      
      .float-animation-delay-2 {
        animation-delay: 2s;
      }
      
      @keyframes pulse-glow {
        0%, 100% { opacity: 0.5; }
        50% { opacity: 0.8; }
      }
      
      .pulse-glow {
        animation: pulse-glow 4s ease-in-out infinite;
      }
    </style>
  </head>

  <body>
    <div class="min-h-screen flex">
      <!-- Left Panel - Form -->
      <div class="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-20 xl:px-24 bg-black relative overflow-hidden">
        <!-- Background effects -->
        <div class="absolute inset-0 bg-gradient-to-br from-purple-950/50 via-black to-black"></div>
        <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_top_left,_var(--tw-gradient-stops))] from-purple-800/20 to-transparent"></div>
        <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_right,_var(--tw-gradient-stops))] from-blue-800/20 to-transparent"></div>
        
        <div class="mx-auto w-full max-w-md relative z-10">
          <!-- Logo -->
          <div class="mb-8">
            <%= link_to root_path, class: "inline-flex items-center space-x-2" do %>
              <div class="w-10 h-10 bg-gradient-to-r from-blue-400 to-purple-400 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
              <span class="text-white font-bold text-2xl">FormFlow Pro</span>
            <% end %>
            <p class="text-gray-400 mt-2">Intelligent Form Analytics Platform</p>
          </div>
          
          <!-- Main Content Card -->
          <div class="bg-gray-900/50 backdrop-blur-xl rounded-2xl p-8 border border-gray-800">
            <%= yield %>
          </div>
        </div>
        
        <!-- Decorative Elements -->
        <div class="absolute top-10 left-10 w-20 h-20 bg-purple-500/10 rounded-full blur-xl pulse-glow"></div>
        <div class="absolute bottom-10 right-10 w-32 h-32 bg-blue-500/10 rounded-full blur-2xl pulse-glow"></div>
      </div>
      
      <!-- Right Panel - Decorative -->
      <div class="hidden lg:block relative w-0 flex-1 bg-gradient-to-br from-purple-950 to-black">
        <div class="absolute inset-0 bg-black/40"></div>
        <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-purple-600/20 to-transparent"></div>
        
        <div class="absolute inset-0 flex items-center justify-center p-12">
          <div class="max-w-xl relative z-10">
            <!-- Main Heading -->
            <h2 class="text-4xl font-bold text-white mb-6">
              Stop Losing Customers at Your Forms
            </h2>
            <p class="text-xl text-gray-300 mb-12">
              Join thousands of businesses using AI-powered insights to optimize their conversion rates
            </p>
            
            <!-- Metric Cards -->
            <div class="grid grid-cols-2 gap-6">
              <!-- Card 1 -->
              <div class="bg-white/10 backdrop-blur-sm p-6 rounded-xl border border-white/20 float-animation">
                <div class="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-2">87%</div>
                <div class="text-gray-300 text-sm">Average conversion improvement</div>
              </div>
              
              <!-- Card 2 -->
              <div class="bg-white/10 backdrop-blur-sm p-6 rounded-xl border border-white/20 float-animation-delay-1">
                <div class="text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-2">2.5M+</div>
                <div class="text-gray-300 text-sm">Forms analyzed monthly</div>
              </div>
              
              <!-- Card 3 -->
              <div class="bg-white/10 backdrop-blur-sm p-6 rounded-xl border border-white/20 float-animation-delay-2">
                <div class="text-3xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent mb-2">< 5min</div>
                <div class="text-gray-300 text-sm">Setup time required</div>
              </div>
              
              <!-- Card 4 -->
              <div class="bg-white/10 backdrop-blur-sm p-6 rounded-xl border border-white/20 float-animation">
                <div class="text-3xl font-bold bg-gradient-to-r from-pink-400 to-orange-400 bg-clip-text text-transparent mb-2">24/7</div>
                <div class="text-gray-300 text-sm">Real-time monitoring</div>
              </div>
            </div>
            
            <!-- Testimonial -->
            <div class="mt-12 bg-white/10 backdrop-blur-sm p-6 rounded-xl border border-white/20">
              <div class="flex items-start">
                <svg class="h-8 w-8 text-purple-400 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                </svg>
                <div>
                  <p class="text-gray-300 italic mb-3">
                    "FormFlow Pro helped us identify critical drop-off points we never knew existed. Our conversion rate jumped 42% in just two weeks!"
                  </p>
                  <div class="text-gray-400 text-sm">
                    <div class="font-semibold text-white">Sarah Chen</div>
                    <div>Head of Growth, TechStartup Inc.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Animated gradient orbs -->
        <div class="absolute top-1/4 right-1/4 w-64 h-64 bg-purple-500 rounded-full blur-3xl opacity-20 float-animation"></div>
        <div class="absolute bottom-1/4 left-1/4 w-48 h-48 bg-blue-500 rounded-full blur-3xl opacity-20 float-animation-delay-2"></div>
      </div>
    </div>
    
    <!-- Flash Messages -->
    <% if notice.present? %>
      <div class="fixed top-4 right-4 z-50 bg-green-500/90 backdrop-blur-sm text-white px-6 py-3 rounded-lg shadow-lg border border-green-400/50" data-turbo-temporary>
        <div class="flex items-center">
          <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
          <%= notice %>
        </div>
      </div>
    <% end %>
    
    <% if alert.present? %>
      <div class="fixed top-4 right-4 z-50 bg-red-500/90 backdrop-blur-sm text-white px-6 py-3 rounded-lg shadow-lg border border-red-400/50" data-turbo-temporary>
        <div class="flex items-center">
          <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
          </svg>
          <%= alert %>
        </div>
      </div>
    <% end %>
  </body>
</html>