<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "FormFlow Pro - Form Analytics Platform" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="<%= user_signed_in? ? 'bg-gray-50 dark:bg-gray-950' : '' %> transition-colors duration-300">

    <!-- Flash messages -->
    <% if notice.present? %>
      <div class="bg-green-50 border-l-4 border-green-400 p-4">
        <div class="flex">
          <div class="ml-3">
            <p class="text-sm text-green-700"><%= notice %></p>
          </div>
        </div>
      </div>
    <% end %>
    
    <% if alert.present? %>
      <div class="bg-red-50 border-l-4 border-red-400 p-4">
        <div class="flex">
          <div class="ml-3">
            <p class="text-sm text-red-700"><%= alert %></p>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Main content -->
    <main class="">
      <% if user_signed_in? %>
        <!-- Full-width dashboard layout with sidebar -->
        <div class="min-h-screen bg-gray-100 dark:bg-gray-900">
          <div class="flex h-screen overflow-hidden" data-controller="sidebar">
            
            <!-- Mobile overlay -->
            <div data-sidebar-target="overlay" class="fixed inset-0 bg-gray-600 bg-opacity-75 z-30 hidden lg:hidden" data-action="click->sidebar#close"></div>
            
            <!-- Mobile menu button -->
            <div class="lg:hidden fixed top-4 left-4 z-50">
              <button data-sidebar-target="hamburger" data-action="click->sidebar#toggle" class="p-2 rounded-md bg-white dark:bg-gray-800 shadow-lg">
                <svg class="w-6 h-6 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
              </button>
            </div>
            
            <!-- Sidebar -->
            <aside data-sidebar-target="menu" class="fixed lg:relative inset-y-0 left-0 z-40 w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex-shrink-0 transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out">
              <div class="h-full flex flex-col">
                <!-- Logo Section -->
                <div class="p-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                  <h2 class="text-2xl font-bold bg-gradient-to-r from-violet-600 to-indigo-600 bg-clip-text text-transparent">
                    FormFlow Pro
                  </h2>
                  <!-- Close button for mobile -->
                  <button data-action="click->sidebar#close" class="lg:hidden p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                    <svg class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </button>
                </div>
                
                <!-- Navigation -->
                <nav class="flex-1 p-4 space-y-6 overflow-y-auto">
                  <!-- Main Section -->
                  <div>
                    <h3 class="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">Main</h3>
                    <div class="space-y-1">
                      <%= link_to dashboard_path, class: "flex items-center gap-3 px-4 py-3 #{current_page?(dashboard_path) ? 'bg-gradient-to-r from-violet-500 to-indigo-500 text-white' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-xl transition-colors #{current_page?(dashboard_path) ? 'shadow-lg' : ''}" do %>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                        <span class="font-medium">Dashboard</span>
                      <% end %>
                      
                      <%= link_to websites_path, class: "flex items-center gap-3 px-4 py-3 #{current_page?(websites_path) ? 'bg-gradient-to-r from-violet-500 to-indigo-500 text-white' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-xl transition-colors #{current_page?(websites_path) ? 'shadow-lg' : ''}" do %>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
                        </svg>
                        <span class="font-medium">Websites</span>
                      <% end %>
                      
                      <% if Current.account && Current.account.forms.any? %>
                        <%= link_to form_path(Current.account.forms.first), class: "flex items-center gap-3 px-4 py-3 #{controller_name == 'forms' ? 'bg-gradient-to-r from-violet-500 to-indigo-500 text-white' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-xl transition-colors #{controller_name == 'forms' ? 'shadow-lg' : ''}" do %>
                          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                          </svg>
                          <span class="font-medium">Forms</span>
                          <% if Current.account.forms.count > 0 %>
                            <span class="ml-auto bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs px-2 py-0.5 rounded-full"><%= Current.account.forms.count %></span>
                          <% end %>
                        <% end %>
                      <% end %>
                    </div>
                  </div>
                  
                  <!-- Analytics Section -->
                  <div>
                    <h3 class="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">Analytics</h3>
                    <div class="space-y-1">
                      <%= link_to analytics_path, class: "flex items-center gap-3 px-4 py-3 #{current_page?(analytics_path) ? 'bg-gradient-to-r from-violet-500 to-indigo-500 text-white' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-xl transition-colors #{current_page?(analytics_path) ? 'shadow-lg' : ''}" do %>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        <span class="font-medium">Analytics</span>
                      <% end %>
                      
                      <% if Current.account && Current.account.forms.any? %>
                        <%= link_to form_session_replays_path(Current.account.forms.first), class: "flex items-center gap-3 px-4 py-3 #{controller_name == 'session_replays' ? 'bg-gradient-to-r from-violet-500 to-indigo-500 text-white' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-xl transition-colors #{controller_name == 'session_replays' ? 'shadow-lg' : ''}" do %>
                          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          </svg>
                          <span class="font-medium">Session Replays</span>
                        <% end %>
                      <% end %>
                      
                      <%= link_to insights_path, class: "flex items-center gap-3 px-4 py-3 #{current_page?(insights_path) ? 'bg-gradient-to-r from-violet-500 to-indigo-500 text-white' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-xl transition-colors #{current_page?(insights_path) ? 'shadow-lg' : ''}" do %>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        <span class="font-medium">Insights</span>
                        <% unresolved_count = Insight.joins(form: { website: :account }).where(websites: { account_id: Current.account.id }).where(resolved_at: nil).count if Current.account %>
                        <% if unresolved_count && unresolved_count > 0 %>
                          <span class="ml-auto bg-yellow-500 text-white text-xs px-2 py-0.5 rounded-full"><%= unresolved_count %></span>
                        <% end %>
                      <% end %>
                      
                      <%= link_to monitoring_dashboard_path, class: "flex items-center gap-3 px-4 py-3 #{current_page?(monitoring_dashboard_path) ? 'bg-gradient-to-r from-violet-500 to-indigo-500 text-white' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-xl transition-colors #{current_page?(monitoring_dashboard_path) ? 'shadow-lg' : ''}" do %>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                        <span class="font-medium">Monitoring</span>
                        <span class="ml-auto">
                          <span class="flex h-2 w-2">
                            <span class="animate-ping absolute inline-flex h-2 w-2 rounded-full bg-green-400 opacity-75"></span>
                            <span class="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
                          </span>
                        </span>
                      <% end %>
                    </div>
                  </div>
                  
                  <!-- Management Section -->
                  <div>
                    <h3 class="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">Management</h3>
                    <div class="space-y-1">
                      <%= link_to settings_path, class: "flex items-center gap-3 px-4 py-3 #{controller_name == 'settings' ? 'bg-gradient-to-r from-violet-500 to-indigo-500 text-white' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-xl transition-colors #{controller_name == 'settings' ? 'shadow-lg' : ''}" do %>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span class="font-medium">Settings</span>
                      <% end %>
                      
                      <%= link_to api_documentation_path, class: "flex items-center gap-3 px-4 py-3 #{current_page?(api_documentation_path) ? 'bg-gradient-to-r from-violet-500 to-indigo-500 text-white' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-xl transition-colors #{current_page?(api_documentation_path) ? 'shadow-lg' : ''}" do %>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                        </svg>
                        <span class="font-medium">API Docs</span>
                      <% end %>
                    </div>
                  </div>
                </nav>
                
                <!-- User Section -->
                <div class="p-4 border-t border-gray-200 dark:border-gray-700">
                  <div class="flex items-center gap-3 px-3 py-2">
                    <div class="w-10 h-10 bg-gradient-to-br from-violet-500 to-indigo-500 rounded-full flex items-center justify-center text-white font-bold">
                      <%= current_user.first_name[0].upcase %>
                    </div>
                    <div class="flex-1">
                      <p class="text-sm font-medium text-gray-900 dark:text-white"><%= current_user.full_name %></p>
                      <p class="text-xs text-gray-500 dark:text-gray-400"><%= current_user.email %></p>
                    </div>
                    <!-- Dark Mode Toggle in Sidebar -->
                    <button data-controller="theme" data-action="click->theme#toggle" 
                            class="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                      <svg class="w-4 h-4 hidden dark:block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                      </svg>
                      <svg class="w-4 h-4 block dark:hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                      </svg>
                    </button>
                  </div>
                  
                  <!-- Settings and Sign Out -->
                  <div class="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex gap-2">
                      <%= link_to edit_user_registration_path, 
                          class: "flex-1 text-center py-2 text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors" do %>
                        Settings
                      <% end %>
                      <%= button_to destroy_user_session_path, 
                          method: :delete, 
                          class: "flex-1 text-center py-2 text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors" do %>
                        Sign out
                      <% end %>
                    </div>
                  </div>
                </div>
              </div>
            </aside>
            
            <!-- Main Content Area -->
            <main class="flex-1 flex flex-col overflow-hidden">
              <%= yield %>
            </main>
          </div>
        </div>
      <% else %>
        <div class="min-h-screen bg-gradient-to-b from-black to-purple-950">
          <%= yield %>
        </div>
      <% end %>
    </main>
  </body>
</html>