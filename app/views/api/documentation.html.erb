<div class="min-h-screen bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="bg-white shadow-sm rounded-lg mb-8">
      <div class="px-6 py-5 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">API Documentation</h1>
            <p class="mt-1 text-sm text-gray-600">Complete reference for the FormFlow Pro API</p>
          </div>
          <div class="flex items-center space-x-4">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              API v1
            </span>
            <%= link_to "View API Keys", settings_api_keys_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Start -->
    <div class="bg-white shadow-sm rounded-lg mb-8">
      <div class="px-6 py-5 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">Quick Start</h2>
      </div>
      <div class="px-6 py-4">
        <div class="prose max-w-none">
          <p class="text-gray-600">Get started with the FormFlow Pro API in minutes. All API requests require authentication using an API key.</p>
          
          <div class="mt-4">
            <h3 class="text-sm font-medium text-gray-900 mb-2">Base URL</h3>
            <div class="bg-gray-900 rounded-md p-3">
              <code class="text-green-400 text-sm">https://api.formflowpro.com/v1</code>
            </div>
          </div>

          <div class="mt-4">
            <h3 class="text-sm font-medium text-gray-900 mb-2">Authentication</h3>
            <p class="text-sm text-gray-600 mb-2">Include your API key in the request header:</p>
            <div class="bg-gray-900 rounded-md p-3">
              <code class="text-green-400 text-sm">X-API-Key: ffp_live_1234567890abcdef...</code>
            </div>
          </div>

          <div class="mt-4">
            <h3 class="text-sm font-medium text-gray-900 mb-2">Example Request</h3>
            <div class="bg-gray-900 rounded-md p-3 overflow-x-auto">
              <pre class="text-green-400 text-sm">curl -X GET https://api.formflowpro.com/v1/forms \
  -H "X-API-Key: ffp_live_1234567890abcdef..." \
  -H "Content-Type: application/json"</pre>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- API Endpoints -->
    <div class="bg-white shadow-sm rounded-lg mb-8">
      <div class="px-6 py-5 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">API Endpoints</h2>
      </div>
      
      <!-- Forms Endpoints -->
      <div class="border-b border-gray-200">
        <button class="w-full px-6 py-4 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50" 
                data-controller="collapse" 
                data-action="click->collapse#toggle"
                data-collapse-target="trigger">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium bg-blue-100 text-blue-800 mr-3">
                Forms
              </span>
              <h3 class="text-base font-medium text-gray-900">Form Management</h3>
            </div>
            <svg class="h-5 w-5 text-gray-400 transform transition-transform" data-collapse-target="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </div>
        </button>
        <div class="hidden" data-collapse-target="content">
          <div class="px-6 py-4 bg-gray-50 space-y-6">
            <!-- GET /forms -->
            <div class="bg-white rounded-lg p-4">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                    GET
                  </span>
                  <code class="ml-3 text-sm font-mono text-gray-900">/forms</code>
                </div>
                <span class="text-sm text-gray-500">List all forms</span>
              </div>
              
              <div class="space-y-3">
                <div>
                  <h4 class="text-xs font-medium text-gray-700 uppercase tracking-wide mb-1">Parameters</h4>
                  <table class="min-w-full divide-y divide-gray-200">
                    <tbody class="divide-y divide-gray-200">
                      <tr>
                        <td class="py-2 text-sm font-mono text-gray-900">website_id</td>
                        <td class="py-2 text-sm text-gray-500">string</td>
                        <td class="py-2 text-sm text-gray-600">Filter by website ID</td>
                      </tr>
                      <tr>
                        <td class="py-2 text-sm font-mono text-gray-900">page</td>
                        <td class="py-2 text-sm text-gray-500">integer</td>
                        <td class="py-2 text-sm text-gray-600">Page number (default: 1)</td>
                      </tr>
                      <tr>
                        <td class="py-2 text-sm font-mono text-gray-900">per_page</td>
                        <td class="py-2 text-sm text-gray-500">integer</td>
                        <td class="py-2 text-sm text-gray-600">Items per page (default: 20, max: 100)</td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <div>
                  <h4 class="text-xs font-medium text-gray-700 uppercase tracking-wide mb-1">Response</h4>
                  <div class="bg-gray-900 rounded-md p-3 overflow-x-auto">
                    <pre class="text-green-400 text-xs">{
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "form_identifier": "contact-form",
      "name": "Contact Form",
      "url": "https://example.com/contact",
      "conversion_rate": 45.2,
      "abandonment_rate": 54.8,
      "total_sessions": 1523,
      "total_submissions": 689
    }
  ],
  "meta": {
    "current_page": 1,
    "total_pages": 5,
    "total_count": 92
  }
}</pre>
                  </div>
                </div>
              </div>
            </div>

            <!-- GET /forms/:id -->
            <div class="bg-white rounded-lg p-4">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                    GET
                  </span>
                  <code class="ml-3 text-sm font-mono text-gray-900">/forms/:id</code>
                </div>
                <span class="text-sm text-gray-500">Get form details</span>
              </div>
              
              <div class="space-y-3">
                <div>
                  <h4 class="text-xs font-medium text-gray-700 uppercase tracking-wide mb-1">Response</h4>
                  <div class="bg-gray-900 rounded-md p-3 overflow-x-auto">
                    <pre class="text-green-400 text-xs">{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "form_identifier": "contact-form",
  "name": "Contact Form",
  "url": "https://example.com/contact",
  "form_type": "contact",
  "field_count": 5,
  "conversion_rate": 45.2,
  "abandonment_rate": 54.8,
  "avg_completion_time": 124.5,
  "total_sessions": 1523,
  "total_submissions": 689,
  "fields": [
    {
      "name": "email",
      "field_type": "email",
      "required": true,
      "error_rate": 12.3,
      "abandonment_rate": 8.7
    }
  ],
  "insights": [
    {
      "type": "high_abandonment",
      "severity": "high",
      "title": "High abandonment on phone field",
      "recommendation": "Consider making the phone field optional"
    }
  ]
}</pre>
                  </div>
                </div>
              </div>
            </div>

            <!-- GET /forms/:id/sessions -->
            <div class="bg-white rounded-lg p-4">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                    GET
                  </span>
                  <code class="ml-3 text-sm font-mono text-gray-900">/forms/:id/sessions</code>
                </div>
                <span class="text-sm text-gray-500">Get form sessions</span>
              </div>
              
              <div class="space-y-3">
                <div>
                  <h4 class="text-xs font-medium text-gray-700 uppercase tracking-wide mb-1">Parameters</h4>
                  <table class="min-w-full divide-y divide-gray-200">
                    <tbody class="divide-y divide-gray-200">
                      <tr>
                        <td class="py-2 text-sm font-mono text-gray-900">status</td>
                        <td class="py-2 text-sm text-gray-500">string</td>
                        <td class="py-2 text-sm text-gray-600">Filter by status (completed, abandoned, in_progress)</td>
                      </tr>
                      <tr>
                        <td class="py-2 text-sm font-mono text-gray-900">date_from</td>
                        <td class="py-2 text-sm text-gray-500">datetime</td>
                        <td class="py-2 text-sm text-gray-600">Start date filter</td>
                      </tr>
                      <tr>
                        <td class="py-2 text-sm font-mono text-gray-900">date_to</td>
                        <td class="py-2 text-sm text-gray-500">datetime</td>
                        <td class="py-2 text-sm text-gray-600">End date filter</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Analytics Endpoints -->
      <div class="border-b border-gray-200">
        <button class="w-full px-6 py-4 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50" 
                data-controller="collapse" 
                data-action="click->collapse#toggle"
                data-collapse-target="trigger">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium bg-purple-100 text-purple-800 mr-3">
                Analytics
              </span>
              <h3 class="text-base font-medium text-gray-900">Analytics & Insights</h3>
            </div>
            <svg class="h-5 w-5 text-gray-400 transform transition-transform" data-collapse-target="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </div>
        </button>
        <div class="hidden" data-collapse-target="content">
          <div class="px-6 py-4 bg-gray-50 space-y-6">
            <!-- GET /analytics/overview -->
            <div class="bg-white rounded-lg p-4">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                    GET
                  </span>
                  <code class="ml-3 text-sm font-mono text-gray-900">/analytics/overview</code>
                </div>
                <span class="text-sm text-gray-500">Get analytics overview</span>
              </div>
              
              <div class="space-y-3">
                <div>
                  <h4 class="text-xs font-medium text-gray-700 uppercase tracking-wide mb-1">Response</h4>
                  <div class="bg-gray-900 rounded-md p-3 overflow-x-auto">
                    <pre class="text-green-400 text-xs">{
  "period": "last_30_days",
  "metrics": {
    "total_sessions": 15234,
    "total_submissions": 6892,
    "overall_conversion_rate": 45.2,
    "average_completion_time": 156.3,
    "top_abandonment_fields": [
      {
        "field_name": "phone",
        "abandonment_rate": 23.4
      }
    ]
  },
  "trends": {
    "sessions_change": 12.3,
    "submissions_change": 8.7,
    "conversion_rate_change": -2.1
  }
}</pre>
                  </div>
                </div>
              </div>
            </div>

            <!-- GET /insights -->
            <div class="bg-white rounded-lg p-4">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                    GET
                  </span>
                  <code class="ml-3 text-sm font-mono text-gray-900">/insights</code>
                </div>
                <span class="text-sm text-gray-500">Get AI-generated insights</span>
              </div>
              
              <div class="space-y-3">
                <div>
                  <h4 class="text-xs font-medium text-gray-700 uppercase tracking-wide mb-1">Parameters</h4>
                  <table class="min-w-full divide-y divide-gray-200">
                    <tbody class="divide-y divide-gray-200">
                      <tr>
                        <td class="py-2 text-sm font-mono text-gray-900">severity</td>
                        <td class="py-2 text-sm text-gray-500">string</td>
                        <td class="py-2 text-sm text-gray-600">Filter by severity (critical, high, medium, low)</td>
                      </tr>
                      <tr>
                        <td class="py-2 text-sm font-mono text-gray-900">status</td>
                        <td class="py-2 text-sm text-gray-500">string</td>
                        <td class="py-2 text-sm text-gray-600">Filter by status (open, resolved)</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Webhooks -->
      <div class="border-b border-gray-200">
        <button class="w-full px-6 py-4 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50" 
                data-controller="collapse" 
                data-action="click->collapse#toggle"
                data-collapse-target="trigger">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium bg-yellow-100 text-yellow-800 mr-3">
                Webhooks
              </span>
              <h3 class="text-base font-medium text-gray-900">Webhook Events</h3>
            </div>
            <svg class="h-5 w-5 text-gray-400 transform transition-transform" data-collapse-target="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </div>
        </button>
        <div class="hidden" data-collapse-target="content">
          <div class="px-6 py-4 bg-gray-50">
            <div class="prose max-w-none">
              <p class="text-sm text-gray-600">Configure webhooks to receive real-time notifications about form events.</p>
              
              <div class="mt-4 space-y-4">
                <div>
                  <h4 class="text-sm font-medium text-gray-900">Available Events</h4>
                  <ul class="mt-2 space-y-2 text-sm text-gray-600">
                    <li class="flex items-start">
                      <code class="text-xs bg-gray-100 px-2 py-1 rounded">form.submission</code>
                      <span class="ml-3">Triggered when a form is submitted</span>
                    </li>
                    <li class="flex items-start">
                      <code class="text-xs bg-gray-100 px-2 py-1 rounded">form.abandoned</code>
                      <span class="ml-3">Triggered when a form session is abandoned</span>
                    </li>
                    <li class="flex items-start">
                      <code class="text-xs bg-gray-100 px-2 py-1 rounded">insight.created</code>
                      <span class="ml-3">Triggered when a new insight is generated</span>
                    </li>
                  </ul>
                </div>

                <div>
                  <h4 class="text-sm font-medium text-gray-900">Webhook Payload Example</h4>
                  <div class="bg-gray-900 rounded-md p-3 overflow-x-auto mt-2">
                    <pre class="text-green-400 text-xs">{
  "event": "form.submission",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "form_id": "550e8400-e29b-41d4-a716-446655440000",
    "session_id": "660e8400-e29b-41d4-a716-446655440001",
    "completion_time": 145.2,
    "field_data": {
      "email": "<EMAIL>",
      "name": "John Doe"
    }
  }
}</pre>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error Codes -->
    <div class="bg-white shadow-sm rounded-lg mb-8">
      <div class="px-6 py-5 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">Error Codes</h2>
      </div>
      <div class="px-6 py-4">
        <table class="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">200</td>
              <td class="px-6 py-4 text-sm text-gray-600">Success</td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">400</td>
              <td class="px-6 py-4 text-sm text-gray-600">Bad Request - Invalid parameters</td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">401</td>
              <td class="px-6 py-4 text-sm text-gray-600">Unauthorized - Invalid or missing API key</td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">403</td>
              <td class="px-6 py-4 text-sm text-gray-600">Forbidden - Insufficient permissions</td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">404</td>
              <td class="px-6 py-4 text-sm text-gray-600">Not Found - Resource not found</td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">429</td>
              <td class="px-6 py-4 text-sm text-gray-600">Too Many Requests - Rate limit exceeded</td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">500</td>
              <td class="px-6 py-4 text-sm text-gray-600">Internal Server Error</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- SDKs & Libraries -->
    <div class="bg-white shadow-sm rounded-lg">
      <div class="px-6 py-5 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">SDKs & Libraries</h2>
      </div>
      <div class="px-6 py-4">
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <svg class="h-10 w-10 text-yellow-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm-.28 19.243c-.687.687-1.813.687-2.5 0L4.72 14.72c-.687-.687-.687-1.813 0-2.5.687-.687 1.813-.687 2.5 0l2.72 2.72 7.28-7.28c.687-.687 1.813-.687 2.5 0s.687 1.813 0 2.5l-8 8.083z"/>
                </svg>
              </div>
              <div>
                <h3 class="text-sm font-medium text-gray-900">JavaScript/TypeScript</h3>
                <p class="mt-1 text-sm text-gray-500">npm install @formflowpro/sdk</p>
              </div>
            </div>
          </div>

          <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <svg class="h-10 w-10 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z"/>
                </svg>
              </div>
              <div>
                <h3 class="text-sm font-medium text-gray-900">Python</h3>
                <p class="mt-1 text-sm text-gray-500">pip install formflowpro</p>
              </div>
            </div>
          </div>

          <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <svg class="h-10 w-10 text-red-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2z"/>
                </svg>
              </div>
              <div>
                <h3 class="text-sm font-medium text-gray-900">Ruby</h3>
                <p class="mt-1 text-sm text-gray-500">gem install formflowpro</p>
              </div>
            </div>
          </div>

          <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <svg class="h-10 w-10 text-purple-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0z"/>
                </svg>
              </div>
              <div>
                <h3 class="text-sm font-medium text-gray-900">PHP</h3>
                <p class="mt-1 text-sm text-gray-500">composer require formflowpro/sdk</p>
              </div>
            </div>
          </div>

          <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <svg class="h-10 w-10 text-green-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0z"/>
                </svg>
              </div>
              <div>
                <h3 class="text-sm font-medium text-gray-900">Go</h3>
                <p class="mt-1 text-sm text-gray-500">go get github.com/formflowpro/sdk</p>
              </div>
            </div>
          </div>

          <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <svg class="h-10 w-10 text-orange-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0z"/>
                </svg>
              </div>
              <div>
                <h3 class="text-sm font-medium text-gray-900">Java</h3>
                <p class="mt-1 text-sm text-gray-500">Maven: com.formflowpro:sdk</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Simple collapse controller for expandable sections
document.addEventListener('DOMContentLoaded', function() {
  const triggers = document.querySelectorAll('[data-controller="collapse"]');
  
  triggers.forEach(trigger => {
    trigger.addEventListener('click', function() {
      const content = this.nextElementSibling;
      const icon = this.querySelector('[data-collapse-target="icon"]');
      
      if (content.classList.contains('hidden')) {
        content.classList.remove('hidden');
        icon.classList.add('rotate-180');
      } else {
        content.classList.add('hidden');
        icon.classList.remove('rotate-180');
      }
    });
  });
});
</script>