<!-- Insights Header -->
<header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 sm:px-6 lg:px-8 py-4">
  <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
    <div>
      <h1 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">AI Insights</h1>
      <p class="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mt-1">Intelligent recommendations to improve your form performance</p>
    </div>
    
    <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4">
      <!-- Filters -->
      <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3">
        <%= form_with url: insights_path, method: :get, local: true, class: "flex items-center gap-3" do |f| %>
          <%= f.select :severity, 
              options_for_select([
                ['All Severities', 'all'],
                ['Critical', 'critical'],
                ['High', 'high'],
                ['Medium', 'medium'],
                ['Low', 'low']
              ], params[:severity]), 
              {}, 
              { class: "px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white", 
                onchange: "this.form.submit();" } %>
          
          <%= f.select :status, 
              options_for_select([
                ['All Status', 'all'],
                ['Unresolved', 'unresolved'],
                ['Resolved', 'resolved'],
                ['Dismissed', 'dismissed']
              ], params[:status]), 
              {}, 
              { class: "px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white", 
                onchange: "this.form.submit();" } %>
          
          <% if @forms_for_filter.any? %>
            <%= f.select :form_id, 
                options_from_collection_for_select(@forms_for_filter, :id, :name, params[:form_id]), 
                { prompt: 'All Forms' }, 
                { class: "px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white", 
                  onchange: "this.form.submit();" } %>
          <% end %>
        <% end %>
      </div>
      
      <!-- Actions -->
      <div class="flex items-center gap-2 mt-2 sm:mt-0">
        <%= button_to "Bulk Resolve", bulk_resolve_insights_path, 
            method: :patch,
            form_class: "inline",
            class: "px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",
            data: { confirm: "Are you sure you want to resolve selected insights?" } %>
        
        <%= link_to export_insights_path(format: :csv), 
            class: "px-5 py-2.5 bg-gradient-to-r from-violet-500 to-indigo-500 text-white rounded-lg font-medium hover:from-violet-600 hover:to-indigo-600 transition-all shadow-lg flex items-center gap-2" do %>
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Export
        <% end %>
      </div>
    </div>
  </div>
</header>

<!-- Insights Content -->
<div class="flex-1 overflow-auto bg-gray-50 dark:bg-gray-900">
  <div class="p-4 sm:p-6 lg:p-8">
    
    <!-- Insights Overview -->
    <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4 lg:gap-6 mb-8">
      <!-- Total Insights -->
      <div class="col-span-2 sm:col-span-1 bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="p-3 bg-gray-100 dark:bg-gray-700 rounded-xl">
            <svg class="w-6 h-6 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
        </div>
        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">
          <%= @insights_by_severity.values.sum %>
        </h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Total Insights</p>
      </div>
      
      <!-- Critical Insights -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="p-3 bg-red-100 dark:bg-red-900/30 rounded-xl">
            <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>
          </div>
          <span class="text-xs font-medium text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 px-2 py-1 rounded-full">
            Critical
          </span>
        </div>
        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">
          <%= @insights_by_severity[:critical] %>
        </h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Critical Issues</p>
      </div>
      
      <!-- High Priority -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-xl">
            <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>
          </div>
          <span class="text-xs font-medium text-orange-600 dark:text-orange-400 bg-orange-100 dark:bg-orange-900/30 px-2 py-1 rounded-full">
            High
          </span>
        </div>
        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">
          <%= @insights_by_severity[:high] %>
        </h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">High Priority</p>
      </div>
      
      <!-- Medium Priority -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-xl">
            <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
            </svg>
          </div>
          <span class="text-xs font-medium text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30 px-2 py-1 rounded-full">
            Medium
          </span>
        </div>
        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">
          <%= @insights_by_severity[:medium] %>
        </h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Medium Priority</p>
      </div>
      
      <!-- Low Priority -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
            </svg>
          </div>
          <span class="text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 px-2 py-1 rounded-full">
            Low
          </span>
        </div>
        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">
          <%= @insights_by_severity[:low] %>
        </h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Low Priority</p>
      </div>
    </div>

    <!-- Insight Categories Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 mb-8">
      
      <!-- Categories Breakdown -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Insight Categories</h2>
        
        <div class="space-y-3">
          <% @insight_categories.each do |category, count| %>
            <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-xl">
              <div class="flex items-center gap-3">
                <div class="w-3 h-3 bg-gradient-to-r from-violet-500 to-indigo-500 rounded-full"></div>
                <span class="text-sm font-medium text-gray-900 dark:text-white"><%= category %></span>
              </div>
              <span class="text-sm font-semibold text-gray-600 dark:text-gray-400"><%= count %></span>
            </div>
          <% end %>
        </div>
      </div>
      
      <!-- Recent Activity -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Insights</h2>
        
        <div class="space-y-3">
          <% if @recent_insights.any? %>
            <% @recent_insights.each do |insight| %>
              <div class="flex items-start gap-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-700/50 rounded-xl transition-colors">
                <div class="mt-1.5">
                  <div class="w-2 h-2 <%= insight.severity == 'critical' ? 'bg-red-500' : insight.severity == 'high' ? 'bg-orange-500' : insight.severity == 'medium' ? 'bg-yellow-500' : 'bg-blue-500' %> rounded-full"></div>
                </div>
                <div class="flex-1">
                  <p class="text-sm font-medium text-gray-900 dark:text-white">
                    <%= insight.title %>
                  </p>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <%= insight.form.website.name %> • <%= insight.form.name %>
                  </p>
                  <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                    <%= time_ago_in_words(insight.created_at) %> ago
                  </p>
                </div>
              </div>
            <% end %>
          <% else %>
            <p class="text-center text-gray-500 dark:text-gray-400 py-8">No recent insights</p>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Insights List -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700">
      <div class="px-4 sm:px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">All Insights</h2>
      </div>
      
      <div class="divide-y divide-gray-200 dark:divide-gray-700">
        <% if @paginated_insights.any? %>
          <% @paginated_insights.each do |insight| %>
            <div class="p-4 sm:p-6 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
              <div class="flex items-start justify-between">
                <div class="flex items-start gap-4">
                  <!-- Severity Indicator -->
                  <div class="flex-shrink-0 mt-1">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= 
                      case insight.severity
                      when 'critical' then 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400'
                      when 'high' then 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-400'
                      when 'medium' then 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400'
                      else 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400'
                      end %>">
                      <%= insight.severity.capitalize %>
                    </span>
                  </div>
                  
                  <!-- Insight Content -->
                  <div class="flex-1">
                    <div class="flex items-center gap-2 mb-2">
                      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                        <%= insight.title %>
                      </h3>
                      <% if insight.resolved_at %>
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400">
                          Resolved
                        </span>
                      <% elsif insight.dismissed_at %>
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-400">
                          Dismissed
                        </span>
                      <% end %>
                    </div>
                    
                    <p class="text-gray-600 dark:text-gray-300 mb-3">
                      <%= insight.description %>
                    </p>
                    
                    <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                      <span class="flex items-center gap-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m-9 9a9 9 0 019-9"></path>
                        </svg>
                        <%= insight.form.website.name %>
                      </span>
                      <span class="flex items-center gap-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <%= insight.form.name %>
                      </span>
                      <span class="flex items-center gap-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <%= time_ago_in_words(insight.created_at) %> ago
                      </span>
                    </div>
                  </div>
                </div>
                
                <!-- Actions -->
                <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 mt-3 sm:mt-0 sm:ml-4">
                  <%= link_to insight_path(insight), 
                      class: "px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-md transition-colors" do %>
                    View Details
                  <% end %>
                  
                  <% unless insight.resolved_at %>
                    <%= button_to "Resolve", resolve_insight_path(insight), 
                        method: :patch,
                        form_class: "inline",
                        class: "px-3 py-2 text-sm font-medium text-green-700 dark:text-green-400 hover:bg-green-100 dark:hover:bg-green-900/30 rounded-md transition-colors" %>
                  <% end %>
                  
                  <% unless insight.dismissed_at %>
                    <%= button_to "Dismiss", dismiss_insight_path(insight), 
                        method: :patch,
                        form_class: "inline",
                        class: "px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-md transition-colors" %>
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>
        <% else %>
          <!-- Empty State -->
          <div class="p-12 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">No insights found</h3>
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
              <% if params[:severity] != 'all' || params[:status] != 'all' || params[:form_id].present? %>
                Try adjusting your filters to see more insights.
              <% else %>
                AI-generated insights will appear here as your forms collect data.
              <% end %>
            </p>
          </div>
        <% end %>
      </div>
      
      <!-- Pagination -->
      <% if @paginated_insights.respond_to?(:current_page) && @paginated_insights.total_pages > 1 %>
        <div class="px-4 sm:px-6 py-4 border-t border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500 dark:text-gray-400">
              Showing <%= (@paginated_insights.current_page - 1) * @paginated_insights.limit_value + 1 %>-<%= [@paginated_insights.current_page * @paginated_insights.limit_value, @paginated_insights.total_count].min %> 
              of <%= @paginated_insights.total_count %> insights
            </div>
            
            <div class="flex gap-2">
              <% if @paginated_insights.prev_page %>
                <%= link_to insights_path(page: @paginated_insights.prev_page), 
                    class: "px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" do %>
                  Previous
                <% end %>
              <% end %>
              
              <% if @paginated_insights.next_page %>
                <%= link_to insights_path(page: @paginated_insights.next_page), 
                    class: "px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" do %>
                  Next
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    
  </div>
</div>