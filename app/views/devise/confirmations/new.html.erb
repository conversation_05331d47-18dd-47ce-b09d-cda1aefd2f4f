<% content_for :title, "Resend Confirmation - FormFlow Pro" %>

<div>
  <h2 class="text-3xl font-bold text-white mb-2">Confirm your email</h2>
  <p class="text-gray-400 mb-8">We'll send you a confirmation link to verify your email address</p>

  <%= form_for(resource, as: resource_name, url: confirmation_path(resource_name), html: { method: :post, class: "space-y-6" }) do |f| %>
    <%= render "devise/shared/error_messages", resource: resource %>

    <!-- Email Field -->
    <div>
      <%= f.label :email, class: "block text-sm font-medium text-gray-300 mb-2" %>
      <%= f.email_field :email, 
          autofocus: true, 
          autocomplete: "email",
          value: (resource.pending_reconfirmation? ? resource.unconfirmed_email : resource.email),
          placeholder: "<EMAIL>",
          class: "appearance-none block w-full px-4 py-3 bg-black/50 border border-gray-700 rounded-lg shadow-sm placeholder-gray-500 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all" %>
    </div>

    <!-- Submit Button -->
    <div>
      <%= f.submit "Resend Confirmation Instructions", 
          class: "w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 focus:ring-offset-gray-900 cursor-pointer transition-all transform hover:scale-[1.02] hover:shadow-lg" %>
    </div>
  <% end %>

  <!-- Help Section -->
  <div class="mt-6 p-4 bg-blue-500/10 backdrop-blur-sm border border-blue-500/20 rounded-lg">
    <div class="flex">
      <svg class="h-5 w-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
      </svg>
      <div class="text-sm text-gray-300">
        <p class="font-medium text-white mb-1">Didn't receive the email?</p>
        <p>Check your spam folder or 
          <a href="mailto:<EMAIL>" class="text-purple-400 hover:text-purple-300 underline transition">contact support</a>
          if you continue to have issues.
        </p>
      </div>
    </div>
  </div>

  <!-- Navigation Links -->
  <div class="mt-6 text-center">
    <span class="text-sm text-gray-400">
      Already confirmed? 
      <%= link_to "Sign in", new_session_path(resource_name), class: "font-medium text-purple-400 hover:text-purple-300 transition" %>
    </span>
  </div>

  <div class="mt-2 text-center">
    <span class="text-sm text-gray-400">
      Need a new account? 
      <%= link_to "Sign up", new_registration_path(resource_name), class: "font-medium text-purple-400 hover:text-purple-300 transition" %>
    </span>
  </div>
</div>