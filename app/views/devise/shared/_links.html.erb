<div class="space-y-2 text-center">
  <%- if controller_name != 'sessions' %>
    <p class="text-sm text-gray-600">
      Already have an account? 
      <%= link_to "Sign in", new_session_path(resource_name), class: "font-medium text-violet-600 hover:text-violet-500 transition-colors duration-200" %>
    </p>
  <% end %>

  <%- if devise_mapping.registerable? && controller_name != 'registrations' %>
    <p class="text-sm text-gray-600">
      Need an account? 
      <%= link_to "Sign up", new_registration_path(resource_name), class: "font-medium text-violet-600 hover:text-violet-500 transition-colors duration-200" %>
    </p>
  <% end %>

  <%- if devise_mapping.recoverable? && controller_name != 'passwords' && controller_name != 'registrations' %>
    <p class="text-sm text-gray-600">
      <%= link_to "Forgot your password?", new_password_path(resource_name), class: "font-medium text-violet-600 hover:text-violet-500 transition-colors duration-200" %>
    </p>
  <% end %>

  <%- if devise_mapping.confirmable? && controller_name != 'confirmations' %>
    <p class="text-sm text-gray-600">
      <%= link_to "Didn't receive confirmation instructions?", new_confirmation_path(resource_name), class: "font-medium text-violet-600 hover:text-violet-500 transition-colors duration-200" %>
    </p>
  <% end %>

  <%- if devise_mapping.lockable? && resource_class.unlock_strategy_enabled?(:email) && controller_name != 'unlocks' %>
    <p class="text-sm text-gray-600">
      <%= link_to "Didn't receive unlock instructions?", new_unlock_path(resource_name), class: "font-medium text-violet-600 hover:text-violet-500 transition-colors duration-200" %>
    </p>
  <% end %>

  <%- if devise_mapping.omniauthable? %>
    <div class="mt-4 space-y-2">
      <%- resource_class.omniauth_providers.each do |provider| %>
        <div>
          <%= button_to "Sign in with #{OmniAuth::Utils.camelize(provider)}", 
              omniauth_authorize_path(resource_name, provider), 
              data: { turbo: false },
              class: "w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-500 transition-all duration-200" %>
        </div>
      <% end %>
    </div>
  <% end %>
</div>
