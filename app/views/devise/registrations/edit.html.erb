<% content_for :title, "Edit Profile - FormFlow Pro" %>

<div class="min-h-screen bg-gray-50 py-12">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
      <div class="flex items-center space-x-3">
        <%= link_to settings_profile_path, class: "text-gray-400 hover:text-gray-600" do %>
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        <% end %>
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Edit Profile</h1>
          <p class="mt-1 text-sm text-gray-600">Manage your account information and security settings</p>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Profile Form -->
      <div class="lg:col-span-2">
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Personal Information</h2>
            <p class="mt-1 text-sm text-gray-600">Update your personal details and contact information.</p>
          </div>
          
          <%= form_for(resource, as: resource_name, url: registration_path(resource_name), html: { method: :put, class: "p-6 space-y-6", data: { turbo: false } }) do |f| %>
            <%= render "devise/shared/error_messages", resource: resource %>

            <!-- Name Fields -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div>
                <%= f.label :first_name, class: "block text-sm font-medium text-gray-700 mb-2" %>
                <%= f.text_field :first_name, 
                    class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent", 
                    autocomplete: "given-name" %>
              </div>
              
              <div>
                <%= f.label :last_name, class: "block text-sm font-medium text-gray-700 mb-2" %>
                <%= f.text_field :last_name, 
                    class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent", 
                    autocomplete: "family-name" %>
              </div>
            </div>

            <!-- Email Field -->
            <div>
              <%= f.label :email, class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= f.email_field :email, 
                  autofocus: true, 
                  autocomplete: "email",
                  class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" %>
              
              <% if devise_mapping.confirmable? && resource.pending_reconfirmation? %>
                <div class="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <div class="flex">
                    <div class="flex-shrink-0">
                      <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                      </svg>
                    </div>
                    <div class="ml-3">
                      <p class="text-sm text-yellow-700">
                        Currently waiting confirmation for: <strong><%= resource.unconfirmed_email %></strong>
                      </p>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>

            <!-- Current Password (Required for Changes) -->
            <div>
              <%= f.label :current_password, "Current Password", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= f.password_field :current_password, 
                  autocomplete: "current-password",
                  class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                  placeholder: "Enter your current password to confirm changes" %>
              <p class="mt-2 text-sm text-gray-500">We need your current password to confirm your changes</p>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-between pt-4">
              <%= link_to "Cancel", :back, class: "px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" %>
              <%= f.submit "Update Profile", class: "px-6 py-2 bg-blue-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed" %>
            </div>
          <% end %>
        </div>

        <!-- Security Settings Card -->
        <div class="mt-8 bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Security Settings</h2>
            <p class="mt-1 text-sm text-gray-600">Update your password and security preferences.</p>
          </div>
          
          <%= form_for(resource, as: resource_name, url: registration_path(resource_name), html: { method: :put, class: "p-6 space-y-6", data: { turbo: false } }) do |f| %>
            <!-- Password Fields -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div>
                <%= f.label :password, "New Password", class: "block text-sm font-medium text-gray-700 mb-2" %>
                <%= f.password_field :password, 
                    autocomplete: "new-password",
                    class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    placeholder: "Leave blank if you don't want to change" %>
                <% if @minimum_password_length %>
                  <p class="mt-2 text-sm text-gray-500">Minimum <%= @minimum_password_length %> characters</p>
                <% end %>
              </div>
              
              <div>
                <%= f.label :password_confirmation, "Confirm New Password", class: "block text-sm font-medium text-gray-700 mb-2" %>
                <%= f.password_field :password_confirmation, 
                    autocomplete: "new-password",
                    class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    placeholder: "Confirm your new password" %>
              </div>
            </div>

            <!-- Current Password for Security Changes -->
            <div>
              <%= f.label :current_password, "Current Password", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= f.password_field :current_password, 
                  autocomplete: "current-password",
                  class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                  placeholder: "Enter current password to update security settings" %>
              <p class="mt-2 text-sm text-gray-500">Required to change your password</p>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-between pt-4">
              <div class="text-sm text-gray-500">
                <svg class="inline w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
                Only fill these fields if you want to change your password
              </div>
              <%= f.submit "Update Password", class: "px-6 py-2 bg-green-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2" %>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="lg:col-span-1">
        <!-- Account Info Card -->
        <div class="bg-white shadow rounded-lg p-6 mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Account Information</h3>
          <dl class="space-y-3">
            <div>
              <dt class="text-sm font-medium text-gray-500">Full Name</dt>
              <dd class="text-sm text-gray-900"><%= current_user.full_name %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Email</dt>
              <dd class="text-sm text-gray-900"><%= current_user.email %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Role</dt>
              <dd class="text-sm text-gray-900">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <%= current_user.role&.titleize || "Member" %>
                </span>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Member Since</dt>
              <dd class="text-sm text-gray-900"><%= current_user.created_at.strftime("%B %Y") %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Last Sign In</dt>
              <dd class="text-sm text-gray-900">
                <%= current_user.last_sign_in_at&.strftime("%b %d, %Y at %I:%M %p") || "Never" %>
              </dd>
            </div>
          </dl>
        </div>

        <!-- Security Features Card -->
        <div class="bg-white shadow rounded-lg p-6 mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Security Features</h3>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-900">Email Confirmation</p>
                <p class="text-sm text-gray-500">Verify email changes</p>
              </div>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                Enabled
              </span>
            </div>
            
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-900">Account Lockout</p>
                <p class="text-sm text-gray-500">Protection against brute force</p>
              </div>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                Enabled
              </span>
            </div>

            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-900">Session Tracking</p>
                <p class="text-sm text-gray-500">Monitor sign-in activity</p>
              </div>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                Enabled
              </span>
            </div>
          </div>
        </div>

        <!-- Danger Zone Card -->
        <div class="bg-white shadow rounded-lg p-6 border-l-4 border-red-400">
          <h3 class="text-lg font-medium text-red-900 mb-4">Danger Zone</h3>
          <div class="space-y-4">
            <div>
              <h4 class="text-sm font-medium text-gray-900">Delete Account</h4>
              <p class="text-sm text-gray-500 mt-1">
                Permanently delete your account and all associated data. This action cannot be undone.
              </p>
              
              <%= button_to "Delete Account", 
                  registration_path(resource_name), 
                  method: :delete,
                  data: { 
                    confirm: "Are you absolutely sure? This will permanently delete your account and all associated data. This action cannot be undone.",
                    turbo_confirm: "Type 'DELETE' to confirm account deletion"
                  },
                  class: "mt-3 w-full inline-flex justify-center py-2 px-4 border border-red-300 shadow-sm bg-red-600 text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded-md" %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Toast Notifications (for success/error messages) -->
<div id="toast-container" class="fixed top-4 right-4 z-50 space-y-4">
  <!-- Toast messages will be inserted here by JavaScript -->
</div>

<script>
  // Auto-hide flash messages after 5 seconds
  document.addEventListener('DOMContentLoaded', function() {
    const flashMessages = document.querySelectorAll('.flash-message');
    flashMessages.forEach(function(message) {
      setTimeout(function() {
        message.style.opacity = '0';
        setTimeout(function() {
          message.remove();
        }, 300);
      }, 5000);
    });
  });
  
  // Form validation feedback
  document.querySelectorAll('input[type="password"]').forEach(function(input) {
    input.addEventListener('input', function() {
      if (input.name === 'user[password]' && input.value.length > 0) {
        const confirmField = document.querySelector('input[name="user[password_confirmation]"]');
        if (confirmField.value.length > 0 && input.value !== confirmField.value) {
          confirmField.setCustomValidity('Passwords do not match');
        } else {
          confirmField.setCustomValidity('');
        }
      }
    });
  });
</script>
