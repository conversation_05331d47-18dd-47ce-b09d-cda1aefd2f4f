<% content_for :title, "Sign In - FormFlow Pro" %>

<div>
  <h2 class="text-3xl font-bold text-white mb-2">Welcome back</h2>
  <p class="text-gray-400 mb-8">Sign in to continue to your dashboard</p>

  <%= form_for(resource, as: resource_name, url: session_path(resource_name), html: { class: "space-y-6" }) do |f| %>
    <!-- Google Sign In Button -->
    <button type="button" class="w-full flex items-center justify-center px-4 py-3 bg-white/10 backdrop-blur-sm border border-gray-700 rounded-lg shadow-sm text-sm font-medium text-white hover:bg-white/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 focus:ring-offset-gray-900 transition-all">
      <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
      </svg>
      Sign in with Google
    </button>

    <div class="relative">
      <div class="absolute inset-0 flex items-center">
        <div class="w-full border-t border-gray-700"></div>
      </div>
      <div class="relative flex justify-center text-sm">
        <span class="px-2 bg-gray-900/50 text-gray-400">or sign in with email</span>
      </div>
    </div>

    <!-- Email Field -->
    <div>
      <%= f.label :email, class: "block text-sm font-medium text-gray-300 mb-2" %>
      <%= f.email_field :email, 
          autofocus: true, 
          autocomplete: "email",
          placeholder: "<EMAIL>",
          class: "appearance-none block w-full px-4 py-3 bg-black/50 border border-gray-700 rounded-lg shadow-sm placeholder-gray-500 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all" %>
    </div>

    <!-- Password Field -->
    <div>
      <%= f.label :password, class: "block text-sm font-medium text-gray-300 mb-2" %>
      <div class="relative">
        <%= f.password_field :password, 
            autocomplete: "current-password",
            placeholder: "••••••••••••",
            class: "appearance-none block w-full px-4 py-3 bg-black/50 border border-gray-700 rounded-lg shadow-sm placeholder-gray-500 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all pr-12",
            "data-password-field": true %>
        <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePassword(this)">
          <svg class="h-5 w-5 text-gray-400 hover:text-gray-300 transition" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-eye-open>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
          <svg class="h-5 w-5 text-gray-400 hover:text-gray-300 transition hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-eye-closed>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Remember Me and Forgot Password -->
    <div class="flex items-center justify-between">
      <% if devise_mapping.rememberable? %>
        <div class="flex items-center">
          <%= f.check_box :remember_me, class: "h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-600 rounded bg-black/50" %>
          <%= f.label :remember_me, class: "ml-2 block text-sm text-gray-300" %>
        </div>
      <% end %>
      
      <%= link_to "Forgot Password?", new_password_path(resource_name), class: "text-sm text-purple-400 hover:text-purple-300 font-medium transition" %>
    </div>

    <!-- Submit Button -->
    <div>
      <%= f.submit "Sign In", 
          class: "w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 focus:ring-offset-gray-900 cursor-pointer transition-all transform hover:scale-[1.02] hover:shadow-lg" %>
    </div>
  <% end %>

  <!-- Sign Up Link -->
  <div class="mt-6 text-center">
    <span class="text-sm text-gray-400">
      Don't have an account? 
      <%= link_to "Create one now", new_registration_path(resource_name), class: "font-medium text-purple-400 hover:text-purple-300 transition" %>
    </span>
  </div>
</div>

<script>
  function togglePassword(button) {
    const input = button.parentElement.querySelector('input[data-password-field]');
    const eyeOpen = button.querySelector('[data-eye-open]');
    const eyeClosed = button.querySelector('[data-eye-closed]');
    
    if (input.getAttribute('type') === 'password') {
      input.setAttribute('type', 'text');
      eyeOpen.classList.add('hidden');
      eyeClosed.classList.remove('hidden');
    } else {
      input.setAttribute('type', 'password');
      eyeOpen.classList.remove('hidden');
      eyeClosed.classList.add('hidden');
    }
  }
</script>