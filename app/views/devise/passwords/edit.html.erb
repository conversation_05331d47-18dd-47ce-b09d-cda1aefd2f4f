<% content_for :title, "Set New Password - FormFlow Pro" %>

<div>
  <h2 class="text-3xl font-bold text-white mb-2">Set your new password</h2>
  <p class="text-gray-400 mb-8">Choose a strong password to secure your account</p>
  <%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :put, class: "space-y-6" }) do |f| %>
    <%= f.hidden_field :reset_password_token %>
    <%= render "devise/shared/error_messages", resource: resource %>

    <!-- New Password Field -->
    <div>
      <%= f.label :password, "New Password", class: "block text-sm font-medium text-gray-300 mb-2" %>
      <% if @minimum_password_length %>
        <p class="text-xs text-gray-500 mb-2">(<%= @minimum_password_length %> characters minimum)</p>
      <% end %>
      <div class="relative">
        <%= f.password_field :password, 
            autofocus: true, 
            autocomplete: "new-password",
            placeholder: "••••••••••••",
            class: "appearance-none block w-full px-4 py-3 bg-black/50 border border-gray-700 rounded-lg shadow-sm placeholder-gray-500 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all pr-12",
            "data-password-field": true %>
        <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePassword(this)">
          <svg class="h-5 w-5 text-gray-400 hover:text-gray-300 transition" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-eye-open>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
          <svg class="h-5 w-5 text-gray-400 hover:text-gray-300 transition hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-eye-closed>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Confirm Password Field -->
    <div>
      <%= f.label :password_confirmation, "Confirm New Password", class: "block text-sm font-medium text-gray-300 mb-2" %>
      <div class="relative">
        <%= f.password_field :password_confirmation, 
            autocomplete: "new-password",
            placeholder: "••••••••••••",
            class: "appearance-none block w-full px-4 py-3 bg-black/50 border border-gray-700 rounded-lg shadow-sm placeholder-gray-500 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all pr-12",
            "data-password-field": true %>
        <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePassword(this)">
          <svg class="h-5 w-5 text-gray-400 hover:text-gray-300 transition" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-eye-open>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
          <svg class="h-5 w-5 text-gray-400 hover:text-gray-300 transition hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-eye-closed>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Password Requirements -->
    <div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-4 border border-gray-700">
      <h4 class="text-sm font-medium text-gray-300 mb-2">Password Requirements:</h4>
      <ul class="text-xs text-gray-400 space-y-1">
        <li class="flex items-center">
          <svg class="h-3 w-3 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
          At least <%= @minimum_password_length || 8 %> characters long
        </li>
        <li class="flex items-center">
          <svg class="h-3 w-3 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
          Mix of uppercase and lowercase letters
        </li>
        <li class="flex items-center">
          <svg class="h-3 w-3 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
          At least one number
        </li>
        <li class="flex items-center">
          <svg class="h-3 w-3 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
          At least one special character
        </li>
      </ul>
    </div>

    <!-- Submit Button -->
    <div>
      <%= f.submit "Update Password", 
          class: "w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 focus:ring-offset-gray-900 cursor-pointer transition-all transform hover:scale-[1.02] hover:shadow-lg" %>
    </div>
  <% end %>

  <!-- Security Notice -->
  <div class="mt-6 p-4 bg-green-500/10 backdrop-blur-sm border border-green-500/20 rounded-lg">
    <div class="flex">
      <svg class="h-5 w-5 text-green-400 mr-2 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
      </svg>
      <div class="text-sm text-gray-300">
        <p class="font-medium text-white mb-1">Security Notice</p>
        <p>Once you update your password, you'll be automatically signed in and all other sessions will be invalidated for your security.</p>
      </div>
    </div>
  </div>

  <!-- Navigation Links -->
  <div class="mt-6 text-center">
    <span class="text-sm text-gray-400">
      Remember your password? 
      <%= link_to "Sign in", new_session_path(resource_name), class: "font-medium text-purple-400 hover:text-purple-300 transition" %>
    </span>
  </div>
</div>

<script>
  function togglePassword(button) {
    const input = button.parentElement.querySelector('input[data-password-field]');
    const eyeOpen = button.querySelector('[data-eye-open]');
    const eyeClosed = button.querySelector('[data-eye-closed]');
    
    if (input.getAttribute('type') === 'password') {
      input.setAttribute('type', 'text');
      eyeOpen.classList.add('hidden');
      eyeClosed.classList.remove('hidden');
    } else {
      input.setAttribute('type', 'password');
      eyeOpen.classList.remove('hidden');
      eyeClosed.classList.add('hidden');
    }
  }
</script>
