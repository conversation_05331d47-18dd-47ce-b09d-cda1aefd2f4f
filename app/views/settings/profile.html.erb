<% content_for :title, "Profile Settings - FormFlow Pro" %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
      <nav class="flex mb-4" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-2">
          <li>
            <%= link_to settings_path, class: "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300" do %>
              Settings
            <% end %>
          </li>
          <li class="flex items-center">
            <svg class="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <span class="ml-2 text-gray-900 dark:text-white">Profile</span>
          </li>
        </ol>
      </nav>
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Profile Settings</h1>
      <p class="mt-2 text-gray-600 dark:text-gray-400">Manage your personal information and preferences</p>
    </div>

    <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
      <%= form_with url: settings_profile_path, method: :patch, local: true, class: "divide-y divide-gray-200 dark:divide-gray-700" do |form| %>
        
        <!-- Personal Information -->
        <div class="p-6">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Personal Information</h2>
          
          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <label for="first_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                First Name
              </label>
              <input type="text" name="user[first_name]" id="first_name" 
                     value="<%= current_user.first_name %>"
                     class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-violet-500 focus:ring-violet-500 sm:text-sm">
            </div>

            <div>
              <label for="last_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Last Name
              </label>
              <input type="text" name="user[last_name]" id="last_name" 
                     value="<%= current_user.last_name %>"
                     class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-violet-500 focus:ring-violet-500 sm:text-sm">
            </div>

            <div class="sm:col-span-2">
              <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Email Address
              </label>
              <input type="email" name="user[email]" id="email" 
                     value="<%= current_user.email %>"
                     class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-violet-500 focus:ring-violet-500 sm:text-sm">
              <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                This is the email address you use to sign in to your account.
              </p>
            </div>
          </div>
        </div>

        <!-- Avatar -->
        <div class="p-6">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Profile Photo</h2>
          
          <div class="flex items-center space-x-6">
            <div class="w-24 h-24 bg-gradient-to-br from-violet-500 to-indigo-500 rounded-full flex items-center justify-center text-white font-bold text-2xl">
              <%= current_user.first_name[0].upcase %><%= current_user.last_name[0].upcase %>
            </div>
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                Profile photos are generated from your initials.
              </p>
              <button type="button" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                Upload Photo (Coming Soon)
              </button>
            </div>
          </div>
        </div>

        <!-- Timezone -->
        <div class="p-6">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Preferences</h2>
          
          <div class="max-w-lg">
            <label for="timezone" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Timezone
            </label>
            <select name="user[timezone]" id="timezone" 
                    class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-violet-500 focus:ring-violet-500 sm:text-sm">
              <option value="UTC">UTC</option>
              <option value="America/New_York" <%= 'selected' if current_user.timezone == 'America/New_York' %>>Eastern Time (US & Canada)</option>
              <option value="America/Chicago" <%= 'selected' if current_user.timezone == 'America/Chicago' %>>Central Time (US & Canada)</option>
              <option value="America/Denver" <%= 'selected' if current_user.timezone == 'America/Denver' %>>Mountain Time (US & Canada)</option>
              <option value="America/Los_Angeles" <%= 'selected' if current_user.timezone == 'America/Los_Angeles' %>>Pacific Time (US & Canada)</option>
              <option value="Europe/London" <%= 'selected' if current_user.timezone == 'Europe/London' %>>London</option>
              <option value="Europe/Paris" <%= 'selected' if current_user.timezone == 'Europe/Paris' %>>Paris</option>
              <option value="Asia/Tokyo" <%= 'selected' if current_user.timezone == 'Asia/Tokyo' %>>Tokyo</option>
            </select>
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
              Used for displaying dates and scheduling reports.
            </p>
          </div>
        </div>

        <!-- Submit -->
        <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700/50 flex justify-end space-x-3">
          <%= link_to "Cancel", settings_path, class: "px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600" %>
          <button type="submit" class="px-4 py-2 bg-violet-600 hover:bg-violet-700 text-white rounded-md text-sm font-medium">
            Save Changes
          </button>
        </div>
      <% end %>
    </div>
  </div>
</div>