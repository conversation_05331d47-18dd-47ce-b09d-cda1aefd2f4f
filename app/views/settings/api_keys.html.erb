<div class="min-h-screen bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="bg-white shadow-sm rounded-lg mb-8">
      <div class="px-6 py-5 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">API Keys</h1>
            <p class="mt-1 text-sm text-gray-600">Manage API keys for programmatic access to your data</p>
          </div>
          <button onclick="document.getElementById('create-api-key-modal').classList.remove('hidden'); document.getElementById('create-api-key-modal').style.display='block'; return false;"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <svg class="mr-2 -ml-1 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Create New Key
          </button>
        </div>
      </div>
    </div>

    <!-- Flash message for newly created key -->
    <% if flash[:api_key].present? %>
      <div class="bg-green-50 border-l-4 border-green-400 p-4 mb-6">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-green-800">API Key Created Successfully</h3>
            <div class="mt-2 text-sm text-green-700">
              <p>Make sure to copy your new API key now. You won't be able to see it again!</p>
              <div class="mt-3 p-3 bg-gray-900 rounded-md">
                <code class="text-green-400 font-mono text-sm break-all" id="api-key-display"><%= flash[:api_key] %></code>
              </div>
              <button onclick="navigator.clipboard.writeText(document.getElementById('api-key-display').innerText); this.innerText = 'Copied!'"
                      class="mt-3 inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                Copy to Clipboard
              </button>
            </div>
          </div>
        </div>
      </div>
    <% end %>

    <!-- API Keys List -->
    <div class="bg-white shadow-sm rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-gray-900">Active API Keys</h2>
          <span class="text-sm text-gray-500">
            <%= @api_keys.active.count %> active keys
          </span>
        </div>
      </div>

      <% if @api_keys.any? %>
        <div class="overflow-hidden">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Key
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Permissions
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Usage
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Used
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Created
                </th>
                <th scope="col" class="relative px-6 py-3">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% @api_keys.each do |api_key| %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">
                      <%= api_key.name %>
                    </div>
                    <% if api_key.expires_at.present? %>
                      <div class="text-xs text-gray-500">
                        Expires: <%= api_key.expires_at.strftime("%b %d, %Y") %>
                      </div>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <code class="text-xs bg-gray-100 px-2 py-1 rounded font-mono">
                      <%= api_key.display_key %>
                    </code>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex flex-wrap gap-1">
                      <% (api_key.permissions || []).each do |permission| %>
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                          <%= permission %>
                        </span>
                      <% end %>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div>
                      <%= number_with_delimiter(api_key.request_count || 0) %> requests
                    </div>
                    <% if api_key.recent_request_count&.> 0 %>
                      <div class="text-xs text-gray-400">
                        <%= api_key.recent_request_count %> this month
                      </div>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <% if api_key.last_used_at %>
                      <%= time_ago_in_words(api_key.last_used_at) %> ago
                    <% else %>
                      <span class="text-gray-400">Never</span>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= api_key.created_at.strftime("%b %d, %Y") %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end gap-2">
                      <button data-controller="modal"
                              data-action="click->modal#open"
                              data-modal-target-value="edit-api-key-<%= api_key.id %>"
                              class="text-indigo-600 hover:text-indigo-900">
                        Edit
                      </button>
                      <% if api_key.active? %>
                        <%= link_to "Revoke", 
                            revoke_api_key_path(api_key), 
                            method: :delete,
                            data: { confirm: "Are you sure you want to revoke this API key? This cannot be undone." },
                            class: "text-red-600 hover:text-red-900" %>
                      <% else %>
                        <span class="text-gray-400">Revoked</span>
                      <% end %>
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      <% else %>
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No API keys</h3>
          <p class="mt-1 text-sm text-gray-500">Get started by creating a new API key.</p>
        </div>
      <% end %>
    </div>

    <!-- API Documentation Link -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-blue-800">API Documentation</h3>
          <div class="mt-2 text-sm text-blue-700">
            <p>Learn how to use the FormFlow Pro API to programmatically access your form analytics data.</p>
            <%= link_to "View API Documentation →", "#", class: "font-medium underline hover:text-blue-800" %>
          </div>
        </div>
      </div>
    </div>

    <!-- Rate Limits Info -->
    <div class="mt-6 bg-white shadow-sm rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">Rate Limits</h2>
      </div>
      <div class="px-6 py-4">
        <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
          <div>
            <dt class="text-sm font-medium text-gray-500">Requests per minute</dt>
            <dd class="mt-1 text-sm text-gray-900">60 requests</dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Requests per hour</dt>
            <dd class="mt-1 text-sm text-gray-900">1,000 requests</dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Requests per day</dt>
            <dd class="mt-1 text-sm text-gray-900">10,000 requests</dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Burst limit</dt>
            <dd class="mt-1 text-sm text-gray-900">10 requests/second</dd>
          </div>
        </dl>
      </div>
    </div>
  </div>
</div>

<!-- Create API Key Modal -->
<div id="create-api-key-modal" class="hidden fixed z-50 inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
  <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
    <!-- Background backdrop -->
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onclick="document.getElementById('create-api-key-modal').classList.add('hidden'); document.getElementById('create-api-key-modal').style.display='none';"></div>
    
    <!-- This element is to trick the browser into centering the modal contents. -->
    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
    
    <!-- Modal panel -->
    <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full relative">
      <%= form_with url: settings_api_keys_path, local: true, class: "space-y-4" do |f| %>
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
            Create API Key
          </h3>
          <div class="mt-4 space-y-4">
            <div>
              <%= f.label :name, class: "block text-sm font-medium text-gray-700" %>
              <%= f.text_field :name, 
                  required: true,
                  placeholder: "Production API Key",
                  class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
              <p class="mt-1 text-xs text-gray-500">A friendly name to identify this key</p>
            </div>
            
            <div>
              <%= f.label :permissions, class: "block text-sm font-medium text-gray-700 mb-2" %>
              <div class="space-y-2">
                <% ApiKey::AVAILABLE_PERMISSIONS.each do |permission, description| %>
                  <label class="flex items-start">
                    <%= check_box_tag "permissions[]", permission, permission == 'read', 
                        class: "mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                    <div class="ml-3">
                      <span class="text-sm font-medium text-gray-700"><%= permission.capitalize %></span>
                      <p class="text-xs text-gray-500"><%= description %></p>
                    </div>
                  </label>
                <% end %>
              </div>
            </div>

            <div>
              <%= f.label :expires_at, "Expiration (Optional)", class: "block text-sm font-medium text-gray-700" %>
              <%= f.date_field :expires_at, 
                  min: Date.tomorrow,
                  class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
              <p class="mt-1 text-xs text-gray-500">Leave blank for a key that never expires</p>
            </div>
          </div>
        </div>
        
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <%= f.submit "Create API Key", 
              class: "w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm" %>
          <button type="button" 
                  onclick="document.getElementById('create-api-key-modal').classList.add('hidden'); document.getElementById('create-api-key-modal').style.display='none'; return false;"
                  class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Cancel
          </button>
        </div>
      <% end %>
    </div>
  </div>
</div>