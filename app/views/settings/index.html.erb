<!-- Settings Header -->
<header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-8 py-4">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
      <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Manage your account settings and preferences</p>
    </div>
  </div>
</header>

<!-- Settings Content -->
<div class="flex-1 overflow-auto bg-gray-50 dark:bg-gray-900">
  <div class="p-8">
    
    <div class="grid grid-cols-4 gap-8">
      <!-- Settings Navigation -->
      <div class="col-span-1">
        <nav class="space-y-2">
          <%= link_to settings_path, 
              class: "flex items-center gap-3 px-4 py-3 #{current_page?(settings_path) ? 'bg-gradient-to-r from-violet-500 to-indigo-500 text-white shadow-lg' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-xl transition-colors" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
            </svg>
            <span class="font-medium">Overview</span>
          <% end %>
          
          <%= link_to settings_profile_path, 
              class: "flex items-center gap-3 px-4 py-3 #{current_page?(settings_profile_path) ? 'bg-gradient-to-r from-violet-500 to-indigo-500 text-white shadow-lg' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-xl transition-colors" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <span class="font-medium">Profile</span>
          <% end %>
          
          <%= link_to settings_account_path, 
              class: "flex items-center gap-3 px-4 py-3 #{current_page?(settings_account_path) ? 'bg-gradient-to-r from-violet-500 to-indigo-500 text-white shadow-lg' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-xl transition-colors" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
            <span class="font-medium">Account</span>
          <% end %>
          
          <%= link_to settings_billing_path, 
              class: "flex items-center gap-3 px-4 py-3 #{current_page?(settings_billing_path) ? 'bg-gradient-to-r from-violet-500 to-indigo-500 text-white shadow-lg' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-xl transition-colors" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
            </svg>
            <span class="font-medium">Billing</span>
          <% end %>
          
          <%= link_to settings_team_path, 
              class: "flex items-center gap-3 px-4 py-3 #{current_page?(settings_team_path) ? 'bg-gradient-to-r from-violet-500 to-indigo-500 text-white shadow-lg' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-xl transition-colors" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            <span class="font-medium">Team</span>
          <% end %>
          
          <%= link_to settings_api_keys_path, 
              class: "flex items-center gap-3 px-4 py-3 #{current_page?(settings_api_keys_path) ? 'bg-gradient-to-r from-violet-500 to-indigo-500 text-white shadow-lg' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-xl transition-colors" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
            </svg>
            <span class="font-medium">API Keys</span>
          <% end %>
          
          <%= link_to settings_security_path, 
              class: "flex items-center gap-3 px-4 py-3 #{current_page?(settings_security_path) ? 'bg-gradient-to-r from-violet-500 to-indigo-500 text-white shadow-lg' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-xl transition-colors" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            <span class="font-medium">Security</span>
          <% end %>
        </nav>
      </div>
      
      <!-- Main Content -->
      <div class="col-span-3">
        
        <!-- Account Overview -->
        <div class="space-y-6">
          
          <!-- Quick Stats -->
          <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Account Overview</h2>
            
            <div class="grid grid-cols-4 gap-6">
              <div class="text-center">
                <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= @current_period_stats[:sessions] %></p>
                <p class="text-sm text-gray-500 dark:text-gray-400">Sessions This Month</p>
              </div>
              <div class="text-center">
                <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= @current_period_stats[:websites] %></p>
                <p class="text-sm text-gray-500 dark:text-gray-400">Active Websites</p>
              </div>
              <div class="text-center">
                <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= @current_period_stats[:forms] %></p>
                <p class="text-sm text-gray-500 dark:text-gray-400">Tracked Forms</p>
              </div>
              <div class="text-center">
                <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= @team_members.count %></p>
                <p class="text-sm text-gray-500 dark:text-gray-400">Team Members</p>
              </div>
            </div>
          </div>
          
          <!-- Account Information -->
          <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between mb-4">
              <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Account Information</h2>
              <%= link_to "Edit Account", settings_account_path, class: "text-violet-600 dark:text-violet-400 hover:underline text-sm font-medium" %>
            </div>
            
            <div class="grid grid-cols-2 gap-6">
              <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Account Name</p>
                <p class="text-gray-900 dark:text-white mt-1"><%= @account.name %></p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Account ID</p>
                <p class="text-gray-900 dark:text-white mt-1"><%= @account.slug %></p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</p>
                <p class="text-gray-900 dark:text-white mt-1"><%= @account.created_at.strftime("%B %d, %Y") %></p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Plan</p>
                <p class="text-gray-900 dark:text-white mt-1">
                  <% if @subscription %>
                    <%= @subscription.plan_name || 'Free Plan' %>
                  <% else %>
                    Free Plan
                  <% end %>
                </p>
              </div>
            </div>
          </div>
          
          <!-- Profile Information -->
          <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between mb-4">
              <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Profile Information</h2>
              <%= link_to "Edit Profile", settings_profile_path, class: "text-violet-600 dark:text-violet-400 hover:underline text-sm font-medium" %>
            </div>
            
            <div class="flex items-center gap-6">
              <div class="w-16 h-16 bg-gradient-to-br from-violet-500 to-indigo-500 rounded-full flex items-center justify-center text-white font-bold text-xl">
                <%= current_user.first_name[0].upcase %><%= current_user.last_name[0].upcase %>
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white"><%= current_user.full_name %></h3>
                <p class="text-gray-500 dark:text-gray-400"><%= current_user.email %></p>
                <p class="text-sm text-gray-400 dark:text-gray-500 mt-1">
                  Member since <%= current_user.created_at.strftime("%B %Y") %>
                </p>
              </div>
            </div>
          </div>
          
          <!-- Recent API Activity -->
          <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between mb-4">
              <h2 class="text-lg font-semibold text-gray-900 dark:text-white">API Keys</h2>
              <%= link_to "Manage Keys", settings_api_keys_path, class: "text-violet-600 dark:text-violet-400 hover:underline text-sm font-medium" %>
            </div>
            
            <% if @api_keys.any? %>
              <div class="space-y-3">
                <% @api_keys.first(3).each do |api_key| %>
                  <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-xl">
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white"><%= api_key.name %></p>
                      <p class="text-sm text-gray-500 dark:text-gray-400">
                        Created <%= time_ago_in_words(api_key.created_at) %> ago
                      </p>
                    </div>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400">
                      Active
                    </span>
                  </div>
                <% end %>
              </div>
            <% else %>
              <p class="text-center text-gray-500 dark:text-gray-400 py-6">
                No API keys created yet. <%= link_to "Create your first API key", settings_api_keys_path, class: "text-violet-600 dark:text-violet-400 hover:underline" %> to get started.
              </p>
            <% end %>
          </div>
          
          <!-- Security Status -->
          <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between mb-4">
              <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Security Status</h2>
              <%= link_to "Security Settings", settings_security_path, class: "text-violet-600 dark:text-violet-400 hover:underline text-sm font-medium" %>
            </div>
            
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                  <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                  </svg>
                  <span class="text-sm font-medium text-gray-900 dark:text-white">Strong Password</span>
                </div>
                <span class="text-sm text-green-600 dark:text-green-400">Enabled</span>
              </div>
              
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                  <svg class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                  </svg>
                  <span class="text-sm font-medium text-gray-900 dark:text-white">Two-Factor Authentication</span>
                </div>
                <span class="text-sm text-yellow-600 dark:text-yellow-400">Recommended</span>
              </div>
              
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                  <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                  </svg>
                  <span class="text-sm font-medium text-gray-900 dark:text-white">Login Notifications</span>
                </div>
                <span class="text-sm text-green-600 dark:text-green-400">Enabled</span>
              </div>
            </div>
          </div>
          
        </div>
      </div>
    </div>
    
  </div>
</div>