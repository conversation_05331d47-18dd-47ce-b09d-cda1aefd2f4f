<% content_for :title, "Account Settings - FormFlow Pro" %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
      <nav class="flex mb-4" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-2">
          <li>
            <%= link_to settings_path, class: "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300" do %>
              Settings
            <% end %>
          </li>
          <li class="flex items-center">
            <svg class="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <span class="ml-2 text-gray-900 dark:text-white">Account</span>
          </li>
        </ol>
      </nav>
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Account Settings</h1>
      <p class="mt-2 text-gray-600 dark:text-gray-400">Manage your account details and preferences</p>
    </div>

    <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
      <%= form_with url: settings_account_path, method: :patch, local: true, class: "divide-y divide-gray-200 dark:divide-gray-700" do |form| %>
        
        <!-- Account Information -->
        <div class="p-6">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Account Information</h2>
          
          <div class="grid grid-cols-1 gap-6">
            <div>
              <label for="account_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Account Name
              </label>
              <input type="text" name="account[name]" id="account_name" 
                     value="<%= @account.name %>"
                     class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-violet-500 focus:ring-violet-500 sm:text-sm">
              <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                This is your organization or company name.
              </p>
            </div>

            <div>
              <label for="account_slug" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Account ID
              </label>
              <input type="text" id="account_slug" 
                     value="<%= @account.slug %>"
                     disabled
                     class="mt-1 block w-full rounded-md bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed sm:text-sm">
              <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Your unique account identifier. This cannot be changed.
              </p>
            </div>

            <div>
              <label for="billing_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Billing Email
              </label>
              <input type="email" name="account[billing_email]" id="billing_email" 
                     value="<%= @account.billing_email %>"
                     placeholder="<EMAIL>"
                     class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-violet-500 focus:ring-violet-500 sm:text-sm">
              <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Invoices and billing notifications will be sent to this email.
              </p>
            </div>
          </div>
        </div>

        <!-- Account Status -->
        <div class="p-6">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Account Status</h2>
          
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-700 dark:text-gray-300">Status</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400">
                    <%= @account.status.capitalize %>
                  </span>
                </p>
              </div>
            </div>

            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-700 dark:text-gray-300">Current Plan</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  <%= @account.plan.capitalize %> Plan
                </p>
              </div>
              <%= link_to "Manage Subscription", settings_billing_path, class: "text-violet-600 dark:text-violet-400 hover:underline text-sm font-medium" %>
            </div>

            <% if @account.trial? %>
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-700 dark:text-gray-300">Trial Period</p>
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    Expires <%= @account.trial_ends_at.strftime("%B %d, %Y") %>
                  </p>
                </div>
                <%= link_to "Upgrade Now", settings_billing_path, class: "text-violet-600 dark:text-violet-400 hover:underline text-sm font-medium" %>
              </div>
            <% end %>

            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-700 dark:text-gray-300">Monthly Event Usage</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  <%= number_with_delimiter(@account.monthly_tracked_events) %> / <%= number_with_delimiter(@account.events_limit) %> events
                </p>
              </div>
              <div class="w-32">
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div class="bg-violet-600 h-2 rounded-full" style="width: <%= [(@account.monthly_tracked_events.to_f / @account.events_limit * 100), 100].min %>%"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Submit -->
        <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700/50 flex justify-end space-x-3">
          <%= link_to "Cancel", settings_path, class: "px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600" %>
          <button type="submit" class="px-4 py-2 bg-violet-600 hover:bg-violet-700 text-white rounded-md text-sm font-medium">
            Save Changes
          </button>
        </div>
      <% end %>
    </div>

    <!-- Danger Zone -->
    <div class="mt-8 bg-white dark:bg-gray-800 shadow-sm rounded-lg border-2 border-red-200 dark:border-red-900">
      <div class="p-6">
        <h2 class="text-lg font-medium text-red-600 dark:text-red-400 mb-4">Danger Zone</h2>
        
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">Export Account Data</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                Download all your data in JSON format.
              </p>
            </div>
            <%= form_with url: settings_export_path, method: :post, local: true do |f| %>
              <button type="submit" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                Export Data
              </button>
            <% end %>
          </div>

          <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">Delete Account</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                Permanently delete your account and all associated data.
              </p>
            </div>
            <button type="button" 
                    onclick="if(confirm('Are you ABSOLUTELY sure? This action cannot be undone!')) { document.getElementById('delete-account-form').submit(); }"
                    class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md text-sm font-medium">
              Delete Account
            </button>
            <%= form_with url: settings_account_path, method: :delete, local: true, id: "delete-account-form", class: "hidden" do |f| %>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>