<div class="min-h-screen bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="bg-white shadow-sm rounded-lg mb-8">
      <div class="px-6 py-5 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">Billing & Subscription</h1>
            <p class="mt-1 text-sm text-gray-600">Manage your subscription plan and billing information</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Current Plan Overview -->
    <div class="bg-white shadow-sm rounded-lg mb-8">
      <div class="px-6 py-5 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-gray-900">Current Plan</h2>
          <% if @subscription&.status == 'trialing' %>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
              Trial - <%= (@subscription.current_period_end.to_date - Date.current).to_i %> days left
            </span>
          <% elsif @subscription&.status == 'active' %>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Active
            </span>
          <% else %>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              Free
            </span>
          <% end %>
        </div>
      </div>
      
      <div class="px-6 py-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Plan Details -->
          <div>
            <h3 class="text-sm font-medium text-gray-500 mb-1">Plan</h3>
            <p class="text-lg font-semibold text-gray-900">
              <%= @subscription&.plan_type&.capitalize || 'Free' %> Plan
            </p>
            <% if @subscription&.current_period_end %>
              <p class="text-sm text-gray-500 mt-1">
                Renews <%= @subscription.current_period_end.strftime("%B %d, %Y") %>
              </p>
            <% end %>
          </div>
          
          <!-- Usage This Period -->
          <div>
            <h3 class="text-sm font-medium text-gray-500 mb-1">Sessions This Period</h3>
            <div class="flex items-baseline">
              <p class="text-lg font-semibold text-gray-900">
                <%= number_with_delimiter(@current_period_stats[:sessions]) %>
              </p>
              <span class="ml-2 text-sm text-gray-500">
                / <%= number_with_delimiter(@plan_limits[:sessions]) %>
              </span>
            </div>
            <div class="mt-2 w-full bg-gray-200 rounded-full h-2">
              <div class="bg-indigo-600 h-2 rounded-full" 
                   style="width: <%= [(@current_period_stats[:sessions].to_f / @plan_limits[:sessions] * 100), 100].min %>%"></div>
            </div>
          </div>
          
          <!-- Monthly Cost -->
          <div>
            <h3 class="text-sm font-medium text-gray-500 mb-1">Monthly Cost</h3>
            <p class="text-lg font-semibold text-gray-900">
              <% if @subscription&.plan_type == 'starter' %>
                $29/month
              <% elsif @subscription&.plan_type == 'professional' %>
                $99/month
              <% elsif @subscription&.plan_type == 'enterprise' %>
                Custom pricing
              <% else %>
                $0/month
              <% end %>
            </p>
            <button class="mt-2 text-sm text-indigo-600 hover:text-indigo-500 font-medium">
              Change Plan →
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Usage & Limits -->
    <div class="bg-white shadow-sm rounded-lg mb-8">
      <div class="px-6 py-5 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">Usage & Limits</h2>
      </div>
      <div class="px-6 py-4">
        <div class="space-y-4">
          <!-- Sessions -->
          <div>
            <div class="flex items-center justify-between mb-1">
              <span class="text-sm font-medium text-gray-700">Form Sessions</span>
              <span class="text-sm text-gray-500">
                <%= number_with_delimiter(@current_period_stats[:sessions]) %> / <%= number_with_delimiter(@plan_limits[:sessions]) %>
              </span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="<%= @current_period_stats[:sessions] > @plan_limits[:sessions] * 0.8 ? 'bg-yellow-500' : 'bg-indigo-600' %> h-2 rounded-full" 
                   style="width: <%= [(@current_period_stats[:sessions].to_f / @plan_limits[:sessions] * 100), 100].min %>%"></div>
            </div>
            <% if @current_period_stats[:sessions] > @plan_limits[:sessions] * 0.8 %>
              <p class="mt-1 text-xs text-yellow-600">Approaching limit - consider upgrading</p>
            <% end %>
          </div>
          
          <!-- Websites -->
          <div>
            <div class="flex items-center justify-between mb-1">
              <span class="text-sm font-medium text-gray-700">Websites</span>
              <span class="text-sm text-gray-500">
                <%= @current_period_stats[:websites] %> / <%= @plan_limits[:websites] %>
              </span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="<%= @current_period_stats[:websites] >= @plan_limits[:websites] ? 'bg-red-500' : 'bg-indigo-600' %> h-2 rounded-full" 
                   style="width: <%= [(@current_period_stats[:websites].to_f / @plan_limits[:websites] * 100), 100].min %>%"></div>
            </div>
          </div>
          
          <!-- Forms -->
          <div>
            <div class="flex items-center justify-between mb-1">
              <span class="text-sm font-medium text-gray-700">Forms Tracked</span>
              <span class="text-sm text-gray-500">
                <%= @current_period_stats[:forms] %> / <%= @plan_limits[:forms] %>
              </span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-indigo-600 h-2 rounded-full" 
                   style="width: <%= [(@current_period_stats[:forms].to_f / @plan_limits[:forms] * 100), 100].min %>%"></div>
            </div>
          </div>
          
          <!-- API Calls -->
          <div>
            <div class="flex items-center justify-between mb-1">
              <span class="text-sm font-medium text-gray-700">API Calls</span>
              <span class="text-sm text-gray-500">
                0 / <%= number_with_delimiter(@plan_limits[:api_calls]) %>
              </span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-indigo-600 h-2 rounded-full" style="width: 0%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Available Plans -->
    <div class="bg-white shadow-sm rounded-lg mb-8">
      <div class="px-6 py-5 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">Available Plans</h2>
      </div>
      <div class="px-6 py-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Starter Plan -->
          <div class="border rounded-lg p-6 <%= @subscription&.plan_type == 'starter' ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200' %>">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">Starter</h3>
              <% if @subscription&.plan_type == 'starter' %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                  Current Plan
                </span>
              <% end %>
            </div>
            <p class="text-3xl font-bold text-gray-900 mb-4">
              $29<span class="text-lg font-normal text-gray-500">/month</span>
            </p>
            <ul class="space-y-2 mb-6">
              <li class="flex items-start">
                <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm text-gray-600">Up to 10,000 sessions/month</span>
              </li>
              <li class="flex items-start">
                <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm text-gray-600">5 websites</span>
              </li>
              <li class="flex items-start">
                <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm text-gray-600">Email support</span>
              </li>
            </ul>
            <% unless @subscription&.plan_type == 'starter' %>
              <button class="w-full bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 font-medium">
                Select Plan
              </button>
            <% end %>
          </div>
          
          <!-- Professional Plan -->
          <div class="border rounded-lg p-6 <%= @subscription&.plan_type == 'professional' ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200' %>">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">Professional</h3>
              <% if @subscription&.plan_type == 'professional' %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                  Current Plan
                </span>
              <% else %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Popular
                </span>
              <% end %>
            </div>
            <p class="text-3xl font-bold text-gray-900 mb-4">
              $99<span class="text-lg font-normal text-gray-500">/month</span>
            </p>
            <ul class="space-y-2 mb-6">
              <li class="flex items-start">
                <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm text-gray-600">Up to 100,000 sessions/month</span>
              </li>
              <li class="flex items-start">
                <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm text-gray-600">Unlimited websites</span>
              </li>
              <li class="flex items-start">
                <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm text-gray-600">Priority support</span>
              </li>
              <li class="flex items-start">
                <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm text-gray-600">Advanced analytics</span>
              </li>
            </ul>
            <% unless @subscription&.plan_type == 'professional' %>
              <button class="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 font-medium">
                Upgrade to Professional
              </button>
            <% end %>
          </div>
          
          <!-- Enterprise Plan -->
          <div class="border rounded-lg p-6 border-gray-200">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">Enterprise</h3>
              <% if @subscription&.plan_type == 'enterprise' %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                  Current Plan
                </span>
              <% end %>
            </div>
            <p class="text-3xl font-bold text-gray-900 mb-4">
              Custom
            </p>
            <ul class="space-y-2 mb-6">
              <li class="flex items-start">
                <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm text-gray-600">Unlimited everything</span>
              </li>
              <li class="flex items-start">
                <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm text-gray-600">Dedicated support</span>
              </li>
              <li class="flex items-start">
                <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm text-gray-600">Custom integrations</span>
              </li>
              <li class="flex items-start">
                <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm text-gray-600">SLA guarantee</span>
              </li>
            </ul>
            <button class="w-full bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 font-medium">
              Contact Sales
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Payment Method -->
    <div class="bg-white shadow-sm rounded-lg mb-8">
      <div class="px-6 py-5 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-gray-900">Payment Method</h2>
          <button class="text-sm text-indigo-600 hover:text-indigo-500 font-medium">
            Update
          </button>
        </div>
      </div>
      <div class="px-6 py-4">
        <% if @subscription&.stripe_customer_id %>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-8 w-12 text-gray-400" fill="currentColor" viewBox="0 0 48 32">
                <path d="M4 4h40v24H4z" stroke="currentColor" stroke-width="2" fill="none"/>
                <circle cx="10" cy="16" r="3" fill="currentColor"/>
                <circle cx="16" cy="16" r="3" fill="currentColor"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-900">•••• •••• •••• 4242</p>
              <p class="text-sm text-gray-500">Expires 12/24</p>
            </div>
          </div>
        <% else %>
          <p class="text-sm text-gray-500">No payment method on file</p>
          <button class="mt-3 bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 text-sm font-medium">
            Add Payment Method
          </button>
        <% end %>
      </div>
    </div>

    <!-- Billing History -->
    <div class="bg-white shadow-sm rounded-lg">
      <div class="px-6 py-5 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-gray-900">Billing History</h2>
          <button class="text-sm text-indigo-600 hover:text-indigo-500 font-medium">
            Download All
          </button>
        </div>
      </div>
      <div class="overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Description
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Amount
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" class="relative px-6 py-3">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% if @billing_history.any? %>
              <% @billing_history.each do |invoice| %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <%= invoice[:date].strftime("%B %d, %Y") %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= invoice[:description] %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    $<%= invoice[:amount] %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      <%= invoice[:status] %>
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <a href="#" class="text-indigo-600 hover:text-indigo-900">Download</a>
                  </td>
                </tr>
              <% end %>
            <% else %>
              <tr>
                <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">
                  No billing history available
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>