<!-- Session Replays Header -->
<header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-8 py-4">
  <div class="flex items-center justify-between">
    <div>
      <nav class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 mb-2">
        <%= link_to "Dashboard", dashboard_path, class: "hover:text-gray-700 dark:hover:text-gray-200" %> / 
        <%= link_to @form.website.name, website_path(@form.website), class: "hover:text-gray-700 dark:hover:text-gray-200" %> / 
        <%= link_to @form.name, form_path(@form), class: "hover:text-gray-700 dark:hover:text-gray-200" %> /
        <span class="text-gray-900 dark:text-white">Session Replays</span>
      </nav>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white"><%= @form.name %> Session Replays</h1>
      <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Watch how users interact with your form fields</p>
    </div>
    
    <!-- Filters -->
    <div class="flex items-center gap-4">
      <%= form_with url: form_session_replays_path(@form), method: :get, local: true, class: "flex items-center gap-3" do |f| %>
        <%= f.select :range, options_for_select([
          ['Last 7 days', '7'],
          ['Last 30 days', '30'],
          ['Last 90 days', '90']
        ], @date_range), {}, { 
          class: "px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-violet-500",
          onchange: "this.form.submit();"
        } %>
        
        <%= f.select :status, options_for_select([
          ['All Sessions', 'all'],
          ['Completed', 'completed'],
          ['Abandoned', 'abandoned'],
          ['Active', 'active']
        ], @status_filter), {}, { 
          class: "px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-violet-500",
          onchange: "this.form.submit();"
        } %>
      <% end %>
      
      <div class="flex items-center gap-2">
        <%= link_to form_path(@form), class: "px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors" do %>
          <svg class="w-4 h-4 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 15l-3-3m0 0l3-3m-3 3h8M3 12a9 9 0 1118 0 9 9 0 01-18 0z"></path>
          </svg>
          Back to Form
        <% end %>
      </div>
    </div>
  </div>
</header>

<!-- Session Replays Content -->
<div class="flex-1 overflow-auto bg-gray-50 dark:bg-gray-900">
  <div class="p-8">
    
    <!-- Session Statistics -->
    <div class="grid grid-cols-5 gap-6 mb-8">
      <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Sessions</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= number_with_delimiter(@session_stats[:total_sessions]) %></p>
          </div>
          <div class="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Completed</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= number_with_delimiter(@session_stats[:completed_sessions]) %></p>
          </div>
          <div class="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Abandoned</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= number_with_delimiter(@session_stats[:abandoned_sessions]) %></p>
          </div>
          <div class="p-3 bg-red-100 dark:bg-red-900/30 rounded-xl">
            <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Avg Duration</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= @session_stats[:avg_duration].round(1) %>s</p>
          </div>
          <div class="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-xl">
            <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Bounce Rate</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= @session_stats[:bounce_rate].round(1) %>%</p>
          </div>
          <div class="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-xl">
            <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- Session List -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Session Recordings</h2>
          <span class="text-sm text-gray-500 dark:text-gray-400"><%= @sessions.total_count %> sessions</span>
        </div>
      </div>
      
      <% if @sessions.any? %>
        <div class="divide-y divide-gray-200 dark:divide-gray-700">
          <% @sessions.each do |session| %>
            <%
              status_colors = {
                'completed' => 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400',
                'abandoned' => 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400',
                'active' => 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400'
              }
              
              duration = session.ended_at && session.started_at ? (session.ended_at - session.started_at).round(2) : 0
              event_count = session.field_events.count
            %>
            
            <div class="p-6 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-3 mb-2">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Session #<%= session.id.split('-').first.upcase %></h3>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= status_colors[session.status] %>">
                      <%= session.status.humanize %>
                    </span>
                    <% if event_count > 50 %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-400">
                        High Activity
                      </span>
                    <% end %>
                  </div>
                  
                  <div class="grid grid-cols-4 gap-6 text-sm text-gray-600 dark:text-gray-300">
                    <div>
                      <span class="font-medium text-gray-500 dark:text-gray-400">Started:</span>
                      <span><%= time_ago_in_words(session.started_at) %> ago</span>
                    </div>
                    <div>
                      <span class="font-medium text-gray-500 dark:text-gray-400">Duration:</span>
                      <span><%= duration.round(1) %>s</span>
                    </div>
                    <div>
                      <span class="font-medium text-gray-500 dark:text-gray-400">Interactions:</span>
                      <span><%= number_with_delimiter(event_count) %> events</span>
                    </div>
                    <div>
                      <span class="font-medium text-gray-500 dark:text-gray-400">Errors:</span>
                      <span class="text-red-600 dark:text-red-400"><%= session.field_events.where(event_type: 'error').count %></span>
                    </div>
                  </div>
                  
                  <% if session.user_agent.present? %>
                    <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                      <span class="font-medium">User Agent:</span> <%= truncate(session.user_agent, length: 80) %>
                    </div>
                  <% end %>
                </div>
                
                <div class="flex items-center gap-2 ml-6">
                  <%= link_to form_session_replay_path(@form, session), class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gradient-to-r from-violet-500 to-indigo-500 hover:from-violet-600 hover:to-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-500" do %>
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10v18a2 2 0 01-2 2H6a2 2 0 01-2-2V4a2 2 0 012-2h8l4 4z"></path>
                    </svg>
                    Watch Replay
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>
        </div>
        
        <!-- Pagination -->
        <% if @sessions.respond_to?(:current_page) %>
          <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <%= paginate @sessions, theme: 'twitter_bootstrap_4', nav_class: 'flex items-center justify-center' %>
          </div>
        <% end %>
        
      <% else %>
        <!-- Empty State -->
        <div class="p-12 text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
          </svg>
          <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">No Session Recordings</h3>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
            No session recordings found for the selected time period and filters.
          </p>
        </div>
      <% end %>
    </div>
    
  </div>
</div>