<!-- Session Replay Header -->
<header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-8 py-4">
  <div class="flex items-center justify-between">
    <div>
      <nav class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 mb-2">
        <%= link_to "Dashboard", dashboard_path, class: "hover:text-gray-700 dark:hover:text-gray-200" %> / 
        <%= link_to @form.website.name, website_path(@form.website), class: "hover:text-gray-700 dark:hover:text-gray-200" %> / 
        <%= link_to @form.name, form_path(@form), class: "hover:text-gray-700 dark:hover:text-gray-200" %> /
        <%= link_to "Session Replays", form_session_replays_path(@form), class: "hover:text-gray-700 dark:hover:text-gray-200" %> /
        <span class="text-gray-900 dark:text-white">Session #<%= @session.id.split('-').first.upcase %></span>
      </nav>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Session Replay</h1>
      <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
        <%= @session_metadata[:duration].round(1) %>s duration • 
        <%= @session_metadata[:total_events] %> interactions • 
        Started <%= time_ago_in_words(@session.started_at) %> ago
      </p>
    </div>
    
    <div class="flex items-center gap-3">
      <%= link_to form_session_replays_path(@form), class: "px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors" do %>
        <svg class="w-4 h-4 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 15l-3-3m0 0l3-3m-3 3h8M3 12a9 9 0 1118 0 9 9 0 01-18 0z"></path>
        </svg>
        Back to Sessions
      <% end %>
    </div>
  </div>
</header>

<!-- Session Replay Content -->
<div class="flex-1 overflow-auto bg-gray-50 dark:bg-gray-900">
  <div class="p-8">
    
    <!-- Session Metadata -->
    <div class="grid grid-cols-5 gap-6 mb-8">
      <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Duration</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= @session_metadata[:duration].round(1) %>s</p>
          </div>
          <div class="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Events</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= @session_metadata[:total_events] %></p>
          </div>
          <div class="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2-5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.122 2.122"></path>
            </svg>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Error Events</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= @session_metadata[:error_events] %></p>
          </div>
          <div class="p-3 bg-red-100 dark:bg-red-900/30 rounded-xl">
            <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Completion</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= @session_metadata[:completion_rate].round(1) %>%</p>
          </div>
          <div class="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-xl">
            <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</p>
            <% 
              status_colors = {
                'completed' => { bg: 'bg-green-100 dark:bg-green-900/30', text: 'text-green-800 dark:text-green-400' },
                'abandoned' => { bg: 'bg-red-100 dark:bg-red-900/30', text: 'text-red-800 dark:text-red-400' },
                'active' => { bg: 'bg-blue-100 dark:bg-blue-900/30', text: 'text-blue-800 dark:text-blue-400' }
              }
            %>
            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-sm font-medium <%= status_colors[@session.status][:bg] %> <%= status_colors[@session.status][:text] %>">
              <%= @session.status.humanize %>
            </span>
          </div>
          <div class="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-xl">
            <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-3 gap-8">
      <!-- Replay Player -->
      <div class="col-span-2">
        <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Session Replay Player</h2>
            <div class="flex items-center gap-2">
              <button id="playPauseBtn" class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-violet-600 hover:bg-violet-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-500">
                <svg id="playIcon" class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z"/>
                </svg>
                <svg id="pauseIcon" class="w-4 h-4 mr-2 hidden" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                </svg>
                <span id="playPauseText">Play</span>
              </button>
              
              <div class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                <span>Speed:</span>
                <select id="speedControl" class="border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-xs">
                  <option value="0.5">0.5x</option>
                  <option value="1" selected>1x</option>
                  <option value="1.5">1.5x</option>
                  <option value="2">2x</option>
                </select>
              </div>
            </div>
          </div>
          
          <!-- Form Visualization -->
          <div id="formVisualization" class="border border-gray-200 dark:border-gray-600 rounded-lg p-6 bg-gray-50 dark:bg-gray-700 min-h-[400px]">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4"><%= @form.name %></h3>
            
            <!-- Form Fields -->
            <div class="space-y-4" id="formFields">
              <% @replay_data[:form_fields].each do |field| %>
                <div id="field-<%= field[:id] %>" class="form-field relative p-3 border border-gray-300 dark:border-gray-500 rounded-lg bg-white dark:bg-gray-800 transition-all duration-300">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <%= field[:name] %>
                    <% if field[:required] %>
                      <span class="text-red-500">*</span>
                    <% end %>
                  </label>
                  
                  <% case field[:field_type] %>
                  <% when 'text', 'email', 'password', 'tel', 'url', 'number' %>
                    <input type="<%= field[:field_type] %>" class="field-input w-full px-3 py-2 border border-gray-300 dark:border-gray-500 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white" disabled>
                  <% when 'textarea' %>
                    <textarea class="field-input w-full px-3 py-2 border border-gray-300 dark:border-gray-500 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white" rows="3" disabled></textarea>
                  <% when 'select' %>
                    <select class="field-input w-full px-3 py-2 border border-gray-300 dark:border-gray-500 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white" disabled>
                      <option>Select an option</option>
                    </select>
                  <% when 'checkbox' %>
                    <input type="checkbox" class="field-input" disabled>
                    <span class="ml-2 text-gray-700 dark:text-gray-300">Checkbox option</span>
                  <% when 'radio' %>
                    <input type="radio" class="field-input" disabled>
                    <span class="ml-2 text-gray-700 dark:text-gray-300">Radio option</span>
                  <% else %>
                    <input type="text" class="field-input w-full px-3 py-2 border border-gray-300 dark:border-gray-500 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white" disabled>
                  <% end %>
                  
                  <!-- Field Activity Indicator -->
                  <div class="field-activity absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white opacity-0 transition-opacity duration-300"></div>
                </div>
              <% end %>
            </div>
          </div>
          
          <!-- Progress Bar -->
          <div class="mt-6">
            <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-2">
              <span>Progress</span>
              <span id="currentTime">0:00</span> / <span id="totalTime"><%= (@session_metadata[:duration] / 60).floor %>:<%= sprintf('%02d', (@session_metadata[:duration] % 60).round) %></span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div id="progressBar" class="bg-violet-600 h-2 rounded-full transition-all duration-100" style="width: 0%"></div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Event Timeline -->
      <div>
        <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Event Timeline</h2>
          
          <div class="space-y-3 max-h-96 overflow-y-auto" id="eventTimeline">
            <% @field_events.each_with_index do |event, index| %>
              <%
                event_colors = {
                  'focus' => 'text-blue-600 dark:text-blue-400',
                  'blur' => 'text-gray-600 dark:text-gray-400',
                  'change' => 'text-green-600 dark:text-green-400',
                  'input' => 'text-purple-600 dark:text-purple-400',
                  'error' => 'text-red-600 dark:text-red-400',
                  'correction' => 'text-yellow-600 dark:text-yellow-400'
                }
                
                seconds_since_start = ((event.created_at - @session.started_at) / 1.minute * 60).round(1)
              %>
              
              <div class="event-item flex items-start gap-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" data-event-index="<%= index %>" data-timestamp="<%= seconds_since_start %>">
                <div class="flex-shrink-0 w-2 h-2 rounded-full bg-gray-300 dark:bg-gray-600 mt-2"></div>
                <div class="flex-1 min-w-0">
                  <div class="flex items-center justify-between">
                    <p class="text-sm font-medium text-gray-900 dark:text-white">
                      <%= event.form_field.name %>
                    </p>
                    <span class="text-xs text-gray-500 dark:text-gray-400">
                      <%= seconds_since_start %>s
                    </span>
                  </div>
                  <p class="text-xs <%= event_colors[event.event_type] || 'text-gray-600 dark:text-gray-400' %>">
                    <%= event.event_type.humanize %>
                    <% if event.value.present? %>
                      • "<%= truncate(event.value, length: 20) %>"
                    <% end %>
                    <% if event.error_message.present? %>
                      • Error: <%= truncate(event.error_message, length: 30) %>
                    <% end %>
                  </p>
                </div>
              </div>
            <% end %>
          </div>
        </div>
        
        <!-- Problematic Fields -->
        <% if @session_metadata[:problematic_fields].any? %>
          <div class="mt-6 bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Problematic Fields</h2>
            <div class="space-y-2">
              <% @session_metadata[:problematic_fields].each do |field_name, error_count| %>
                <div class="flex items-center justify-between p-2 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <span class="text-sm font-medium text-gray-900 dark:text-white"><%= field_name %></span>
                  <span class="text-sm text-red-600 dark:text-red-400"><%= error_count %> errors</span>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<!-- JavaScript for Replay Player -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  const replayData = <%= raw @replay_data.to_json %>;
  const events = replayData.events;
  const sessionStart = replayData.session_start;
  const sessionEnd = replayData.session_end;
  const totalDuration = sessionEnd - sessionStart;
  
  let currentEventIndex = 0;
  let isPlaying = false;
  let playbackSpeed = 1;
  let startTime = null;
  let pausedTime = 0;
  
  const playPauseBtn = document.getElementById('playPauseBtn');
  const playIcon = document.getElementById('playIcon');
  const pauseIcon = document.getElementById('pauseIcon');
  const playPauseText = document.getElementById('playPauseText');
  const speedControl = document.getElementById('speedControl');
  const progressBar = document.getElementById('progressBar');
  const currentTimeDisplay = document.getElementById('currentTime');
  
  function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return minutes + ':' + (remainingSeconds < 10 ? '0' : '') + remainingSeconds;
  }
  
  function updateProgress(currentTime) {
    const progress = (currentTime / totalDuration) * 100;
    progressBar.style.width = Math.min(progress, 100) + '%';
    currentTimeDisplay.textContent = formatTime(currentTime);
  }
  
  function highlightField(fieldId, eventType) {
    // Reset all field highlights
    document.querySelectorAll('.form-field').forEach(field => {
      field.classList.remove('ring-2', 'ring-blue-500', 'ring-red-500', 'ring-green-500', 'ring-yellow-500');
      const activity = field.querySelector('.field-activity');
      activity.style.opacity = '0';
    });
    
    // Highlight current field
    const field = document.getElementById(`field-${fieldId}`);
    if (field) {
      const colors = {
        'focus': 'ring-blue-500',
        'error': 'ring-red-500', 
        'change': 'ring-green-500',
        'correction': 'ring-yellow-500'
      };
      
      const activityColors = {
        'focus': 'bg-blue-500',
        'error': 'bg-red-500',
        'change': 'bg-green-500', 
        'correction': 'bg-yellow-500'
      };
      
      field.classList.add('ring-2', colors[eventType] || 'ring-blue-500');
      
      const activity = field.querySelector('.field-activity');
      activity.className = `field-activity absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white transition-opacity duration-300 ${activityColors[eventType] || 'bg-blue-500'}`;
      activity.style.opacity = '1';
      
      // Update field value if available
      const input = field.querySelector('.field-input');
      const event = events[currentEventIndex];
      if (input && event.value) {
        input.value = event.value;
      }
      
      // Scroll field into view
      field.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }
  
  function highlightTimelineEvent(index) {
    document.querySelectorAll('.event-item').forEach((item, i) => {
      if (i === index) {
        item.classList.add('bg-violet-100', 'dark:bg-violet-900/30');
        item.scrollIntoView({ behavior: 'smooth', block: 'center' });
      } else {
        item.classList.remove('bg-violet-100', 'dark:bg-violet-900/30');
      }
    });
  }
  
  function playReplay() {
    if (currentEventIndex >= events.length) {
      stopReplay();
      return;
    }
    
    if (!startTime) {
      startTime = Date.now() - pausedTime;
    }
    
    const elapsed = (Date.now() - startTime) * playbackSpeed;
    const currentTime = elapsed / 1000;
    
    updateProgress(currentTime);
    
    // Process events that should have occurred by now
    while (currentEventIndex < events.length) {
      const event = events[currentEventIndex];
      const eventTime = (event.timestamp - sessionStart);
      
      if (eventTime <= currentTime) {
        highlightField(event.field_id, event.event_type);
        highlightTimelineEvent(currentEventIndex);
        currentEventIndex++;
      } else {
        break;
      }
    }
    
    if (isPlaying && currentEventIndex < events.length) {
      requestAnimationFrame(playReplay);
    } else if (currentEventIndex >= events.length) {
      stopReplay();
    }
  }
  
  function startReplay() {
    isPlaying = true;
    playIcon.classList.add('hidden');
    pauseIcon.classList.remove('hidden');
    playPauseText.textContent = 'Pause';
    
    if (!startTime) {
      startTime = Date.now() - pausedTime;
    } else {
      startTime = Date.now() - pausedTime;
    }
    
    requestAnimationFrame(playReplay);
  }
  
  function pauseReplay() {
    isPlaying = false;
    playIcon.classList.remove('hidden');
    pauseIcon.classList.add('hidden');
    playPauseText.textContent = 'Play';
    
    if (startTime) {
      pausedTime = Date.now() - startTime;
    }
  }
  
  function stopReplay() {
    isPlaying = false;
    startTime = null;
    pausedTime = 0;
    currentEventIndex = 0;
    
    playIcon.classList.remove('hidden');
    pauseIcon.classList.add('hidden');
    playPauseText.textContent = 'Replay';
    
    // Reset field highlights
    document.querySelectorAll('.form-field').forEach(field => {
      field.classList.remove('ring-2', 'ring-blue-500', 'ring-red-500', 'ring-green-500', 'ring-yellow-500');
      field.querySelector('.field-activity').style.opacity = '0';
      const input = field.querySelector('.field-input');
      if (input) input.value = '';
    });
    
    // Reset timeline highlights
    document.querySelectorAll('.event-item').forEach(item => {
      item.classList.remove('bg-violet-100', 'dark:bg-violet-900/30');
    });
    
    updateProgress(0);
  }
  
  // Event listeners
  playPauseBtn.addEventListener('click', function() {
    if (isPlaying) {
      pauseReplay();
    } else {
      startReplay();
    }
  });
  
  speedControl.addEventListener('change', function() {
    playbackSpeed = parseFloat(this.value);
    if (isPlaying && startTime) {
      // Adjust timing for speed change
      pausedTime = Date.now() - startTime;
      startTime = Date.now() - pausedTime;
    }
  });
  
  // Click on timeline events to jump to that point
  document.querySelectorAll('.event-item').forEach((item, index) => {
    item.addEventListener('click', function() {
      const timestamp = parseFloat(this.dataset.timestamp);
      currentEventIndex = index;
      pausedTime = timestamp * 1000;
      
      if (isPlaying) {
        startTime = Date.now() - pausedTime;
      }
      
      updateProgress(timestamp);
      highlightField(events[index].field_id, events[index].event_type);
      highlightTimelineEvent(index);
    });
  });
});
</script>