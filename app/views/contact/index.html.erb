<% content_for :title, "Contact - FormFlow Pro" %>

<%= render 'shared/navbar' %>

<!-- Hero Section -->
<section class="relative min-h-[40vh] flex items-center justify-center overflow-hidden">
  <!-- Background effects -->
  <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-purple-900/20 via-transparent to-transparent"></div>
  <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom,_var(--tw-gradient-stops))] from-blue-900/20 via-transparent to-transparent"></div>
  
  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <h1 class="text-5xl sm:text-6xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-4">
      Get in Touch
    </h1>
    <p class="text-xl text-gray-300 max-w-3xl mx-auto">
      Have questions? We're here to help you succeed with FormFlow Pro
    </p>
  </div>
</section>

<!-- Contact Section -->
<section class="py-20 bg-black">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid lg:grid-cols-2 gap-12">
      <!-- Contact Form -->
      <div>
        <h2 class="text-3xl font-bold text-white mb-6">Send us a Message</h2>
        <p class="text-gray-400 mb-8">
          Fill out the form below and we'll get back to you within 24 hours.
        </p>
        
        <form class="space-y-6">
          <div class="grid sm:grid-cols-2 gap-6">
            <div>
              <label for="first_name" class="block text-sm font-medium text-gray-300 mb-2">First Name</label>
              <input type="text" id="first_name" name="first_name" required
                     class="w-full px-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
            </div>
            <div>
              <label for="last_name" class="block text-sm font-medium text-gray-300 mb-2">Last Name</label>
              <input type="text" id="last_name" name="last_name" required
                     class="w-full px-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
            </div>
          </div>
          
          <div>
            <label for="email" class="block text-sm font-medium text-gray-300 mb-2">Email</label>
            <input type="email" id="email" name="email" required
                   class="w-full px-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
          </div>
          
          <div>
            <label for="company" class="block text-sm font-medium text-gray-300 mb-2">Company (Optional)</label>
            <input type="text" id="company" name="company"
                   class="w-full px-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
          </div>
          
          <div>
            <label for="subject" class="block text-sm font-medium text-gray-300 mb-2">Subject</label>
            <select id="subject" name="subject" required
                    class="w-full px-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
              <option value="">Select a topic</option>
              <option value="sales">Sales Inquiry</option>
              <option value="support">Technical Support</option>
              <option value="billing">Billing Question</option>
              <option value="partnership">Partnership Opportunity</option>
              <option value="other">Other</option>
            </select>
          </div>
          
          <div>
            <label for="message" class="block text-sm font-medium text-gray-300 mb-2">Message</label>
            <textarea id="message" name="message" rows="6" required
                      class="w-full px-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all resize-none"
                      placeholder="Tell us how we can help..."></textarea>
          </div>
          
          <div class="flex items-start">
            <input type="checkbox" id="privacy" name="privacy" required
                   class="mt-1 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-600 rounded bg-gray-900/50">
            <label for="privacy" class="ml-2 text-sm text-gray-400">
              I agree to the <%= link_to "Privacy Policy", privacy_path, class: "text-purple-400 hover:text-purple-300 underline transition" %> 
              and <%= link_to "Terms of Service", terms_path, class: "text-purple-400 hover:text-purple-300 underline transition" %>
            </label>
          </div>
          
          <button type="submit" 
                  class="w-full px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-600 transition-all transform hover:scale-[1.02] shadow-lg">
            Send Message
          </button>
        </form>
      </div>
      
      <!-- Contact Information -->
      <div class="lg:pl-12">
        <h2 class="text-3xl font-bold text-white mb-6">Other Ways to Reach Us</h2>
        
        <!-- Contact Cards -->
        <div class="space-y-6">
          <!-- Email -->
          <div class="bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
            <div class="flex items-start space-x-4">
              <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>
              <div>
                <h3 class="text-white font-semibold mb-1">Email Us</h3>
                <p class="text-gray-400 text-sm mb-2">Get in touch via email</p>
                <a href="mailto:<EMAIL>" class="text-purple-400 hover:text-purple-300 transition"><EMAIL></a>
              </div>
            </div>
          </div>
          
          <!-- Support -->
          <div class="bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
            <div class="flex items-start space-x-4">
              <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
                </svg>
              </div>
              <div>
                <h3 class="text-white font-semibold mb-1">Support Center</h3>
                <p class="text-gray-400 text-sm mb-2">24/7 support for paid plans</p>
                <a href="mailto:<EMAIL>" class="text-purple-400 hover:text-purple-300 transition"><EMAIL></a>
              </div>
            </div>
          </div>
          
          <!-- Sales -->
          <div class="bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
            <div class="flex items-start space-x-4">
              <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>
              <div>
                <h3 class="text-white font-semibold mb-1">Sales Team</h3>
                <p class="text-gray-400 text-sm mb-2">Questions about pricing or enterprise?</p>
                <a href="mailto:<EMAIL>" class="text-purple-400 hover:text-purple-300 transition"><EMAIL></a>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Office Location -->
        <div class="mt-8 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
          <h3 class="text-white font-semibold mb-4">Office Location</h3>
          <p class="text-gray-400">
            FormFlow Pro, Inc.<br>
            123 Innovation Drive<br>
            San Francisco, CA 94107<br>
            United States
          </p>
        </div>
        
        <!-- Social Links -->
        <div class="mt-8">
          <h3 class="text-white font-semibold mb-4">Follow Us</h3>
          <div class="flex space-x-4">
            <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-all">
              <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
            </a>
            <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-all">
              <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
              </svg>
            </a>
            <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-all">
              <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-20">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-white mb-4">Frequently Asked Questions</h2>
      <p class="text-gray-400">
        Quick answers to common questions about FormFlow Pro
      </p>
    </div>
    
    <div class="space-y-6">
      <div class="bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
        <h3 class="text-lg font-semibold text-white mb-2">How quickly can I get started?</h3>
        <p class="text-gray-400">
          You can start tracking your forms in less than 5 minutes. Simply sign up, add our tracking script to your website, and you'll begin seeing data immediately.
        </p>
      </div>
      
      <div class="bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
        <h3 class="text-lg font-semibold text-white mb-2">Do you offer a free trial?</h3>
        <p class="text-gray-400">
          Yes! We offer a 14-day free trial with full access to all features. No credit card required to start.
        </p>
      </div>
      
      <div class="bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
        <h3 class="text-lg font-semibold text-white mb-2">Is my data secure?</h3>
        <p class="text-gray-400">
          Absolutely. We use enterprise-grade encryption for all data transmission and storage. We're also GDPR compliant and never store personally identifiable information.
        </p>
      </div>
      
      <div class="bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
        <h3 class="text-lg font-semibold text-white mb-2">Can I cancel anytime?</h3>
        <p class="text-gray-400">
          Yes, you can cancel your subscription at any time. There are no long-term contracts or cancellation fees.
        </p>
      </div>
    </div>
  </div>
</section>

<%= render 'shared/footer' %>