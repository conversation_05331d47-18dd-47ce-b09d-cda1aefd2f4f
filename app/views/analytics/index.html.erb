<!-- Analytics Header -->
<header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 sm:px-6 lg:px-8 py-4">
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
    <div>
      <h1 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Analytics</h1>
      <p class="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mt-1">Deep insights into your form performance and user behavior</p>
    </div>
    
    <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
      <!-- Time Range Selector -->
      <div class="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1 overflow-x-auto">
        <%= link_to analytics_path(range: '7'), 
            class: "px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium whitespace-nowrap #{params[:range] == '7' ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' : 'text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-600'} rounded-md transition-colors" do %>
          Last 7 days
        <% end %>
        <%= link_to analytics_path(range: '30'), 
            class: "px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium whitespace-nowrap #{params[:range].nil? || params[:range] == '30' ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' : 'text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-600'} rounded-md transition-colors" do %>
          Last 30 days
        <% end %>
        <%= link_to analytics_path(range: '90'), 
            class: "px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium whitespace-nowrap #{params[:range] == '90' ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' : 'text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-600'} rounded-md transition-colors" do %>
          Last 90 days
        <% end %>
      </div>
      
      <!-- Export Button -->
      <div class="relative inline-block text-left">
        <button class="px-4 sm:px-5 py-2 sm:py-2.5 bg-gradient-to-r from-violet-500 to-indigo-500 text-white rounded-lg font-medium hover:from-violet-600 hover:to-indigo-600 transition-all shadow-lg flex items-center justify-center gap-2 text-sm sm:text-base">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Export Data
        </button>
      </div>
    </div>
  </div>
</header>

<!-- Analytics Content -->
<div class="flex-1 overflow-auto bg-gray-50 dark:bg-gray-900">
  <div class="p-4 sm:p-6 lg:p-8">
    
    <!-- Key Performance Indicators -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
      <!-- Total Sessions -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
            </svg>
          </div>
          <span class="text-xs font-medium <%= @previous_period_comparison[:sessions_change] >= 0 ? 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30' : 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30' %> px-2 py-1 rounded-full">
            <%= @previous_period_comparison[:sessions_change] >= 0 ? '+' : '' %><%= @previous_period_comparison[:sessions_change] %>%
          </span>
        </div>
        <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><%= number_with_delimiter(@total_sessions) %></h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Total Sessions</p>
      </div>
      
      <!-- Total Submissions -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <span class="text-xs font-medium <%= @previous_period_comparison[:submissions_change] >= 0 ? 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30' : 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30' %> px-2 py-1 rounded-full">
            <%= @previous_period_comparison[:submissions_change] >= 0 ? '+' : '' %><%= @previous_period_comparison[:submissions_change] %>%
          </span>
        </div>
        <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><%= number_with_delimiter(@total_submissions) %></h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Form Submissions</p>
      </div>
      
      <!-- Conversion Rate -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-xl">
            <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
            </svg>
          </div>
          <span class="text-xs font-medium <%= @previous_period_comparison[:conversion_change] >= 0 ? 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30' : 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30' %> px-2 py-1 rounded-full">
            <%= @previous_period_comparison[:conversion_change] >= 0 ? '+' : '' %><%= @previous_period_comparison[:conversion_change] %>%
          </span>
        </div>
        <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><%= @conversion_rate %>%</h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Conversion Rate</p>
      </div>
      
      <!-- Abandonment Rate -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="p-3 bg-red-100 dark:bg-red-900/30 rounded-xl">
            <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
            </svg>
          </div>
          <span class="text-xs font-medium text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 px-2 py-1 rounded-full">
            <%= @abandonment_rate %>%
          </span>
        </div>
        <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><%= @abandonment_rate %>%</h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Abandonment Rate</p>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 mb-8">
      
      <!-- Sessions & Submissions Chart -->
      <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Sessions & Submissions Over Time</h2>
          <div class="flex gap-4">
            <div class="flex items-center gap-2">
              <div class="w-3 h-3 bg-violet-500 rounded-full"></div>
              <span class="text-sm text-gray-600 dark:text-gray-400">Sessions</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="w-3 h-3 bg-green-500 rounded-full"></div>
              <span class="text-sm text-gray-600 dark:text-gray-400">Submissions</span>
            </div>
          </div>
        </div>
        
        <!-- Chart placeholder -->
        <div class="h-64" data-controller="chart" data-chart-sessions-value="<%= @sessions_over_time.to_json %>" data-chart-submissions-value="<%= @submissions_over_time.to_json %>">
          <canvas id="sessionsChart"></canvas>
        </div>
      </div>
      
      <!-- Conversion Funnel -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Conversion Funnel</h2>
        
        <div class="space-y-4">
          <div class="relative">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Visitors</span>
              <span class="text-sm font-semibold text-gray-900 dark:text-white"><%= number_with_delimiter(@conversion_funnel[:total_visitors]) %></span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
              <div class="bg-violet-500 h-3 rounded-full" style="width: 100%"></div>
            </div>
          </div>
          
          <div class="relative">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Started Forms</span>
              <span class="text-sm font-semibold text-gray-900 dark:text-white"><%= number_with_delimiter(@conversion_funnel[:form_started]) %></span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
              <div class="bg-indigo-500 h-3 rounded-full" style="width: <%= @conversion_funnel[:total_visitors] > 0 ? (@conversion_funnel[:form_started].to_f / @conversion_funnel[:total_visitors] * 100).round : 0 %>%"></div>
            </div>
            <span class="text-xs text-gray-500 dark:text-gray-400"><%= @conversion_funnel[:total_visitors] > 0 ? ((@conversion_funnel[:form_started].to_f / @conversion_funnel[:total_visitors]) * 100).round(1) : 0 %>%</span>
          </div>
          
          <div class="relative">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Completed</span>
              <span class="text-sm font-semibold text-gray-900 dark:text-white"><%= number_with_delimiter(@conversion_funnel[:form_completed]) %></span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
              <div class="bg-blue-500 h-3 rounded-full" style="width: <%= @conversion_funnel[:total_visitors] > 0 ? (@conversion_funnel[:form_completed].to_f / @conversion_funnel[:total_visitors] * 100).round : 0 %>%"></div>
            </div>
            <span class="text-xs text-gray-500 dark:text-gray-400"><%= @conversion_funnel[:total_visitors] > 0 ? ((@conversion_funnel[:form_completed].to_f / @conversion_funnel[:total_visitors]) * 100).round(1) : 0 %>%</span>
          </div>
          
          <div class="relative">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Submitted</span>
              <span class="text-sm font-semibold text-gray-900 dark:text-white"><%= number_with_delimiter(@conversion_funnel[:form_submitted]) %></span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
              <div class="bg-green-500 h-3 rounded-full" style="width: <%= @conversion_funnel[:total_visitors] > 0 ? (@conversion_funnel[:form_submitted].to_f / @conversion_funnel[:total_visitors] * 100).round : 0 %>%"></div>
            </div>
            <span class="text-xs text-gray-500 dark:text-gray-400"><%= @conversion_funnel[:total_visitors] > 0 ? ((@conversion_funnel[:form_submitted].to_f / @conversion_funnel[:total_visitors]) * 100).round(1) : 0 %>%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance Tables Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 mb-8">
      
      <!-- Form Performance -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Form Performance</h2>
        
        <% if @form_performance.any? %>
          <div class="overflow-x-auto">
            <table class="min-w-full">
              <thead>
                <tr class="border-b border-gray-200 dark:border-gray-700">
                  <th class="text-left text-sm font-medium text-gray-500 dark:text-gray-400 pb-3">Form</th>
                  <th class="text-right text-sm font-medium text-gray-500 dark:text-gray-400 pb-3">Sessions</th>
                  <th class="text-right text-sm font-medium text-gray-500 dark:text-gray-400 pb-3">Conv. Rate</th>
                </tr>
              </thead>
              <tbody class="space-y-2">
                <% @form_performance.first(8).each do |form| %>
                  <tr class="border-b border-gray-100 dark:border-gray-700">
                    <td class="py-3">
                      <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white"><%= form[:name] %></p>
                        <p class="text-xs text-gray-500 dark:text-gray-400"><%= form[:website] %></p>
                      </div>
                    </td>
                    <td class="text-right py-3">
                      <span class="text-sm font-medium text-gray-900 dark:text-white"><%= number_with_delimiter(form[:sessions]) %></span>
                    </td>
                    <td class="text-right py-3">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= form[:conversion_rate] >= 10 ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' : form[:conversion_rate] >= 5 ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400' : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400' %>">
                        <%= form[:conversion_rate] %>%
                      </span>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        <% else %>
          <p class="text-center text-gray-500 dark:text-gray-400 py-8">No form performance data available</p>
        <% end %>
      </div>
      
      <!-- Field Analytics -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Field Analytics</h2>
        
        <% if @field_analytics.any? %>
          <div class="space-y-3">
            <% @field_analytics.first(8).each do |field_name, stats| %>
              <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-xl">
                <div class="flex-1">
                  <p class="text-sm font-medium text-gray-900 dark:text-white"><%= field_name %></p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    <%= stats[:total_interactions] %> interactions • <%= stats[:completion_rate] %>% completion
                  </p>
                </div>
                <div class="ml-4">
                  <div class="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div class="bg-gradient-to-r from-violet-500 to-indigo-500 h-2 rounded-full transition-all" 
                         style="width: <%= stats[:completion_rate] %>%"></div>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <p class="text-center text-gray-500 dark:text-gray-400 py-8">No field analytics data available</p>
        <% end %>
      </div>
    </div>

    <!-- Traffic & Device Analytics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6">
      
      <!-- Traffic Sources -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Traffic Sources</h2>
        
        <div class="space-y-3">
          <% @traffic_sources.each do |source, percentage| %>
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div class="w-3 h-3 bg-gradient-to-r from-violet-500 to-indigo-500 rounded-full"></div>
                <span class="text-sm text-gray-700 dark:text-gray-300"><%= source %></span>
              </div>
              <span class="text-sm font-semibold text-gray-900 dark:text-white"><%= percentage %>%</span>
            </div>
          <% end %>
        </div>
      </div>
      
      <!-- Device Breakdown -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Device Types</h2>
        
        <div class="space-y-3">
          <% @device_breakdown.each do |device, percentage| %>
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div class="w-3 h-3 bg-gradient-to-r from-green-500 to-blue-500 rounded-full"></div>
                <span class="text-sm text-gray-700 dark:text-gray-300"><%= device %></span>
              </div>
              <span class="text-sm font-semibold text-gray-900 dark:text-white"><%= percentage %>%</span>
            </div>
          <% end %>
        </div>
      </div>
      
      <!-- Browser Breakdown -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Browsers</h2>
        
        <div class="space-y-3">
          <% @browser_breakdown.each do |browser, percentage| %>
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div class="w-3 h-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"></div>
                <span class="text-sm text-gray-700 dark:text-gray-300"><%= browser %></span>
              </div>
              <span class="text-sm font-semibold text-gray-900 dark:text-white"><%= percentage %>%</span>
            </div>
          <% end %>
        </div>
      </div>
    </div>
    
  </div>
</div>