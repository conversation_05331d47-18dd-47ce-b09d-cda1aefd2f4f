class InsightGenerationJob < ApplicationJob
  queue_as :insights

  retry_on StandardError, wait: :exponentially_longer, attempts: 3
  discard_on ActiveJob::DeserializationError

  def perform(form_id)
    @form = Form.find(form_id)
    @website = @form.website

    Rails.logger.info "Generating insights for form #{form_id}"

    # Generate different types of insights
    performance_insights = analyze_performance
    user_behavior_insights = analyze_user_behavior
    field_optimization_insights = analyze_field_performance
    conversion_insights = analyze_conversion_patterns

    # Combine all insights
    all_insights = [
      performance_insights,
      user_behavior_insights,
      field_optimization_insights,
      conversion_insights
    ].flatten.compact

    # Save insights to database
    save_insights(all_insights)

    Rails.logger.info "Generated #{all_insights.count} insights for form #{form_id}"
  end

  private

  def analyze_performance
    insights = []

    # Low conversion rate insight
    if @form.conversion_rate < 15
      insights << {
        type: "performance",
        severity: "high",
        title: "Low Conversion Rate Alert",
        description: "Your form has a conversion rate of #{@form.conversion_rate}%, which is below the industry average of 15-25%.",
        recommendations: [
          "Reduce the number of required fields",
          "Improve form design and user experience",
          "Add progress indicators for multi-step forms",
          "Optimize field labels and descriptions"
        ],
        impact_score: calculate_impact_score("conversion_rate", @form.conversion_rate, 20),
        metrics: {
          current_rate: @form.conversion_rate,
          industry_average: 20,
          potential_improvement: estimate_conversion_improvement
        }
      }
    end

    # High abandonment rate insight
    if @form.abandonment_rate > 70
      insights << {
        type: "performance",
        severity: "high",
        title: "High Abandonment Rate",
        description: "#{@form.abandonment_rate}% of users abandon your form, indicating potential UX issues.",
        recommendations: [
          "Focus on the '#{@form.most_abandoned_field}' field - it's where most users drop off",
          "Simplify form validation and error messages",
          "Consider making some fields optional",
          "Reduce form length or break into steps"
        ],
        impact_score: calculate_impact_score("abandonment_rate", @form.abandonment_rate, 50),
        metrics: {
          current_rate: @form.abandonment_rate,
          most_abandoned_field: @form.most_abandoned_field,
          acceptable_rate: 50
        }
      }
    end

    # Completion time insight
    avg_time = @form.avg_completion_time
    if avg_time > 300 # 5 minutes
      insights << {
        type: "performance",
        severity: "medium",
        title: "Long Form Completion Time",
        description: "Users take an average of #{(avg_time / 60).round(1)} minutes to complete your form.",
        recommendations: [
          "Reduce the number of fields",
          "Use smart defaults and auto-fill where possible",
          "Improve field input types (dropdowns vs text)",
          "Add inline validation to prevent errors"
        ],
        impact_score: calculate_impact_score("completion_time", avg_time, 180),
        metrics: {
          current_time: avg_time,
          optimal_time: 180,
          time_savings_potential: avg_time - 180
        }
      }
    end

    insights
  end

  def analyze_user_behavior
    insights = []

    # Device-specific performance
    device_performance = analyze_device_performance
    if device_performance[:mobile_conversion] < device_performance[:desktop_conversion] * 0.7
      insights << {
        type: "user_behavior",
        severity: "medium",
        title: "Mobile Experience Issues",
        description: "Mobile users convert at #{device_performance[:mobile_conversion]}% vs #{device_performance[:desktop_conversion]}% on desktop.",
        recommendations: [
          "Optimize form for mobile devices",
          "Use mobile-friendly input types",
          "Ensure tap targets are large enough",
          "Test form on various mobile browsers"
        ],
        impact_score: 8,
        metrics: device_performance
      }
    end

    # Time-based patterns
    time_patterns = analyze_time_patterns
    if time_patterns[:peak_hours].any?
      insights << {
        type: "user_behavior",
        severity: "low",
        title: "Peak Usage Patterns",
        description: "Most form submissions occur during #{time_patterns[:peak_hours].join(', ')}.",
        recommendations: [
          "Consider targeted campaigns during peak hours",
          "Ensure server capacity during peak times",
          "Schedule maintenance during low-traffic periods"
        ],
        impact_score: 4,
        metrics: time_patterns
      }
    end

    # Referrer analysis
    referrer_insights = analyze_referrer_performance
    if referrer_insights[:top_converting_referrer]
      insights << {
        type: "user_behavior",
        severity: "low",
        title: "High-Converting Traffic Sources",
        description: "Users from #{referrer_insights[:top_converting_referrer]} convert #{referrer_insights[:top_conversion_rate]}% better.",
        recommendations: [
          "Invest more in high-converting traffic sources",
          "Analyze what makes these users more likely to convert",
          "Create targeted landing pages for top referrers"
        ],
        impact_score: 6,
        metrics: referrer_insights
      }
    end

    insights
  end

  def analyze_field_performance
    insights = []

    # Field-level analysis
    problem_fields = identify_problem_fields
    problem_fields.each do |field_data|
      insights << {
        type: "field_optimization",
        severity: field_data[:severity],
        title: "Field Issue: #{field_data[:field_name]}",
        description: field_data[:description],
        recommendations: field_data[:recommendations],
        impact_score: field_data[:impact_score],
        metrics: field_data[:metrics],
        field_name: field_data[:field_name]
      }
    end

    # Form structure insights
    field_count = @form.form_fields.count
    if field_count > 10
      insights << {
        type: "field_optimization",
        severity: "medium",
        title: "Too Many Form Fields",
        description: "Your form has #{field_count} fields. Research shows conversion drops significantly after 7 fields.",
        recommendations: [
          "Remove non-essential fields",
          "Make optional fields clearly marked",
          "Consider progressive disclosure",
          "Break long forms into multiple steps"
        ],
        impact_score: 7,
        metrics: {
          current_fields: field_count,
          recommended_max: 7,
          excess_fields: field_count - 7
        }
      }
    end

    insights
  end

  def analyze_conversion_patterns
    insights = []

    # Seasonal patterns
    seasonal_data = analyze_seasonal_trends
    if seasonal_data[:has_pattern]
      insights << {
        type: "conversion",
        severity: "low",
        title: "Seasonal Conversion Patterns",
        description: "Form performance varies by #{seasonal_data[:pattern_type]} with peak conversion in #{seasonal_data[:peak_period]}.",
        recommendations: [
          "Plan campaigns around high-conversion periods",
          "Adjust form content for seasonal relevance",
          "Prepare for traffic spikes during peak times"
        ],
        impact_score: 5,
        metrics: seasonal_data
      }
    end

    # A/B testing opportunities
    ab_opportunities = identify_ab_test_opportunities
    ab_opportunities.each do |opportunity|
      insights << {
        type: "conversion",
        severity: "medium",
        title: "A/B Test Opportunity: #{opportunity[:element]}",
        description: opportunity[:description],
        recommendations: opportunity[:test_ideas],
        impact_score: opportunity[:impact_score],
        metrics: opportunity[:metrics],
        test_element: opportunity[:element]
      }
    end

    insights
  end

  # Helper methods for analysis

  def analyze_device_performance
    sessions_by_device = @form.form_sessions.group(:device_type).count
    submissions_by_device = @form.form_submissions
                                 .joins(:form_session)
                                 .group("form_sessions.device_type")
                                 .count

    performance = {}
    sessions_by_device.each do |device, sessions|
      submissions = submissions_by_device[device] || 0
      conversion_rate = sessions > 0 ? (submissions.to_f / sessions * 100).round(2) : 0
      performance["#{device}_sessions"] = sessions
      performance["#{device}_conversion"] = conversion_rate
    end

    performance
  end

  def analyze_time_patterns
    # Analyze hourly patterns
    hourly_sessions = @form.form_sessions
                          .group("EXTRACT(hour FROM started_at)")
                          .count

    # Find peak hours (top 3 hours with most activity)
    peak_hours = hourly_sessions.sort_by { |hour, count| -count }
                               .first(3)
                               .map { |hour, count| "#{hour}:00-#{hour + 1}:00" }

    {
      peak_hours: peak_hours,
      hourly_distribution: hourly_sessions
    }
  end

  def analyze_referrer_performance
    referrer_data = @form.form_sessions
                        .where.not(referrer: [ nil, "" ])
                        .group(:referrer)
                        .count

    return {} if referrer_data.empty?

    # Calculate conversion rates by referrer
    referrer_conversions = {}
    referrer_data.each do |referrer, sessions|
      submissions = @form.form_submissions
                        .joins(:form_session)
                        .where(form_sessions: { referrer: referrer })
                        .count

      conversion_rate = sessions > 0 ? (submissions.to_f / sessions * 100).round(2) : 0
      referrer_conversions[referrer] = {
        sessions: sessions,
        submissions: submissions,
        conversion_rate: conversion_rate
      }
    end

    # Find top performer
    top_referrer = referrer_conversions.max_by { |referrer, data| data[:conversion_rate] }

    {
      top_converting_referrer: extract_domain(top_referrer[0]),
      top_conversion_rate: top_referrer[1][:conversion_rate],
      all_referrers: referrer_conversions
    }
  end

  def identify_problem_fields
    problem_fields = []

    @form.form_fields.includes(:field_events).each do |field|
      field_events = field.field_events
      next if field_events.empty?

      # Calculate field-specific metrics
      focus_events = field_events.where(event_type: "focus").count
      error_events = field_events.where(event_type: "error").count
      change_events = field_events.where(event_type: "change").count

      error_rate = focus_events > 0 ? (error_events.to_f / focus_events * 100).round(2) : 0
      correction_rate = focus_events > 0 ? (change_events.to_f / focus_events * 100).round(2) : 0

      # Identify problems
      if error_rate > 20
        problem_fields << {
          field_name: field.name,
          severity: "high",
          description: "Field '#{field.name}' has a #{error_rate}% error rate, indicating validation or UX issues.",
          recommendations: [
            "Improve field validation messages",
            "Add field format hints or examples",
            "Consider changing field type or input method",
            "Review required vs optional status"
          ],
          impact_score: 9,
          metrics: {
            error_rate: error_rate,
            total_errors: error_events,
            total_interactions: focus_events
          }
        }
      elsif correction_rate > 50
        problem_fields << {
          field_name: field.name,
          severity: "medium",
          description: "Users frequently correct their input in '#{field.name}' (#{correction_rate}% correction rate).",
          recommendations: [
            "Add input formatting or masks",
            "Provide clearer labels or instructions",
            "Consider using dropdowns instead of free text",
            "Add real-time validation feedback"
          ],
          impact_score: 6,
          metrics: {
            correction_rate: correction_rate,
            total_corrections: change_events,
            total_interactions: focus_events
          }
        }
      end
    end

    problem_fields
  end

  def analyze_seasonal_trends
    # Analyze data over the past 3 months to identify patterns
    three_months_ago = 3.months.ago
    monthly_data = @form.form_sessions
                       .where(started_at: three_months_ago..Time.current)
                       .group("DATE_TRUNC('month', started_at)")
                       .count

    return { has_pattern: false } if monthly_data.size < 2

    # Simple pattern detection (could be made more sophisticated)
    values = monthly_data.values
    avg = values.sum.to_f / values.size
    variance = values.map { |v| (v - avg) ** 2 }.sum / values.size

    has_significant_variation = variance > (avg * 0.3)

    if has_significant_variation
      peak_month = monthly_data.max_by { |month, count| count }
      {
        has_pattern: true,
        pattern_type: "monthly",
        peak_period: peak_month[0].strftime("%B %Y"),
        monthly_data: monthly_data,
        variance: variance.round(2)
      }
    else
      { has_pattern: false }
    end
  end

  def identify_ab_test_opportunities
    opportunities = []

    # Test opportunity: Form title/headline
    if @form.conversion_rate < 25
      opportunities << {
        element: "Form Headline",
        description: "Testing different headlines could improve initial engagement and conversion rates.",
        test_ideas: [
          "Test benefit-focused vs feature-focused headlines",
          "Try different lengths (short vs descriptive)",
          "Test urgency or scarcity messaging",
          "A/B test with/without subheadlines"
        ],
        impact_score: 8,
        metrics: {
          current_conversion: @form.conversion_rate,
          potential_lift: "15-30%"
        }
      }
    end

    # Test opportunity: Form layout
    if @form.form_fields.count > 5
      opportunities << {
        element: "Form Layout",
        description: "Multi-step forms often convert better than long single-page forms.",
        test_ideas: [
          "Test single-page vs multi-step layout",
          "Try different field groupings",
          "Test with/without progress indicators",
          "Experiment with field ordering"
        ],
        impact_score: 7,
        metrics: {
          current_fields: @form.form_fields.count,
          completion_rate: (100 - @form.abandonment_rate).round(2)
        }
      }
    end

    opportunities
  end

  def calculate_impact_score(metric_type, current_value, target_value)
    # Calculate impact score from 1-10 based on how far from target
    case metric_type
    when "conversion_rate"
      gap = target_value - current_value
      [ [ (gap / target_value * 10).round, 1 ].max, 10 ].min
    when "abandonment_rate"
      gap = current_value - target_value
      [ [ (gap / target_value * 10).round, 1 ].max, 10 ].min
    when "completion_time"
      gap = current_value - target_value
      [ [ (gap / target_value * 10).round, 1 ].max, 10 ].min
    else
      5 # Default moderate impact
    end
  end

  def estimate_conversion_improvement
    # Simple estimation based on industry benchmarks
    current_rate = @form.conversion_rate
    field_count = @form.form_fields.count

    # Estimate improvement potential
    potential_rate = case field_count
    when 1..3 then 35
    when 4..7 then 25
    when 8..12 then 18
    else 12
    end

    improvement = potential_rate - current_rate
    [ improvement, 0 ].max.round(1)
  end

  def extract_domain(url)
    URI.parse(url).host
  rescue URI::InvalidURIError
    url
  end

  def save_insights(insights_data)
    # Delete old insights for this form
    @form.insights.destroy_all

    # Save new insights
    insights_data.each do |insight_data|
      @form.insights.create!(
        insight_type: insight_data[:type],
        severity: insight_data[:severity],
        title: insight_data[:title],
        description: insight_data[:description],
        recommendations: insight_data[:recommendations],
        impact_score: insight_data[:impact_score],
        metadata: {
          metrics: insight_data[:metrics],
          field_name: insight_data[:field_name],
          test_element: insight_data[:test_element]
        }.compact,
        generated_at: Time.current
      )
    end
  end
end
