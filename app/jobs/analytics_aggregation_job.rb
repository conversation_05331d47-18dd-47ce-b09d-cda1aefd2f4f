class AnalyticsAggregationJob < ApplicationJob
  queue_as :analytics

  retry_on StandardError, wait: :exponentially_longer, attempts: 3
  discard_on ActiveJob::DeserializationError

  def perform(website_id, time_range = "hourly")
    @website = Website.find(website_id)
    @time_range = time_range

    Rails.logger.info "Running analytics aggregation for website #{website_id} (#{time_range})"

    case time_range
    when "hourly"
      aggregate_hourly_data
    when "daily"
      aggregate_daily_data
    when "weekly"
      aggregate_weekly_data
    when "monthly"
      aggregate_monthly_data
    end

    # Broadcast updated analytics to real-time channels
    broadcast_analytics_update
  end

  private

  def aggregate_hourly_data
    current_hour = Time.current.beginning_of_hour

    @website.forms.find_each do |form|
      # Sessions started this hour
      sessions_count = form.form_sessions
                          .where(started_at: current_hour..current_hour.end_of_hour)
                          .count

      # Submissions this hour
      submissions_count = form.form_submissions
                             .where(submitted_at: current_hour..current_hour.end_of_hour)
                             .count

      # Abandonments this hour
      abandonments_count = form.form_sessions
                              .abandoned
                              .where(abandoned_at: current_hour..current_hour.end_of_hour)
                              .count

      # Calculate conversion rate
      conversion_rate = sessions_count > 0 ? (submissions_count.to_f / sessions_count * 100).round(2) : 0
      abandonment_rate = sessions_count > 0 ? (abandonments_count.to_f / sessions_count * 100).round(2) : 0

      # Store aggregated data (you might want to create an AnalyticsSnapshot model)
      Rails.cache.write(
        "analytics:#{form.id}:hourly:#{current_hour.to_i}",
        {
          sessions: sessions_count,
          submissions: submissions_count,
          abandonments: abandonments_count,
          conversion_rate: conversion_rate,
          abandonment_rate: abandonment_rate,
          timestamp: current_hour
        },
        expires_in: 25.hours
      )

      # Update form metrics
      form.update_columns(
        total_sessions: form.form_sessions.count,
        total_submissions: form.form_submissions.count,
        conversion_rate: calculate_overall_conversion_rate(form),
        abandonment_rate: calculate_overall_abandonment_rate(form)
      )
    end
  end

  def aggregate_daily_data
    current_day = Date.current

    @website.forms.find_each do |form|
      # Get sessions for today
      sessions_today = form.form_sessions
                          .where(started_at: current_day.beginning_of_day..current_day.end_of_day)

      # Calculate metrics
      metrics = {
        total_sessions: sessions_today.count,
        completed_sessions: sessions_today.completed.count,
        abandoned_sessions: sessions_today.abandoned.count,
        avg_completion_time: sessions_today.completed.average(:completion_time)&.round(2) || 0,
        bounce_rate: calculate_bounce_rate(sessions_today),
        top_exit_field: find_top_exit_field(sessions_today),
        device_breakdown: calculate_device_breakdown(sessions_today),
        referrer_breakdown: calculate_referrer_breakdown(sessions_today)
      }

      # Store daily aggregated data
      Rails.cache.write(
        "analytics:#{form.id}:daily:#{current_day}",
        metrics,
        expires_in: 8.days
      )
    end
  end

  def aggregate_weekly_data
    # Weekly aggregation logic
    current_week = Date.current.beginning_of_week

    @website.forms.find_each do |form|
      weekly_metrics = calculate_weekly_metrics(form, current_week)

      Rails.cache.write(
        "analytics:#{form.id}:weekly:#{current_week.cweek}",
        weekly_metrics,
        expires_in: 5.weeks
      )
    end
  end

  def aggregate_monthly_data
    # Monthly aggregation logic
    current_month = Date.current.beginning_of_month

    @website.forms.find_each do |form|
      monthly_metrics = calculate_monthly_metrics(form, current_month)

      Rails.cache.write(
        "analytics:#{form.id}:monthly:#{current_month.strftime('%Y-%m')}",
        monthly_metrics,
        expires_in: 13.months
      )
    end
  end

  def calculate_overall_conversion_rate(form)
    total_sessions = form.form_sessions.count
    total_submissions = form.form_submissions.count

    return 0 if total_sessions == 0
    (total_submissions.to_f / total_sessions * 100).round(2)
  end

  def calculate_overall_abandonment_rate(form)
    total_sessions = form.form_sessions.count
    abandoned_sessions = form.form_sessions.abandoned.count

    return 0 if total_sessions == 0
    (abandoned_sessions.to_f / total_sessions * 100).round(2)
  end

  def calculate_bounce_rate(sessions)
    # Define bounce as sessions with no field interactions
    total = sessions.count
    return 0 if total == 0

    bounced = sessions.joins(:field_events)
                     .group("form_sessions.id")
                     .having("COUNT(field_events.id) = 0")
                     .count
                     .size

    (bounced.to_f / total * 100).round(2)
  end

  def find_top_exit_field(sessions)
    # Find the field where most users abandon
    sessions.abandoned
            .joins(:field_events)
            .group("field_events.form_field_id")
            .order("COUNT(*) DESC")
            .limit(1)
            .first
            &.field_events
            &.first
            &.form_field
            &.name
  end

  def calculate_device_breakdown(sessions)
    sessions.group(:device_type).count
  end

  def calculate_referrer_breakdown(sessions)
    sessions.where.not(referrer: [ nil, "" ])
            .group(:referrer)
            .count
            .transform_keys { |referrer| URI.parse(referrer).host rescue referrer }
  rescue URI::InvalidURIError
    {}
  end

  def calculate_weekly_metrics(form, start_date)
    # Weekly specific metrics
    {
      week_start: start_date,
      total_sessions: form.form_sessions.where(started_at: start_date..start_date.end_of_week).count,
      conversion_trend: calculate_daily_conversion_trend(form, start_date, 7),
      popular_days: calculate_popular_days(form, start_date),
      peak_hours: calculate_peak_hours(form, start_date)
    }
  end

  def calculate_monthly_metrics(form, start_date)
    # Monthly specific metrics
    {
      month_start: start_date,
      total_sessions: form.form_sessions.where(started_at: start_date..start_date.end_of_month).count,
      weekly_breakdown: calculate_weekly_breakdown(form, start_date),
      growth_rate: calculate_growth_rate(form, start_date),
      cohort_analysis: perform_cohort_analysis(form, start_date)
    }
  end

  def calculate_daily_conversion_trend(form, start_date, days)
    (0...days).map do |i|
      day = start_date + i.days
      sessions = form.form_sessions.where(started_at: day.beginning_of_day..day.end_of_day)
      submissions = form.form_submissions.where(submitted_at: day.beginning_of_day..day.end_of_day)

      {
        date: day.to_date,
        sessions: sessions.count,
        submissions: submissions.count,
        conversion_rate: sessions.count > 0 ? (submissions.count.to_f / sessions.count * 100).round(2) : 0
      }
    end
  end

  def calculate_popular_days(form, start_date)
    form.form_sessions
        .where(started_at: start_date..start_date.end_of_week)
        .group("DATE_TRUNC('day', started_at)")
        .order("COUNT(*) DESC")
        .count
        .transform_keys { |date| date.strftime("%A") }
  end

  def calculate_peak_hours(form, start_date)
    form.form_sessions
        .where(started_at: start_date..start_date.end_of_week)
        .group("EXTRACT(hour FROM started_at)")
        .order("COUNT(*) DESC")
        .count
        .transform_keys(&:to_i)
  end

  def calculate_weekly_breakdown(form, start_date)
    weeks_in_month = ((start_date.end_of_month - start_date) / 7).ceil

    (0...weeks_in_month).map do |week_num|
      week_start = start_date + (week_num * 7).days
      week_end = [ week_start.end_of_week, start_date.end_of_month ].min

      {
        week: week_num + 1,
        start_date: week_start,
        end_date: week_end,
        sessions: form.form_sessions.where(started_at: week_start..week_end).count
      }
    end
  end

  def calculate_growth_rate(form, current_month)
    previous_month = current_month - 1.month

    current_sessions = form.form_sessions
                          .where(started_at: current_month..current_month.end_of_month)
                          .count

    previous_sessions = form.form_sessions
                           .where(started_at: previous_month..previous_month.end_of_month)
                           .count

    return 0 if previous_sessions == 0
    ((current_sessions - previous_sessions).to_f / previous_sessions * 100).round(2)
  end

  def perform_cohort_analysis(form, month_start)
    # Simple cohort analysis - users who started in this month
    cohort_users = form.form_sessions
                      .where(started_at: month_start..month_start.end_of_month)
                      .distinct
                      .pluck(:visitor_id)

    # Track their activity in subsequent weeks
    cohort_data = []
    (0..4).each do |week_offset|
      week_start = month_start + (week_offset * 7).days
      week_end = week_start + 6.days

      active_users = form.form_sessions
                        .where(visitor_id: cohort_users)
                        .where(started_at: week_start..week_end)
                        .distinct
                        .count(:visitor_id)

      retention_rate = cohort_users.size > 0 ? (active_users.to_f / cohort_users.size * 100).round(2) : 0

      cohort_data << {
        week: week_offset,
        active_users: active_users,
        retention_rate: retention_rate
      }
    end

    cohort_data
  end

  def broadcast_analytics_update
    # Broadcast to all forms for this website
    @website.forms.find_each do |form|
      ActionCable.server.broadcast(
        "form_analytics_#{form.id}",
        {
          type: "analytics_update",
          form_id: form.id,
          metrics: {
            total_sessions: form.total_sessions,
            total_submissions: form.total_submissions,
            conversion_rate: form.conversion_rate,
            abandonment_rate: form.abandonment_rate
          },
          timestamp: Time.current.to_i
        }
      )
    end

    # Broadcast to website channel
    ActionCable.server.broadcast(
      "website_analytics_#{@website.id}",
      {
        type: "website_analytics_update",
        website_id: @website.id,
        total_sessions: @website.forms.sum(:total_sessions),
        total_submissions: @website.forms.sum(:total_submissions),
        avg_conversion_rate: @website.forms.average(:conversion_rate)&.round(2) || 0,
        forms: @website.forms.map do |form|
          {
            id: form.id,
            name: form.name,
            sessions: form.total_sessions,
            conversion_rate: form.conversion_rate
          }
        end,
        timestamp: Time.current.to_i
      }
    )
  end
end
