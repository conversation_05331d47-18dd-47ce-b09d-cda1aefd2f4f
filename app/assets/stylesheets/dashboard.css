/* Dashboard Theme Styles */

/* Card styles that work for both light and dark modes */
.dashboard-card {
  @apply bg-white dark:bg-gray-800/50 backdrop-blur border border-gray-200 dark:border-gray-700/50 rounded-2xl p-6 shadow-lg dark:shadow-xl transition-all duration-200;
}

.dashboard-card-hover {
  @apply hover:shadow-xl dark:hover:shadow-2xl hover:border-gray-300 dark:hover:border-gray-600/50;
}

/* Text colors for better contrast */
.dashboard-heading {
  @apply text-gray-900 dark:text-white font-semibold;
}

.dashboard-text {
  @apply text-gray-600 dark:text-gray-300;
}

.dashboard-subtext {
  @apply text-gray-500 dark:text-gray-400;
}

/* Button styles */
.dashboard-btn {
  @apply px-4 py-2 bg-gray-100 dark:bg-gray-800/50 backdrop-blur border border-gray-300 dark:border-gray-700/50 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700/50 transition-all;
}

.dashboard-btn-active {
  @apply bg-purple-50 dark:bg-gray-700/50 border-purple-500 dark:border-purple-500/50 text-purple-600 dark:text-white;
}

/* Progress bars */
.dashboard-progress-bg {
  @apply bg-gray-200 dark:bg-gray-700;
}

/* Interactive elements */
.dashboard-interactive {
  @apply bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer;
}

/* Gradient cards maintain their colors in both modes */
.dashboard-gradient-purple {
  @apply bg-gradient-to-br from-purple-500 to-purple-600 text-white shadow-xl border border-purple-400/20;
}

.dashboard-gradient-pink {
  @apply bg-gradient-to-br from-pink-500 to-pink-600 text-white shadow-xl border border-pink-400/20;
}

/* Dark mode toggle animation */
.dark-mode-transition {
  @apply transition-colors duration-300 ease-in-out;
}