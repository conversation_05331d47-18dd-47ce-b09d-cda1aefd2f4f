class FormField < ApplicationRecord
  # Associations
  belongs_to :form
  has_many :field_events, dependent: :destroy

  # Validations
  validates :name, presence: true, uniqueness: { scope: :form_id }
  validates :position, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true

  # Scopes
  scope :ordered, -> { order(:position, :created_at) }
  scope :required_fields, -> { where(required: true) }
  scope :high_error_rate, -> { where("error_rate > ?", 0.1) }
  scope :high_abandonment, -> { where("abandonment_rate > ?", 0.3) }

  # Callbacks
  before_save :calculate_rates
  after_create :update_form_field_count
  after_destroy :update_form_field_count

  # Common field types
  FIELD_TYPES = %w[
    text email password tel url number
    textarea select checkbox radio
    date time datetime file
    hidden submit button
  ].freeze

  # Instance methods
  def calculate_error_rate
    return 0.0 if interaction_count.zero?
    (error_count.to_f / interaction_count * 100).round(2)
  end

  def calculate_correction_rate
    return 0.0 if error_count.zero?
    (correction_count.to_f / error_count * 100).round(2)
  end

  def calculate_abandonment_rate
    return 0.0 if interaction_count.zero?

    # Count sessions where this field was the last interaction
    last_field_events = field_events
      .joins(:form_session)
      .where(form_sessions: { status: "abandoned" })
      .where('field_events.created_at = (
        SELECT MAX(fe2.created_at)
        FROM field_events fe2
        WHERE fe2.form_session_id = field_events.form_session_id
      )')
      .count

    (last_field_events.to_f / interaction_count * 100).round(2)
  end

  def track_interaction(event_type = "focus")
    increment!(:interaction_count)
    increment!(:error_count) if event_type == "error"
    increment!(:correction_count) if event_type == "correction"
  end

  def problematic?
    error_rate > 10 || abandonment_rate > 30
  end

  def validation_summary
    {
      required: required?,
      type: field_type,
      rules: validation_rules,
      error_rate: error_rate,
      abandonment_rate: abandonment_rate
    }
  end

  private

  def calculate_rates
    self.error_rate = calculate_error_rate
    self.correction_rate = calculate_correction_rate
    # Abandonment rate calculation requires more complex logic with events
    # Will be updated by background job
  end

  def update_form_field_count
    form.update_field_count
  end
end
