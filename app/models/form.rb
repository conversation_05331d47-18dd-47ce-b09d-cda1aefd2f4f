class Form < ApplicationRecord
  # Associations
  belongs_to :website
  has_one :account, through: :website
  has_many :form_fields, dependent: :destroy
  has_many :form_sessions, dependent: :destroy
  has_many :form_submissions, dependent: :destroy
  has_many :field_events, through: :form_fields
  has_many :insights, dependent: :destroy

  # Validations
  validates :form_identifier, presence: true, uniqueness: { scope: :website_id }
  validates :url, presence: true

  # Scopes
  scope :active_forms, -> { where("last_seen_at > ?", 7.days.ago) }
  scope :high_abandonment, -> { where("abandonment_rate > ?", 0.7) }
  scope :low_conversion, -> { where("conversion_rate < ?", 0.3) }
  scope :by_conversion_rate, -> { order(conversion_rate: :desc) }
  scope :by_abandonment_rate, -> { order(abandonment_rate: :desc) }

  # Callbacks
  before_save :calculate_metrics
  after_create :set_first_seen_at

  # Instance methods
  def active?
    last_seen_at.present? && last_seen_at > 7.days.ago
  end

  def calculate_conversion_rate
    return 0.0 if total_sessions.zero?
    (total_submissions.to_f / total_sessions * 100).round(2)
  end

  def calculate_abandonment_rate
    return 0.0 if total_sessions.zero?
    abandoned = total_sessions - total_submissions
    (abandoned.to_f / total_sessions * 100).round(2)
  end

  def calculate_avg_completion_time
    recent_submissions = form_submissions.where("created_at > ?", 30.days.ago)
    return nil if recent_submissions.empty?

    times = recent_submissions.pluck(:completion_time).compact
    return nil if times.empty?

    (times.sum.to_f / times.size).round(2)
  end

  def update_field_count
    update_column(:field_count, form_fields.count)
  end

  def track_session_start
    increment!(:total_sessions)
    touch(:last_seen_at)
  end

  def track_submission
    increment!(:total_submissions)
    calculate_metrics
    save!
  end

  def field_performance_data
    form_fields.includes(:field_events).map do |field|
      {
        name: field.name,
        type: field.field_type,
        interaction_count: field.field_events.count,
        avg_time_spent: field.avg_interaction_time,
        error_count: field.error_count,
        abandonment_rate: field.calculate_abandonment_rate
      }
    end
  end

  def generate_insights
    InsightGeneratorService.new(self).generate
  end

  private

  def calculate_metrics
    self.conversion_rate = calculate_conversion_rate
    self.abandonment_rate = calculate_abandonment_rate
    self.avg_completion_time = calculate_avg_completion_time if total_submissions > 0
  end

  def set_first_seen_at
    update_column(:first_seen_at, Time.current) if first_seen_at.blank?
  end
end
