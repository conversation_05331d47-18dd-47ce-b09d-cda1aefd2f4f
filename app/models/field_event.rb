# frozen_string_literal: true

# FieldEvent represents an individual interaction with a form field
class FieldEvent < ApplicationRecord
  # Associations
  belongs_to :form_session
  belongs_to :form_field

  # Validations
  validates :event_type, presence: true, inclusion: {
    in: %w[focus blur change input error correction validation submit]
  }
  validates :timestamp, presence: true

  # Scopes
  scope :errors, -> { where(event_type: "error") }
  scope :corrections, -> { where(event_type: "correction") }
  scope :recent, -> { order(timestamp: :desc) }
  scope :by_field, ->(field_id) { where(form_field_id: field_id) }
  scope :by_session, ->(session_id) { where(form_session_id: session_id) }

  # Callbacks
  after_create :update_field_metrics
  after_create :track_field_interaction

  # Instance methods
  def duration_seconds
    return 0 unless duration
    duration / 1000.0
  end

  def duration_formatted
    return "N/A" unless duration
    seconds = duration_seconds
    if seconds < 60
      "#{seconds.round(1)}s"
    else
      "#{(seconds / 60).round(1)}m"
    end
  end

  def error?
    event_type == "error"
  end

  def correction?
    event_type == "correction"
  end

  def interaction?
    %w[focus blur change input].include?(event_type)
  end

  private

  def update_field_metrics
    # Queue job to update form metrics (which includes field metrics)
    FormMetricsUpdateJob.perform_later(form_field.form_id)
  end

  def track_field_interaction
    form_field.increment!(:interaction_count) if form_field.respond_to?(:interaction_count)
    form_session.increment!(:interaction_count) if form_session.respond_to?(:interaction_count)
  end
end
