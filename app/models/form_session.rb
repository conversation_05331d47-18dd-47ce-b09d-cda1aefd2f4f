class FormSession < ApplicationRecord
  # Associations
  belongs_to :form
  has_many :field_events, dependent: :destroy
  has_one :form_submission, dependent: :destroy

  # Validations
  validates :visitor_id, presence: true
  validates :session_id, presence: true, uniqueness: true
  validates :started_at, presence: true
  validates :status, inclusion: { in: %w[in_progress completed abandoned] }

  # Status values (using string values instead of enum)

  # Scopes
  scope :recent, -> { order(started_at: :desc) }
  scope :completed_sessions, -> { where(status: "completed") }
  scope :abandoned_sessions, -> { where(status: "abandoned") }
  scope :in_progress_sessions, -> { where(status: "in_progress") }
  scope :by_date_range, ->(start_date, end_date) { where(started_at: start_date..end_date) }

  # Callbacks
  before_validation :set_defaults, on: :create
  after_update :update_form_metrics, if: :saved_change_to_status?

  # Instance methods
  def completed?
    status == "completed"
  end

  def abandoned?
    status == "abandoned"
  end

  def in_progress?
    status == "in_progress"
  end

  def complete!
    return if completed?

    update!(
      status: "completed",
      completed_at: Time.current,
      time_spent: calculate_time_spent
    )
    form.track_submission if form.respond_to?(:track_submission)
  end

  def abandon!
    return if abandoned? || completed?

    update!(
      status: "abandoned",
      abandoned_at: Time.current,
      time_spent: calculate_time_spent
    )
  end

  def duration
    end_time = completed_at || abandoned_at || Time.current
    (end_time - started_at).to_i
  end

  def calculate_time_spent
    return time_spent if time_spent.present?

    # Calculate from field events if available
    events = field_events.order(:created_at)
    return 0 if events.empty?

    total_time = 0
    events.each_cons(2) do |prev_event, curr_event|
      time_diff = (curr_event.created_at - prev_event.created_at).to_f
      # Cap individual interactions at 5 minutes to filter out idle time
      total_time += [ time_diff, 300 ].min
    end

    total_time.round(2)
  end

  def field_interaction_sequence
    field_events.includes(:form_field).order(:created_at).map do |event|
      {
        field: event.form_field.name,
        event_type: event.event_type,
        timestamp: event.created_at,
        duration: event.duration
      }
    end
  end

  def last_interaction_field
    field_events.order(:created_at).last&.form_field
  end

  def device_info
    {
      device_type: device_type,
      browser: browser,
      operating_system: operating_system,
      user_agent: user_agent
    }
  end

  def location_info
    {
      country: country,
      city: city,
      ip_address: ip_address
    }
  end

  private

  def set_defaults
    self.session_id ||= SecureRandom.uuid
    self.started_at ||= Time.current
    self.status ||= "in_progress"
  end

  def update_form_metrics
    FormMetricsUpdateJob.perform_later(form.id)
  end
end
