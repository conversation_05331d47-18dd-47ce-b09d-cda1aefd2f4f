class Website < ApplicationRecord
  # Associations
  belongs_to :account
  has_many :forms, dependent: :destroy
  has_many :form_submissions, through: :forms
  has_many :form_sessions, through: :forms

  # Validations
  validates :domain, presence: true, uniqueness: { scope: :account_id }
  validates :name, presence: true, length: { maximum: 100 }
  validates :tracking_id, presence: true, uniqueness: true

  # Enums
  enum :status, { active: 0, inactive: 1, suspended: 2 }

  # Callbacks
  before_validation :generate_tracking_id, on: :create
  before_validation :generate_tracking_token, on: :create
  before_validation :generate_verification_token, on: :create
  before_validation :normalize_domain

  # Scopes
  scope :verified, -> { where.not(verified_at: nil) }
  scope :unverified, -> { where(verified_at: nil) }
  scope :active_websites, -> { active.verified }

  # Instance methods
  def verified?
    verified_at.present?
  end

  def verify!
    update!(verified_at: Time.current) unless verified?
  end

  def can_track?
    active? && verified? && account.can_track_events?
  end

  def tracking_script
    <<~HTML
      <!-- FormFlow Pro Analytics -->
      <script>
        window.FORMFLOW_TOKEN = '#{tracking_token}';
        window.FORMFLOW_ENDPOINT = '#{Rails.application.routes.url_helpers.api_v1_track_url(host: ENV['APP_HOST'] || 'formflowpro.fly.dev', protocol: 'https')}';
        (function() {
          var script = document.createElement('script');
          script.src = '#{Rails.application.routes.url_helpers.root_url(host: ENV['APP_HOST'] || 'formflowpro.fly.dev', protocol: 'https')}formflow-tracker.js';
          script.async = true;
          document.head.appendChild(script);
        })();
      </script>
      <!-- End FormFlow Pro Analytics -->
    HTML
  end

  private

  def generate_tracking_id
    self.tracking_id = "FFP-#{SecureRandom.hex(8).upcase}" if tracking_id.blank?
  end

  def generate_tracking_token
    self.tracking_token = SecureRandom.urlsafe_base64(32) if tracking_token.blank?
  end

  def generate_verification_token
    self.verification_token = SecureRandom.hex(16) if verification_token.blank?
  end

  def normalize_domain
    return if domain.blank?

    # Remove protocol and www
    normalized = domain.downcase
                      .gsub(/^https?:\/\//, "")
                      .gsub(/^www\./, "")
                      .gsub(/\/$/, "")

    self.domain = normalized
  end
end
