# frozen_string_literal: true

# Session represents a user's interaction with a form
class Session < ApplicationRecord
  # Associations
  belongs_to :form
  has_one :website, through: :form
  has_many :field_events, dependent: :destroy

  # Validations
  validates :visitor_id, presence: true
  validates :session_id, presence: true, uniqueness: true
  validates :status, inclusion: { in: %w[in_progress completed abandoned] }
  validates :started_at, presence: true

  # Callbacks
  before_validation :generate_session_id, on: :create
  before_validation :set_started_at, on: :create
  after_update :track_status_change

  # Scopes
  scope :completed, -> { where(status: "completed") }
  scope :abandoned, -> { where(status: "abandoned") }
  scope :in_progress, -> { where(status: "in_progress") }
  scope :recent, -> { order(started_at: :desc) }
  scope :today, -> { where("started_at >= ?", Time.current.beginning_of_day) }
  scope :this_week, -> { where("started_at >= ?", Time.current.beginning_of_week) }
  scope :this_month, -> { where("started_at >= ?", Time.current.beginning_of_month) }

  # State machine using AASM
  include AASM

  aasm column: :status do
    state :in_progress, initial: true
    state :completed
    state :abandoned

    event :complete do
      transitions from: :in_progress, to: :completed
      after do
        update!(completed_at: Time.current)
        form.insights.create!(
          type: "conversion",
          severity: "info",
          title: "Form completed successfully",
          description: "Session #{session_id} completed in #{duration_seconds} seconds"
        ) if duration_seconds < 30
      end
    end

    event :abandon do
      transitions from: :in_progress, to: :abandoned
      after do
        update!(abandoned_at: Time.current)
        analyze_abandonment_reason
      end
    end
  end

  # Instance methods
  def duration
    end_time = completed_at || abandoned_at || Time.current
    end_time - started_at
  end

  def duration_seconds
    duration.to_i
  end

  def fields_interacted
    field_events.select(:form_field_id).distinct.count
  end

  def last_field_interacted
    field_events.order(timestamp: :desc).first&.form_field
  end

  def completion_percentage
    return 100.0 if completed?
    return 0.0 if form.form_fields.empty?

    (fields_interacted.to_f / form.form_fields.count * 100).round(2)
  end

  def time_per_field
    return {} if field_events.empty?

    field_events.where(event_type: "blur")
                .group(:form_field_id)
                .average(:duration)
  end

  def error_fields
    field_events.where(event_type: "error")
                .joins(:form_field)
                .select("form_fields.field_name, form_fields.field_label")
                .distinct
  end

  def hesitation_points
    # Fields where user spent > 30 seconds
    field_events.where(event_type: "blur")
                .where("duration > ?", 30000) # milliseconds
                .joins(:form_field)
                .pluck("form_fields.field_name")
  end

  def device_category
    case device_type&.downcase
    when /mobile|android|iphone/
      "mobile"
    when /tablet|ipad/
      "tablet"
    else
      "desktop"
    end
  end

  # Analytics methods
  def replay_data
    field_events.order(:timestamp).map do |event|
      {
        field: event.form_field.field_name,
        event_type: event.event_type,
        timestamp: event.timestamp,
        duration: event.duration,
        error: event.error_message
      }
    end
  end

  private

  def generate_session_id
    self.session_id ||= "sess_#{SecureRandom.hex(16)}"
  end

  def set_started_at
    self.started_at ||= Time.current
  end

  def track_status_change
    return unless saved_change_to_status?

    old_status, new_status = saved_change_to_status

    # Track metrics
    case new_status
    when "completed"
      form.increment!(:total_sessions)
      UpdateFormMetricsJob.perform_async(form.id)
    when "abandoned"
      form.increment!(:total_sessions)
      AnalyzeAbandonmentJob.perform_async(id)
    end
  end

  def analyze_abandonment_reason
    last_field = last_field_interacted
    return unless last_field

    # Check if abandonment was due to field error
    last_error = field_events.where(form_field: last_field, event_type: "error").last

    if last_error && (Time.current - last_error.timestamp) < 30.seconds
      form.insights.create!(
        type: "abandonment_after_error",
        severity: "warning",
        title: "High abandonment after error on #{last_field.field_label}",
        description: "Users frequently abandon after encountering an error on this field",
        recommendation: "Improve validation messages or field hints",
        metadata: { field_id: last_field.id }
      )
    end

    # Check if abandonment was due to slow field
    last_duration = field_events.where(form_field: last_field, event_type: "blur").last&.duration

    if last_duration && last_duration > 60000 # 60 seconds
      form.insights.create!(
        type: "abandonment_slow_field",
        severity: "info",
        title: "Abandonment after spending #{last_duration / 1000}s on #{last_field.field_label}",
        description: "User spent significant time on this field before abandoning",
        recommendation: "Consider simplifying this field or adding help text",
        metadata: { field_id: last_field.id }
      )
    end
  end
end
