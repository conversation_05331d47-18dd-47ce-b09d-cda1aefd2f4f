class Subscription < ApplicationRecord
  # Associations
  belongs_to :account

  # Validations
  validates :status, presence: true
  validates :plan_type, presence: true

  # Callbacks
  after_initialize :set_defaults, if: :new_record?

  # Instance methods
  def active?
    status == "active"
  end

  def trial?
    status == "trialing"
  end

  def canceled?
    status == "canceled"
  end

  def past_due?
    status == "past_due"
  end

  def usage_percentage
    return 0 if monthly_session_limit.nil? || monthly_session_limit.zero?
    (monthly_sessions_used.to_f / monthly_session_limit * 100).round(2)
  end

  def sessions_remaining
    return 0 if monthly_session_limit.nil?
    [monthly_session_limit - monthly_sessions_used, 0].max
  end

  def at_limit?
    return false if monthly_session_limit.nil?
    monthly_sessions_used >= monthly_session_limit
  end

  def plan_display_name
    plan_type&.humanize&.capitalize || "Free"
  end

  def price_display
    case plan_type
    when "free" then "$0"
    when "starter" then "$29"
    when "growth" then "$99"
    when "enterprise" then "Custom"
    else "$0"
    end
  end

  # Plan limits based on plan_type
  def sessions_limit
    monthly_session_limit || default_session_limit
  end

  def default_session_limit
    case plan_type
    when "free" then 1_000
    when "starter" then 10_000
    when "growth" then 100_000
    when "enterprise" then 1_000_000
    else 1_000
    end
  end

  def team_members_limit
    case plan_type
    when "free" then 1
    when "starter" then 3
    when "growth" then 10
    when "enterprise" then 100
    else 1
    end
  end

  def websites_limit
    case plan_type
    when "free" then 1
    when "starter" then 5
    when "growth" then 25
    when "enterprise" then 100
    else 1
    end
  end

  def features
    case plan_type
    when "free"
      ["Basic analytics", "1 website", "1,000 sessions/month", "7-day data retention"]
    when "starter"
      ["Advanced analytics", "5 websites", "10,000 sessions/month", "30-day data retention", "Email support"]
    when "growth"
      ["All Starter features", "25 websites", "100,000 sessions/month", "90-day data retention", "Priority support", "API access"]
    when "enterprise"
      ["All Growth features", "100 websites", "1M sessions/month", "Unlimited data retention", "Dedicated support", "Custom integrations"]
    else
      ["Basic analytics", "1 website", "1,000 sessions/month"]
    end
  end

  # Alias methods for compatibility
  def plan_name
    plan_type
  end

  def sessions_used
    monthly_sessions_used || 0
  end

  def price_cents
    case plan_type
    when "free" then 0
    when "starter" then 2900
    when "growth" then 9900
    when "enterprise" then nil # Custom pricing
    else 0
    end
  end

  private

  def set_defaults
    self.status ||= "trialing"
    self.plan_type ||= "free"
    self.monthly_sessions_used ||= 0
    self.monthly_session_limit ||= default_session_limit
    self.current_period_start ||= Time.current
    self.current_period_end ||= 30.days.from_now
  end
end