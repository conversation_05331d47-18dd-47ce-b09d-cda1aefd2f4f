class FormSubmission < ApplicationRecord
  # Associations
  belongs_to :form_session
  belongs_to :form
  has_one :website, through: :form
  has_one :account, through: :website

  # Validations
  validates :submitted_at, presence: true
  validates :form_data, presence: true

  # Scopes
  scope :recent, -> { order(submitted_at: :desc) }
  scope :today, -> { where("submitted_at >= ?", Time.current.beginning_of_day) }
  scope :this_week, -> { where("submitted_at >= ?", Time.current.beginning_of_week) }
  scope :this_month, -> { where("submitted_at >= ?", Time.current.beginning_of_month) }
  scope :by_visitor, ->(visitor_id) { where(visitor_id: visitor_id) }

  # Callbacks
  before_validation :set_submitted_at, on: :create
  before_validation :copy_session_data, on: :create
  after_create :update_form_stats
  after_create :check_for_patterns

  # Instance methods
  def field_values
    form_data.transform_keys do |key|
      field = form.form_fields.find_by(name: key) || form.form_fields.find_by(field_identifier: key)
      field&.label || key
    end
  end

  def completion_time_minutes
    return nil unless completion_time
    (completion_time / 60.0).round(2)
  end

  def device_info
    {
      user_agent: user_agent,
      ip_address: ip_address,
      device_type: form_session&.device_type,
      browser: form_session&.browser,
      operating_system: form_session&.operating_system
    }
  end

  def location_info
    {
      country: form_session&.country,
      city: form_session&.city,
      referrer: form_session&.referrer
    }
  end

  def duplicate_submission?
    # Check if there's another submission from same visitor within 5 minutes
    FormSubmission.where(
      form: form,
      visitor_id: visitor_id
    ).where(
      "submitted_at > ? AND id != ?",
      submitted_at - 5.minutes,
      id
    ).exists?
  end

  def spam_score
    score = 0

    # Quick submission (less than 3 seconds)
    score += 30 if completion_time && completion_time < 3

    # No field events
    score += 20 if form_session && form_session.field_events.count == 0

    # Hidden field filled (honeypot)
    score += 50 if form_data["honeypot"].present?

    # Suspicious patterns in data
    form_data.values.each do |value|
      next unless value.is_a?(String)
      score += 10 if value =~ /viagra|casino|lottery/i
      score += 5 if value =~ /http:\/\//
    end

    [ score, 100 ].min
  end

  def likely_spam?
    spam_score > 50
  end

  private

  def set_submitted_at
    self.submitted_at ||= Time.current
  end

  def copy_session_data
    return unless form_session

    self.visitor_id ||= form_session.visitor_id
    self.ip_address ||= form_session.ip_address
    self.user_agent ||= form_session.user_agent
    self.completion_time ||= form_session.time_spent
  end

  def update_form_stats
    form.track_submission if form.respond_to?(:track_submission)
    FormMetricsUpdateJob.perform_later(form.id)
  end

  def check_for_patterns
    # Check for duplicate submissions
    if duplicate_submission?
      form.insights.create!(
        type: "duplicate_submission",
        severity: "info",
        title: "Duplicate submission detected",
        description: "Multiple submissions from visitor #{visitor_id} within 5 minutes",
        metadata: { visitor_id: visitor_id }
      )
    end

    # Check for spam
    if likely_spam?
      form.insights.create!(
        type: "spam_submission",
        severity: "warning",
        title: "Potential spam submission",
        description: "Submission has high spam score (#{spam_score})",
        recommendation: "Consider implementing CAPTCHA or additional validation",
        metadata: { spam_score: spam_score, submission_id: id }
      )
    end
  end
end
