class Account < ApplicationRecord
  # Associations
  has_many :users, dependent: :destroy
  has_many :websites, dependent: :destroy
  has_many :forms, through: :websites
  has_many :form_submissions, through: :forms
  has_many :api_keys, dependent: :destroy
  has_one :subscription, dependent: :destroy

  # Validations
  validates :name, presence: true, length: { maximum: 100 }
  validates :slug, presence: true, uniqueness: true, format: { with: /\A[a-z0-9-]+\z/ }
  validates :billing_email, format: { with: URI::MailTo::EMAIL_REGEXP }, allow_blank: true
  validates :plan, inclusion: { in: %w[free starter growth enterprise] }

  # Enums
  enum :status, { active: 0, suspended: 1, cancelled: 2, trial: 3 }

  # Callbacks
  before_validation :generate_slug, on: :create
  before_create :set_trial_period

  # Scopes
  scope :active_accounts, -> { where(status: :active) }
  scope :trial_accounts, -> { where(status: :trial) }
  scope :expired_trials, -> { trial_accounts.where("trial_ends_at < ?", Time.current) }

  # Instance methods
  def trial?
    status == "trial" && trial_ends_at.present? && trial_ends_at > Time.current
  end

  def trial_expired?
    status == "trial" && trial_ends_at.present? && trial_ends_at <= Time.current
  end

  def can_track_events?
    active? || trial?
  end

  def events_limit
    case plan
    when "free" then 10_000
    when "starter" then 100_000
    when "growth" then 1_000_000
    when "enterprise" then Float::INFINITY
    else 10_000
    end
  end

  def events_remaining
    events_limit - monthly_tracked_events
  end

  def exceeded_events_limit?
    monthly_tracked_events >= events_limit
  end

  private

  def generate_slug
    self.slug = name.parameterize if slug.blank? && name.present?
  end

  def set_trial_period
    self.trial_ends_at ||= 14.days.from_now
    self.status = :trial if status.blank?
  end
end
