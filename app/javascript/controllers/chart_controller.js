import { Controller } from "@hotwired/stimulus"
import { Chart, registerables } from "chart.js"

Chart.register(...registerables)

export default class extends Controller {
  static targets = ["canvas"]
  static values = {
    type: String,
    data: Object,
    options: Object
  }

  connect() {
    this.initChart()
  }

  disconnect() {
    if (this.chart) {
      this.chart.destroy()
    }
  }

  initChart() {
    const ctx = this.canvasTarget.getContext('2d')
    
    const defaultOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          mode: 'index',
          intersect: false,
        }
      },
      scales: {
        x: {
          display: true,
          grid: {
            display: false
          }
        },
        y: {
          display: true,
          beginAtZero: true,
          grid: {
            borderDash: [5, 5]
          }
        }
      }
    }

    this.chart = new Chart(ctx, {
      type: this.typeValue || 'line',
      data: this.dataValue || {},
      options: { ...defaultOptions, ...this.optionsValue }
    })
  }

  update(event) {
    const { data } = event.detail
    if (this.chart && data) {
      this.chart.data = data
      this.chart.update()
    }
  }

  // Helper method to update chart type
  changeType(event) {
    const newType = event.currentTarget.dataset.chartType
    if (this.chart && newType) {
      this.chart.destroy()
      this.typeValue = newType
      this.initChart()
    }
  }
}