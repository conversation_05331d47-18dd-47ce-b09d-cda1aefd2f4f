import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["dateFilter", "content"]

  connect() {
    console.log("Dashboard controller connected")
    this.initializeCharts()
  }

  initializeCharts() {
    // Initialize any charts if needed
    // This can be expanded with Chart.js or other libraries
  }

  filterByDate(event) {
    const period = event.currentTarget.dataset.period
    
    // Update active state
    this.dateFilterTargets.forEach(btn => {
      btn.classList.remove('bg-gray-800', 'text-white')
      btn.classList.add('text-gray-300')
    })
    
    event.currentTarget.classList.remove('text-gray-300')
    event.currentTarget.classList.add('bg-gray-800', 'text-white')
    
    // Here you would typically fetch filtered data
    // For now, we'll just log it
    console.log(`Filtering by: ${period}`)
    
    // You could make a Turbo request to update the dashboard
    // fetch(`/dashboard?period=${period}`, {
    //   headers: {
    //     'Accept': 'text/vnd.turbo-stream.html'
    //   }
    // })
  }

  refreshData() {
    // Refresh dashboard data
    Turbo.visit(window.location.href, { action: "replace" })
  }
}