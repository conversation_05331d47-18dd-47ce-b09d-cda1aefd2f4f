import { Controller } from "@hotwired/stimulus"
import { subscribeToFormAnalytics, subscribeToWebsiteAnalytics } from "../channels/form_analytics_channel"

export default class extends Controller {
  static values = {
    formId: Number,
    websiteId: Number,
    autoRefresh: { type: Boolean, default: true },
    refreshInterval: { type: Number, default: 30000 } // 30 seconds
  }

  connect() {
    this.setupSubscriptions()
    this.setupEventListeners()
    
    if (this.autoRefreshValue) {
      this.startAutoRefresh()
    }
  }

  disconnect() {
    if (this.subscription) {
      this.subscription.unsubscribe()
    }
    
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
    
    this.removeEventListeners()
  }

  setupSubscriptions() {
    if (this.formIdValue) {
      this.subscription = subscribeToFormAnalytics(this.formIdValue, {
        connected: () => this.handleConnected(),
        disconnected: () => this.handleDisconnected(),
        onSessionStart: (data) => this.handleSessionStart(data),
        onFieldInteraction: (data) => this.handleFieldInteraction(data),
        onFormSubmit: (data) => this.handleFormSubmit(data),
        onFormAbandon: (data) => this.handleFormAbandon(data),
        onAnalyticsUpdate: (data) => this.handleAnalyticsUpdate(data)
      })
    } else if (this.websiteIdValue) {
      this.subscription = subscribeToWebsiteAnalytics(this.websiteIdValue, {
        connected: () => this.handleConnected(),
        disconnected: () => this.handleDisconnected(),
        onUpdate: (data) => this.handleWebsiteUpdate(data)
      })
    }
  }

  setupEventListeners() {
    this.analyticsUpdateHandler = (event) => this.updateCharts(event.detail)
    window.addEventListener('analytics:update', this.analyticsUpdateHandler)
    
    this.websiteUpdateHandler = (event) => this.updateWebsiteMetrics(event.detail)
    window.addEventListener('website:analytics:update', this.websiteUpdateHandler)
  }

  removeEventListeners() {
    if (this.analyticsUpdateHandler) {
      window.removeEventListener('analytics:update', this.analyticsUpdateHandler)
    }
    
    if (this.websiteUpdateHandler) {
      window.removeEventListener('website:analytics:update', this.websiteUpdateHandler)
    }
  }

  handleConnected() {
    this.showConnectionStatus('connected')
    console.log('Real-time analytics connected')
  }

  handleDisconnected() {
    this.showConnectionStatus('disconnected')
    console.log('Real-time analytics disconnected')
  }

  handleSessionStart(data) {
    this.incrementCounter('[data-active-sessions]')
    this.showNotification('New session started', 'info')
  }

  handleFieldInteraction(data) {
    // Update field-specific metrics
    const fieldMetric = this.element.querySelector(`[data-field="${data.field_name}"]`)
    if (fieldMetric) {
      const count = parseInt(fieldMetric.dataset.interactionCount || 0) + 1
      fieldMetric.dataset.interactionCount = count
      fieldMetric.textContent = count
    }
  }

  handleFormSubmit(data) {
    this.incrementCounter('[data-submissions-today]')
    this.updateConversionRate(data.conversion_rate)
    this.showNotification('New form submission!', 'success')
    
    // Update recent submissions list
    this.addRecentSubmission(data)
  }

  handleFormAbandon(data) {
    this.incrementCounter('[data-abandonments-today]')
    this.updateAbandonmentRate(data.abandonment_rate)
    
    // Highlight problematic field if specified
    if (data.last_field) {
      this.highlightProblematicField(data.last_field)
    }
  }

  handleAnalyticsUpdate(data) {
    // Update all analytics displays
    Object.keys(data).forEach(key => {
      const element = this.element.querySelector(`[data-metric="${key}"]`)
      if (element) {
        element.textContent = this.formatMetricValue(key, data[key])
      }
    })
  }

  handleWebsiteUpdate(data) {
    // Update website-level metrics
    if (data.forms) {
      this.updateFormsList(data.forms)
    }
    
    if (data.top_performing_form) {
      this.highlightTopForm(data.top_performing_form)
    }
  }

  startAutoRefresh() {
    this.refreshTimer = setInterval(() => {
      this.refreshAnalytics()
    }, this.refreshIntervalValue)
  }

  refreshAnalytics() {
    // Fetch latest analytics data
    fetch(`/api/v1/analytics/${this.formIdValue || this.websiteIdValue}`)
      .then(response => response.json())
      .then(data => {
        this.handleAnalyticsUpdate(data)
      })
      .catch(error => console.error('Failed to refresh analytics:', error))
  }

  updateCharts(data) {
    // Dispatch event to chart controllers
    this.element.querySelectorAll('[data-controller="chart"]').forEach(chartElement => {
      chartElement.dispatchEvent(new CustomEvent('chart:update', {
        detail: { data: this.formatChartData(data) }
      }))
    })
  }

  updateWebsiteMetrics(data) {
    // Update summary cards
    const metrics = ['total_sessions', 'total_submissions', 'avg_conversion_rate', 'avg_completion_time']
    
    metrics.forEach(metric => {
      if (data[metric] !== undefined) {
        const element = this.element.querySelector(`[data-${metric.replace(/_/g, '-')}]`)
        if (element) {
          element.textContent = this.formatMetricValue(metric, data[metric])
          this.animateValue(element)
        }
      }
    })
  }

  incrementCounter(selector) {
    const element = this.element.querySelector(selector)
    if (element) {
      const current = parseInt(element.textContent) || 0
      element.textContent = current + 1
      this.animateValue(element)
    }
  }

  updateConversionRate(rate) {
    const element = this.element.querySelector('[data-conversion-rate]')
    if (element && rate !== undefined) {
      element.textContent = `${rate.toFixed(1)}%`
      
      // Color code based on performance
      element.classList.remove('text-red-600', 'text-yellow-600', 'text-green-600')
      if (rate < 20) {
        element.classList.add('text-red-600')
      } else if (rate < 40) {
        element.classList.add('text-yellow-600')
      } else {
        element.classList.add('text-green-600')
      }
    }
  }

  updateAbandonmentRate(rate) {
    const element = this.element.querySelector('[data-abandonment-rate]')
    if (element && rate !== undefined) {
      element.textContent = `${rate.toFixed(1)}%`
      
      // Color code inversely (high abandonment is bad)
      element.classList.remove('text-red-600', 'text-yellow-600', 'text-green-600')
      if (rate > 70) {
        element.classList.add('text-red-600')
      } else if (rate > 50) {
        element.classList.add('text-yellow-600')
      } else {
        element.classList.add('text-green-600')
      }
    }
  }

  addRecentSubmission(data) {
    const list = this.element.querySelector('[data-recent-submissions]')
    if (!list) return
    
    const item = document.createElement('div')
    item.className = 'p-3 bg-green-50 border border-green-200 rounded-lg mb-2 animate-slide-in'
    item.innerHTML = `
      <div class="flex justify-between items-center">
        <span class="text-sm font-medium">${data.visitor_id || 'Anonymous'}</span>
        <span class="text-xs text-gray-500">${new Date().toLocaleTimeString()}</span>
      </div>
      <div class="text-xs text-gray-600 mt-1">
        Completed in ${data.completion_time || 'N/A'} seconds
      </div>
    `
    
    list.insertBefore(item, list.firstChild)
    
    // Keep only last 10 submissions
    while (list.children.length > 10) {
      list.removeChild(list.lastChild)
    }
  }

  highlightProblematicField(fieldName) {
    const element = this.element.querySelector(`[data-field-name="${fieldName}"]`)
    if (element) {
      element.classList.add('bg-red-100', 'border-red-500', 'animate-pulse')
      setTimeout(() => {
        element.classList.remove('animate-pulse')
      }, 3000)
    }
  }

  updateFormsList(forms) {
    const list = this.element.querySelector('[data-forms-list]')
    if (!list) return
    
    forms.forEach(form => {
      const item = list.querySelector(`[data-form-id="${form.id}"]`)
      if (item) {
        // Update existing form metrics
        item.querySelector('[data-form-sessions]').textContent = form.sessions
        item.querySelector('[data-form-conversion]').textContent = `${form.conversion_rate}%`
      }
    })
  }

  highlightTopForm(formId) {
    const element = this.element.querySelector(`[data-form-id="${formId}"]`)
    if (element) {
      element.classList.add('ring-2', 'ring-green-500', 'bg-green-50')
    }
  }

  showConnectionStatus(status) {
    const indicator = this.element.querySelector('[data-connection-status]')
    if (indicator) {
      indicator.classList.remove('bg-green-500', 'bg-red-500', 'bg-yellow-500')
      
      switch(status) {
        case 'connected':
          indicator.classList.add('bg-green-500')
          indicator.title = 'Real-time updates active'
          break
        case 'disconnected':
          indicator.classList.add('bg-red-500')
          indicator.title = 'Real-time updates disconnected'
          break
        case 'reconnecting':
          indicator.classList.add('bg-yellow-500')
          indicator.title = 'Reconnecting...'
          break
      }
    }
  }

  showNotification(message, type = 'info') {
    // Create and show a temporary notification
    const notification = document.createElement('div')
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 animate-slide-in ${
      type === 'success' ? 'bg-green-500 text-white' :
      type === 'error' ? 'bg-red-500 text-white' :
      'bg-blue-500 text-white'
    }`
    notification.textContent = message
    
    document.body.appendChild(notification)
    
    setTimeout(() => {
      notification.classList.add('animate-slide-out')
      setTimeout(() => {
        document.body.removeChild(notification)
      }, 300)
    }, 3000)
  }

  animateValue(element) {
    element.classList.add('animate-pulse', 'text-blue-600')
    setTimeout(() => {
      element.classList.remove('animate-pulse', 'text-blue-600')
    }, 1000)
  }

  formatMetricValue(key, value) {
    // Format different metric types appropriately
    if (key.includes('rate') || key.includes('percentage')) {
      return `${value.toFixed(1)}%`
    } else if (key.includes('time')) {
      return `${value}s`
    } else if (key.includes('currency') || key.includes('revenue')) {
      return `$${value.toFixed(2)}`
    } else {
      return value.toString()
    }
  }

  formatChartData(data) {
    // Format data for Chart.js
    return {
      labels: data.labels || [],
      datasets: data.datasets || []
    }
  }
}