import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="modal"
export default class extends Controller {
  connect() {
    console.log("Modal controller connected")
  }

  open(event) {
    event.preventDefault()
    const modalId = event.currentTarget.dataset.modalTargetValue
    console.log("Opening modal:", modalId)
    const modal = document.getElementById(modalId)
    
    if (modal) {
      // Remove hidden class and make sure all elements are visible
      modal.classList.remove('hidden')
      modal.style.display = 'block'
      document.body.classList.add('overflow-hidden')
      
      // Make sure the modal content is visible
      const modalContent = modal.querySelector('.inline-block')
      if (modalContent) {
        modalContent.style.display = 'inline-block'
      }
      
      // Focus the first input in the modal
      setTimeout(() => {
        const firstInput = modal.querySelector('input:not([type="hidden"]), textarea, select')
        if (firstInput) {
          firstInput.focus()
        }
      }, 100)
    } else {
      console.error("Modal not found:", modalId)
    }
  }

  close(event) {
    if (event) {
      event.preventDefault()
    }
    
    // Find the modal by looking for parent with id containing 'modal'
    let modal = event ? event.target.closest('[id*="modal"]') : this.element.closest('[id*="modal"]')
    
    if (modal) {
      modal.classList.add('hidden')
      modal.style.display = 'none'
      document.body.classList.remove('overflow-hidden')
    }
  }

  // Handle escape key
  disconnect() {
    document.body.classList.remove('overflow-hidden')
  }
}