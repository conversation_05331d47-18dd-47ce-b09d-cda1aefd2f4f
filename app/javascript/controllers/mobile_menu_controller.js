import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="mobile-menu"
export default class extends Controller {
  static targets = ["menu", "open", "close"]

  connect() {
    this.menuTarget.classList.add("hidden")
  }

  toggle() {
    this.menuTarget.classList.toggle("hidden")
    this.openTarget.classList.toggle("hidden")
    this.closeTarget.classList.toggle("hidden")
  }

  open() {
    this.menuTarget.classList.remove("hidden")
    this.openTarget.classList.add("hidden")
    this.closeTarget.classList.remove("hidden")
  }

  close() {
    this.menuTarget.classList.add("hidden")
    this.openTarget.classList.remove("hidden")
    this.closeTarget.classList.add("hidden")
  }
}