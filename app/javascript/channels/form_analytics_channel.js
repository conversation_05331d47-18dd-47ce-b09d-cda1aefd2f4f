import consumer from "./consumer"

const formAnalyticsSubscriptions = {}

export function subscribeToFormAnalytics(formId, callbacks = {}) {
  if (formAnalyticsSubscriptions[formId]) {
    return formAnalyticsSubscriptions[formId]
  }

  const subscription = consumer.subscriptions.create(
    {
      channel: "FormAnalyticsChannel",
      form_id: formId
    },
    {
      connected() {
        console.log(`Connected to form analytics for form ${formId}`)
        if (callbacks.connected) callbacks.connected()
      },

      disconnected() {
        console.log(`Disconnected from form analytics for form ${formId}`)
        if (callbacks.disconnected) callbacks.disconnected()
      },

      received(data) {
        console.log("Received real-time data:", data)
        
        // Handle different types of updates
        switch(data.type) {
          case 'session_started':
            if (callbacks.onSessionStart) callbacks.onSessionStart(data)
            updateSessionCounter(data)
            break
          
          case 'field_interaction':
            if (callbacks.onFieldInteraction) callbacks.onFieldInteraction(data)
            updateFieldHeatmap(data)
            break
          
          case 'form_submitted':
            if (callbacks.onFormSubmit) callbacks.onFormSubmit(data)
            updateSubmissionCounter(data)
            updateConversionRate(data)
            break
          
          case 'form_abandoned':
            if (callbacks.onFormAbandon) callbacks.onFormAbandon(data)
            updateAbandonmentRate(data)
            break
          
          case 'analytics_update':
            if (callbacks.onAnalyticsUpdate) callbacks.onAnalyticsUpdate(data)
            updateAnalyticsDashboard(data)
            break
        }
      }
    }
  )

  formAnalyticsSubscriptions[formId] = subscription
  return subscription
}

export function subscribeToWebsiteAnalytics(websiteId, callbacks = {}) {
  return consumer.subscriptions.create(
    {
      channel: "FormAnalyticsChannel",
      website_id: websiteId
    },
    {
      connected() {
        console.log(`Connected to website analytics for website ${websiteId}`)
        if (callbacks.connected) callbacks.connected()
      },

      disconnected() {
        console.log(`Disconnected from website analytics`)
        if (callbacks.disconnected) callbacks.disconnected()
      },

      received(data) {
        console.log("Received website analytics:", data)
        if (callbacks.onUpdate) callbacks.onUpdate(data)
        updateWebsiteDashboard(data)
      }
    }
  )
}

// Helper functions to update UI elements
function updateSessionCounter(data) {
  const counter = document.querySelector('[data-sessions-count]')
  if (counter) {
    const currentCount = parseInt(counter.textContent) || 0
    counter.textContent = currentCount + 1
    
    // Add animation class
    counter.classList.add('animate-pulse')
    setTimeout(() => counter.classList.remove('animate-pulse'), 1000)
  }
}

function updateSubmissionCounter(data) {
  const counter = document.querySelector('[data-submissions-count]')
  if (counter) {
    const currentCount = parseInt(counter.textContent) || 0
    counter.textContent = currentCount + 1
    
    // Add animation class
    counter.classList.add('animate-pulse', 'text-green-600')
    setTimeout(() => {
      counter.classList.remove('animate-pulse', 'text-green-600')
    }, 2000)
  }
}

function updateConversionRate(data) {
  const rateElement = document.querySelector('[data-conversion-rate]')
  if (rateElement && data.conversion_rate) {
    rateElement.textContent = `${data.conversion_rate.toFixed(1)}%`
  }
}

function updateAbandonmentRate(data) {
  const rateElement = document.querySelector('[data-abandonment-rate]')
  if (rateElement && data.abandonment_rate) {
    rateElement.textContent = `${data.abandonment_rate.toFixed(1)}%`
    
    // Highlight if rate is high
    if (data.abandonment_rate > 70) {
      rateElement.classList.add('text-red-600', 'font-bold')
    }
  }
}

function updateFieldHeatmap(data) {
  // Update field interaction heatmap
  const fieldElement = document.querySelector(`[data-field-name="${data.field_name}"]`)
  if (fieldElement) {
    const interactionCount = parseInt(fieldElement.dataset.interactions || 0) + 1
    fieldElement.dataset.interactions = interactionCount
    
    // Update heatmap color based on interaction count
    const intensity = Math.min(interactionCount * 10, 100)
    fieldElement.style.backgroundColor = `rgba(239, 68, 68, ${intensity / 100})`
  }
}

function updateAnalyticsDashboard(data) {
  // Dispatch custom event for chart updates
  window.dispatchEvent(new CustomEvent('analytics:update', {
    detail: data
  }))
}

function updateWebsiteDashboard(data) {
  // Update various dashboard metrics
  if (data.total_sessions !== undefined) {
    const element = document.querySelector('[data-total-sessions]')
    if (element) element.textContent = data.total_sessions
  }
  
  if (data.total_submissions !== undefined) {
    const element = document.querySelector('[data-total-submissions]')
    if (element) element.textContent = data.total_submissions
  }
  
  if (data.avg_conversion_rate !== undefined) {
    const element = document.querySelector('[data-avg-conversion-rate]')
    if (element) element.textContent = `${data.avg_conversion_rate.toFixed(1)}%`
  }
  
  // Dispatch event for other components
  window.dispatchEvent(new CustomEvent('website:analytics:update', {
    detail: data
  }))
}