class FormMetricsUpdateService < ApplicationService
  def initialize(form_id)
    @form = Form.find(form_id)
  end

  def call
    ActiveRecord::Base.transaction do
      update_conversion_metrics
      update_field_metrics
      update_timing_metrics
      generate_insights_if_needed
    end

    success(form: form)
  rescue => e
    failure(e.message)
  end

  private

  attr_reader :form

  def update_conversion_metrics
    total_sessions = form.form_sessions.count
    total_submissions = form.form_submissions.count

    form.update!(
      total_sessions: total_sessions,
      total_submissions: total_submissions,
      conversion_rate: calculate_conversion_rate(total_sessions, total_submissions),
      abandonment_rate: calculate_abandonment_rate(total_sessions, total_submissions)
    )
  end

  def update_field_metrics
    form.form_fields.each do |field|
      interaction_count = field.field_events.count
      error_count = field.field_events.errors.count

      # Calculate abandonment rate for this field
      last_field_sessions = form.form_sessions
        .abandoned_sessions
        .joins(:field_events)
        .where('field_events.id = (
          SELECT MAX(fe2.id)
          FROM field_events fe2
          WHERE fe2.form_session_id = form_sessions.id
        )')
        .where("field_events.form_field_id = ?", field.id)
        .count

      abandonment_rate = interaction_count > 0 ?
        (last_field_sessions.to_f / interaction_count * 100).round(2) : 0

      # Calculate average interaction time
      avg_time = field.field_events
        .where(event_type: "blur")
        .where.not(duration: nil)
        .average(:duration)

      avg_time_seconds = avg_time ? (avg_time / 1000.0).round(2) : nil

      field.update!(
        interaction_count: interaction_count,
        error_count: error_count,
        error_rate: calculate_error_rate(interaction_count, error_count),
        abandonment_rate: abandonment_rate,
        avg_interaction_time: avg_time_seconds
      )
    end
  end

  def update_timing_metrics
    recent_submissions = form.form_submissions
      .where("created_at > ?", 30.days.ago)
      .where.not(completion_time: nil)

    if recent_submissions.any?
      avg_time = recent_submissions.average(:completion_time)
      form.update!(avg_completion_time: avg_time.round(2))
    end
  end

  def generate_insights_if_needed
    # Generate insights if we have enough data
    if form.total_sessions >= 100
      last_insight = form.insights.order(:created_at).last

      # Generate new insights if none exist or last one is older than 1 day
      if last_insight.nil? || last_insight.created_at < 1.day.ago
        InsightGeneratorService.call(form)
      end
    end
  end

  def calculate_conversion_rate(sessions, submissions)
    return 0.0 if sessions.zero?
    (submissions.to_f / sessions * 100).round(2)
  end

  def calculate_abandonment_rate(sessions, submissions)
    return 0.0 if sessions.zero?
    abandoned = sessions - submissions
    (abandoned.to_f / sessions * 100).round(2)
  end

  def calculate_error_rate(interactions, errors)
    return 0.0 if interactions.zero?
    (errors.to_f / interactions * 100).round(2)
  end
end
