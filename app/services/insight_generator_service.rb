class InsightGeneratorService < ApplicationService
  def initialize(form)
    @form = form
  end

  def call
    insights = []

    # Analyze conversion rate
    insights.concat(analyze_conversion_rate)

    # Analyze field performance
    insights.concat(analyze_field_performance)

    # Analyze abandonment patterns
    insights.concat(analyze_abandonment_patterns)

    # Analyze time patterns
    insights.concat(analyze_time_patterns)

    success(insights: insights)
  rescue => e
    failure(e.message)
  end

  private

  attr_reader :form

  def analyze_conversion_rate
    insights = []

    if form.conversion_rate < 20
      insights << form.insights.create!(
        insight_type: "low_conversion",
        severity: "critical",
        title: "Very low conversion rate: #{form.conversion_rate}%",
        description: "Your form has a significantly lower conversion rate than the industry average of 40-60%",
        recommendation: "Consider simplifying the form, reducing required fields, or improving the user experience",
        metadata: { conversion_rate: form.conversion_rate }
      )
    elsif form.conversion_rate < 40
      insights << form.insights.create!(
        insight_type: "below_average_conversion",
        severity: "warning",
        title: "Below average conversion rate: #{form.conversion_rate}%",
        description: "Your form conversion rate is below the industry average",
        recommendation: "Review field validation errors and consider A/B testing form variations",
        metadata: { conversion_rate: form.conversion_rate }
      )
    end

    insights
  end

  def analyze_field_performance
    insights = []

    form.form_fields.each do |field|
      # High error rate fields
      if field.error_rate > 20
        insights << form.insights.create!(
          insight_type: "high_error_field",
          severity: "critical",
          title: "High error rate on '#{field.label || field.name}' field",
          description: "#{field.error_rate}% of users encounter errors on this field",
          recommendation: "Improve validation messages, add help text, or reconsider field requirements",
          metadata: { field_id: field.id, error_rate: field.error_rate }
        )
      end

      # High abandonment fields
      if field.abandonment_rate > 30
        insights << form.insights.create!(
          insight_type: "high_abandonment_field",
          severity: "warning",
          title: "High abandonment on '#{field.label || field.name}' field",
          description: "#{field.abandonment_rate}% of users abandon the form at this field",
          recommendation: "Consider making this field optional or providing better guidance",
          metadata: { field_id: field.id, abandonment_rate: field.abandonment_rate }
        )
      end
    end

    insights
  end

  def analyze_abandonment_patterns
    insights = []

    if form.abandonment_rate > 70
      insights << form.insights.create!(
        insight_type: "high_abandonment",
        severity: "critical",
        title: "Critical abandonment rate: #{form.abandonment_rate}%",
        description: "More than 70% of users abandon this form",
        recommendation: "This form needs immediate attention. Consider a complete redesign or breaking it into steps",
        metadata: { abandonment_rate: form.abandonment_rate }
      )
    end

    # Check for specific abandonment points
    last_field_abandonments = form.form_sessions
      .abandoned_sessions
      .joins(:field_events)
      .group("field_events.form_field_id")
      .count

    if last_field_abandonments.any?
      most_abandoned_field_id = last_field_abandonments.max_by { |_, count| count }.first
      field = FormField.find(most_abandoned_field_id)

      insights << form.insights.create!(
        insight_type: "abandonment_hotspot",
        severity: "warning",
        title: "Most abandonments occur at '#{field.label || field.name}'",
        description: "This field is the last interaction point for most abandoned sessions",
        recommendation: "Focus optimization efforts on this field and the ones preceding it",
        metadata: { field_id: field.id }
      )
    end

    insights
  end

  def analyze_time_patterns
    insights = []

    if form.avg_completion_time && form.avg_completion_time > 300
      insights << form.insights.create!(
        insight_type: "slow_completion",
        severity: "info",
        title: "Long completion time: #{(form.avg_completion_time / 60).round(1)} minutes",
        description: "Users take longer than 5 minutes to complete this form on average",
        recommendation: "Consider breaking the form into multiple steps or reducing complexity",
        metadata: { avg_time: form.avg_completion_time }
      )
    end

    # Check for fields with high interaction time
    slow_fields = form.form_fields.where("avg_interaction_time > ?", 30)
    slow_fields.each do |field|
      insights << form.insights.create!(
        insight_type: "slow_field",
        severity: "info",
        title: "Users spend #{field.avg_interaction_time.round}s on '#{field.label || field.name}'",
        description: "This field takes significantly longer than average to complete",
        recommendation: "Simplify this field or provide autocomplete/suggestions",
        metadata: { field_id: field.id, avg_time: field.avg_interaction_time }
      )
    end

    insights
  end
end
