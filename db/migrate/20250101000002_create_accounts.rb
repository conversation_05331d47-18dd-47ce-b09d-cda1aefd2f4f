# frozen_string_literal: true

class CreateAccounts < ActiveRecord::Migration[8.0]
  def change
    create_table :accounts, id: :uuid do |t|
      t.string :name, null: false
      t.string :slug, null: false
      t.string :billing_email
      t.string :timezone, default: 'UTC', null: false
      t.datetime :trial_ends_at
      t.string :plan, default: 'free'
      t.integer :status, default: 0
      t.jsonb :settings, default: {}
      t.integer :monthly_tracked_events, default: 0
      t.timestamps
    end

    add_index :accounts, :slug, unique: true
    add_index :accounts, :name
    add_index :accounts, :status
    add_index :accounts, :trial_ends_at
  end
end
