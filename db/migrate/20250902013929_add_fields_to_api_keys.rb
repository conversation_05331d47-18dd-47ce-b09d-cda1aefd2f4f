class AddFieldsToApiKeys < ActiveRecord::Migration[8.0]
  def change
    add_column :api_keys, :permissions, :jsonb, default: ['read'], null: false
    add_column :api_keys, :request_count, :integer, default: 0
    add_column :api_keys, :recent_request_count, :integer, default: 0
    add_column :api_keys, :rate_limit_hits, :integer, default: 0
    add_column :api_keys, :created_by_id, :bigint
    add_column :api_keys, :revoked_at, :datetime
    add_column :api_keys, :full_key, :string
    
    add_index :api_keys, :created_by_id
    add_index :api_keys, :revoked_at
    add_index :api_keys, :permissions, using: :gin
  end
end
