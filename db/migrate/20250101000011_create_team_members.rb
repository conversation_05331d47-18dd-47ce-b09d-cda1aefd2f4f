# frozen_string_literal: true

class CreateTeamMembers < ActiveRecord::Migration[8.0]
  def change
    create_table :team_members, id: :uuid do |t|
      t.references :account, null: false, foreign_key: true, type: :uuid
      t.string :email, null: false
      t.string :name
      t.string :role, null: false, default: 'member'
      t.string :invitation_token
      t.datetime :invitation_sent_at
      t.datetime :invitation_accepted_at
      t.timestamps
    end

    add_index :team_members, [ :account_id, :email ], unique: true
    add_index :team_members, :invitation_token, unique: true
    add_index :team_members, :role
  end
end
