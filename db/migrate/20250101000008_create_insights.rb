# frozen_string_literal: true

class CreateInsights < ActiveRecord::Migration[8.0]
  def change
    create_table :insights, id: :uuid do |t|
      t.references :form, null: false, foreign_key: true, type: :uuid
      t.string :type, null: false
      t.string :severity, null: false
      t.string :title, null: false
      t.text :description
      t.text :recommendation
      t.integer :impact_score
      t.jsonb :metadata, default: {}
      t.datetime :detected_at
      t.datetime :resolved_at
      t.timestamps
    end

    add_index :insights, [ :form_id, :type ]
    add_index :insights, :severity
    add_index :insights, :resolved_at
    add_index :insights, :metadata, using: :gin
  end
end
