class CreateFormSubmissions < ActiveRecord::Migration[8.0]
  def change
    create_table :form_submissions, id: :uuid do |t|
      t.references :form_session, null: false, foreign_key: true, type: :uuid
      t.references :form, null: false, foreign_key: true, type: :uuid
      t.datetime :submitted_at, null: false
      t.float :completion_time
      t.jsonb :form_data, default: {}
      t.jsonb :metadata, default: {}
      t.string :visitor_id
      t.string :ip_address
      t.string :user_agent

      t.timestamps
    end

    add_index :form_submissions, :submitted_at
    add_index :form_submissions, :visitor_id
  end
end
