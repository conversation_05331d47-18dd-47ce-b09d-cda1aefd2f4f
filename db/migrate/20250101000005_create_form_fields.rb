# frozen_string_literal: true

class CreateFormFields < ActiveRecord::Migration[8.0]
  def change
    create_table :form_fields, id: :uuid do |t|
      t.references :form, null: false, foreign_key: true, type: :uuid
      t.string :name, null: false
      t.string :field_type
      t.string :label
      t.string :field_identifier
      t.integer :position
      t.boolean :required, default: false
      t.integer :interaction_count, default: 0
      t.integer :error_count, default: 0
      t.integer :correction_count, default: 0
      t.float :avg_interaction_time
      t.float :error_rate
      t.float :correction_rate
      t.float :abandonment_rate
      t.jsonb :validation_rules, default: {}
      t.jsonb :metadata, default: {}
      t.timestamps
    end

    add_index :form_fields, [ :form_id, :name ], unique: true
    add_index :form_fields, [ :form_id, :position ]
    add_index :form_fields, :error_rate
    add_index :form_fields, :abandonment_rate
  end
end
