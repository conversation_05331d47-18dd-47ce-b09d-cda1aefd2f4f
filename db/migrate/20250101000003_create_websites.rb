# frozen_string_literal: true

class CreateWebsites < ActiveRecord::Migration[8.0]
  def change
    create_table :websites, id: :uuid do |t|
      t.references :account, null: false, foreign_key: true, type: :uuid
      t.string :domain, null: false
      t.string :name, null: false
      t.string :tracking_id, null: false
      t.string :verification_token
      t.datetime :verified_at
      t.integer :status, default: 0
      t.jsonb :settings, default: {}
      t.timestamps
    end

    add_index :websites, :tracking_id, unique: true
    add_index :websites, [ :account_id, :domain ], unique: true
    add_index :websites, :status
    add_index :websites, :verified_at
  end
end
