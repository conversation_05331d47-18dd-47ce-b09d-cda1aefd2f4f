# frozen_string_literal: true

class CreateSubscriptions < ActiveRecord::Migration[8.0]
  def change
    create_table :subscriptions, id: :uuid do |t|
      t.references :account, null: false, foreign_key: true, type: :uuid, index: { unique: true }
      t.string :plan_type, null: false, default: 'starter'
      t.string :status, null: false, default: 'trialing'
      t.datetime :current_period_start
      t.datetime :current_period_end
      t.string :stripe_subscription_id
      t.string :stripe_customer_id
      t.integer :monthly_session_limit
      t.integer :monthly_sessions_used, default: 0
      t.timestamps
    end
    add_index :subscriptions, :stripe_subscription_id
    add_index :subscriptions, :status
    add_index :subscriptions, :current_period_end
  end
end
