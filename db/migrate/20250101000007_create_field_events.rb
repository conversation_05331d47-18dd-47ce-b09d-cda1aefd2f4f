# frozen_string_literal: true

class CreateFieldEvents < ActiveRecord::Migration[8.0]
  def change
    create_table :field_events, id: :uuid do |t|
      t.references :form_session, null: false, foreign_key: true, type: :uuid
      t.references :form_field, null: false, foreign_key: true, type: :uuid
      t.string :event_type, null: false
      t.datetime :timestamp, null: false
      t.integer :duration
      t.integer :value_length
      t.text :value
      t.text :error_message
      t.jsonb :metadata, default: {}
      t.timestamps
    end

    add_index :field_events, [ :form_session_id, :timestamp ]
    add_index :field_events, :event_type
    add_index :field_events, [ :form_field_id, :event_type ]
  end
end
