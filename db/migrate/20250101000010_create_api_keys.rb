# frozen_string_literal: true

class CreateApiKeys < ActiveRecord::Migration[8.0]
  def change
    create_table :api_keys, id: :uuid do |t|
      t.references :account, null: false, foreign_key: true, type: :uuid
      t.string :name, null: false
      t.string :key, null: false
      t.datetime :last_used_at
      t.datetime :expires_at
      t.boolean :active, default: true
      t.timestamps
    end

    add_index :api_keys, :key, unique: true
    add_index :api_keys, :active
  end
end
