# frozen_string_literal: true

class CreateForms < ActiveRecord::Migration[8.0]
  def change
    create_table :forms, id: :uuid do |t|
      t.references :website, null: false, foreign_key: true, type: :uuid
      t.string :form_identifier, null: false
      t.string :name
      t.text :url, null: false
      t.string :form_type
      t.integer :field_count, default: 0
      t.integer :total_sessions, default: 0
      t.integer :total_submissions, default: 0
      t.float :avg_completion_time
      t.float :conversion_rate
      t.float :abandonment_rate
      t.jsonb :field_metadata, default: {}
      t.jsonb :settings, default: {}
      t.datetime :first_seen_at
      t.datetime :last_seen_at
      t.timestamps
    end

    add_index :forms, [ :website_id, :form_identifier ], unique: true
    add_index :forms, [ :website_id, :url ]
    add_index :forms, :last_seen_at
    add_index :forms, :conversion_rate
    add_index :forms, :abandonment_rate
  end
end
