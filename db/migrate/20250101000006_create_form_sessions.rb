# frozen_string_literal: true

class CreateFormSessions < ActiveRecord::Migration[8.0]
  def change
    create_table :form_sessions, id: :uuid do |t|
      t.references :form, null: false, foreign_key: true, type: :uuid
      t.string :visitor_id, null: false
      t.string :session_id, null: false
      t.datetime :started_at, null: false
      t.datetime :completed_at
      t.datetime :abandoned_at
      t.string :status, null: false, default: 'in_progress'
      t.integer :interaction_count, default: 0
      t.float :time_spent
      t.string :device_type
      t.string :browser
      t.string :operating_system
      t.string :country
      t.string :city
      t.text :referrer
      t.text :user_agent
      t.string :ip_address
      t.jsonb :metadata, default: {}
      t.timestamps
    end

    add_index :form_sessions, :session_id, unique: true
    add_index :form_sessions, [ :form_id, :started_at ]
    add_index :form_sessions, :status
    add_index :form_sessions, :visitor_id
  end
end
