#!/usr/bin/env ruby

# Create subscriptions for existing accounts
Account.find_each do |account|
  unless account.subscription
    puts "Creating subscription for account: #{account.name}"
    account.create_subscription!(
      plan_type: 'starter',
      status: 'active',
      monthly_session_limit: 10000,
      monthly_sessions_used: 0,
      current_period_start: Time.current,
      current_period_end: 30.days.from_now
    )
  end
end

puts "Subscriptions created successfully!"