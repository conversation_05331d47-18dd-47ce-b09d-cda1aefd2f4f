FactoryBot.define do
  factory :form_session do
    association :form
    session_id { SecureRandom.uuid }
    visitor_id { "visitor_#{SecureRandom.hex(8)}" }
    started_at { Time.current }
    status { "in_progress" }
    device_type { "desktop" }
    browser { "Chrome" }
    operating_system { "macOS" }
    referrer { "https://google.com" }
    user_agent { "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)" }
    ip_address { "127.0.0.1" }
    country { "US" }
    metadata { { screen_resolution: "1920x1080", viewport_size: "1920x900" } }

    trait :completed do
      status { "completed" }
      completed_at { Time.current }
    end

    trait :abandoned do
      status { "abandoned" }
      abandoned_at { Time.current }
    end

    trait :mobile do
      device_type { "mobile" }
      browser { "Safari" }
      operating_system { "iOS" }
      user_agent { "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)" }
    end
  end
end
