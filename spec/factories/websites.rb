FactoryBot.define do
  factory :website do
    association :account
    sequence(:name) { |n| "Website #{n}" }
    sequence(:domain) { |n| "example#{n}.com" }
    status { "active" }
    verified_at { Time.current }

    trait :unverified do
      verified_at { nil }
    end

    trait :inactive do
      status { "inactive" }
    end

    trait :suspended do
      status { "suspended" }
    end
  end
end
