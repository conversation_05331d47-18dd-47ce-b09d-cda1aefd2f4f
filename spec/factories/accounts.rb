FactoryBot.define do
  factory :account do
    sequence(:name) { |n| "Account #{n}" }
    sequence(:slug) { |n| "account-#{n}" }
    billing_email { "<EMAIL>" }
    plan { "starter" }
    status { "active" }
    monthly_tracked_events { 0 }
    trial_ends_at { 14.days.from_now }

    trait :trial do
      status { "trial" }
      trial_ends_at { 7.days.from_now }
    end

    trait :expired_trial do
      status { "trial" }
      trial_ends_at { 1.day.ago }
    end

    trait :suspended do
      status { "suspended" }
    end

    trait :free do
      plan { "free" }
    end

    trait :growth do
      plan { "growth" }
    end

    trait :enterprise do
      plan { "enterprise" }
    end
  end
end
