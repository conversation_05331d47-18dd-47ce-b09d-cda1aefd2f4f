require 'rails_helper'

RSpec.describe WebsitesController, type: :controller do
  let(:account) { create(:account) }
  let(:user) { create(:user, account: account) }

  before do
    sign_in user
    allow(controller).to receive(:current_account).and_return(account)
    allow(Current).to receive(:account).and_return(account)
  end

  describe "GET #index" do
    let!(:website1) { create(:website, account: account) }
    let!(:website2) { create(:website, account: account) }
    let!(:other_website) { create(:website) }

    it "returns a success response" do
      get :index
      expect(response).to be_successful
    end

    it "assigns websites belonging to the current account" do
      get :index
      expect(assigns(:websites)).to contain_exactly(website1, website2)
    end

    it "does not include websites from other accounts" do
      get :index
      expect(assigns(:websites)).not_to include(other_website)
    end
  end

  describe "GET #show" do
    let(:website) { create(:website, account: account) }
    let!(:form) { create(:form, website: website) }

    it "returns a success response" do
      get :show, params: { id: website.id }
      expect(response).to be_successful
    end

    it "assigns the requested website" do
      get :show, params: { id: website.id }
      expect(assigns(:website)).to eq(website)
    end

    it "assigns forms and analytics data" do
      get :show, params: { id: website.id }
      expect(assigns(:forms)).to include(form)
      expect(assigns(:total_sessions)).to eq(form.total_sessions)
      expect(assigns(:total_submissions)).to eq(form.total_submissions)
      expect(assigns(:avg_conversion_rate)).to eq(form.conversion_rate)
    end

    context "when website belongs to another account" do
      let(:other_website) { create(:website) }

      it "raises an error" do
        expect {
          get :show, params: { id: other_website.id }
        }.to raise_error(ActiveRecord::RecordNotFound)
      end
    end
  end

  describe "GET #new" do
    it "returns a success response" do
      get :new
      expect(response).to be_successful
    end

    it "assigns a new website" do
      get :new
      expect(assigns(:website)).to be_a_new(Website)
      expect(assigns(:website).account).to eq(account)
    end
  end

  describe "POST #create" do
    context "with valid params" do
      let(:valid_attributes) {
        { name: "Test Website", domain: "test.com" }
      }

      it "creates a new Website" do
        expect {
          post :create, params: { website: valid_attributes }
        }.to change(Website, :count).by(1)
      end

      it "redirects to the created website" do
        post :create, params: { website: valid_attributes }
        expect(response).to redirect_to(Website.last)
      end

      it "sets a success notice" do
        post :create, params: { website: valid_attributes }
        expect(flash[:notice]).to include("successfully created")
      end
    end

    context "with invalid params" do
      let(:invalid_attributes) {
        { name: "", domain: "" }
      }

      it "does not create a new Website" do
        expect {
          post :create, params: { website: invalid_attributes }
        }.not_to change(Website, :count)
      end

      it "renders the new template" do
        post :create, params: { website: invalid_attributes }
        expect(response).to render_template(:new)
      end

      it "returns unprocessable entity status" do
        post :create, params: { website: invalid_attributes }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "PUT #update" do
    let(:website) { create(:website, account: account) }

    context "with valid params" do
      let(:new_attributes) {
        { name: "Updated Website", domain: "updated.com" }
      }

      it "updates the requested website" do
        put :update, params: { id: website.id, website: new_attributes }
        website.reload
        expect(website.name).to eq("Updated Website")
        expect(website.domain).to eq("updated.com")
      end

      it "redirects to the website" do
        put :update, params: { id: website.id, website: new_attributes }
        expect(response).to redirect_to(website)
      end
    end

    context "with invalid params" do
      let(:invalid_attributes) {
        { name: "", domain: "" }
      }

      it "does not update the website" do
        original_name = website.name
        put :update, params: { id: website.id, website: invalid_attributes }
        website.reload
        expect(website.name).to eq(original_name)
      end

      it "renders the edit template" do
        put :update, params: { id: website.id, website: invalid_attributes }
        expect(response).to render_template(:edit)
      end
    end
  end

  describe "DELETE #destroy" do
    let!(:website) { create(:website, account: account) }

    it "destroys the requested website" do
      expect {
        delete :destroy, params: { id: website.id }
      }.to change(Website, :count).by(-1)
    end

    it "redirects to the websites list" do
      delete :destroy, params: { id: website.id }
      expect(response).to redirect_to(websites_url)
    end
  end

  describe "POST #verify" do
    let(:website) { create(:website, :unverified, account: account) }

    it "verifies the website" do
      expect(website).not_to be_verified
      post :verify, params: { id: website.id }
      website.reload
      expect(website).to be_verified
    end

    it "redirects to the website" do
      post :verify, params: { id: website.id }
      expect(response).to redirect_to(website)
    end

    it "sets a success notice" do
      post :verify, params: { id: website.id }
      expect(flash[:notice]).to include("verified")
    end
  end
end
