require 'rails_helper'

RSpec.describe Api::V1::TrackingController, type: :controller do
  let(:account) { create(:account) }
  let(:website) { create(:website, account: account) }

  describe "POST #session_start" do
    context "with valid tracking ID" do
      let(:params) do
        {
          tracking_id: website.tracking_id,
          form_identifier: "contact_form",
          form_name: "Contact Form",
          url: "https://example.com/contact",
          referrer: "https://google.com",
          visitor_id: "visitor_123",
          screen_resolution: "1920x1080",
          viewport_size: "1920x900"
        }
      end

      it "creates a new form if it doesn't exist" do
        expect {
          post :session_start, params: params
        }.to change(Form, :count).by(1)
      end

      it "creates a new form session" do
        expect {
          post :session_start, params: params
        }.to change(FormSession, :count).by(1)
      end

      it "returns session ID and form ID" do
        post :session_start, params: params
        json_response = JSON.parse(response.body)
        expect(json_response).to have_key("session_id")
        expect(json_response).to have_key("form_id")
      end

      it "reuses existing form" do
        form = create(:form, website: website, form_identifier: "contact_form")
        expect {
          post :session_start, params: params
        }.not_to change(Form, :count)

        json_response = JSON.parse(response.body)
        expect(json_response["form_id"]).to eq(form.id)
      end
    end

    context "with invalid tracking ID" do
      it "returns unauthorized" do
        post :session_start, params: { tracking_id: "invalid" }
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "with unverified website" do
      let(:unverified_website) { create(:website, :unverified, account: account) }

      it "returns unauthorized" do
        post :session_start, params: { tracking_id: unverified_website.tracking_id }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe "POST #field_event" do
    let(:form) { create(:form, website: website) }
    let(:form_session) { create(:form_session, form: form) }

    context "with valid params" do
      let(:params) do
        {
          tracking_id: website.tracking_id,
          form_id: form.id,
          session_id: form_session.session_id,
          field_name: "email",
          field_type: "email",
          field_label: "Email Address",
          event_type: "focus",
          timestamp: Time.current.to_i * 1000,
          value: "<EMAIL>"
        }
      end

      it "creates a field event" do
        expect {
          post :field_event, params: params
        }.to change(FieldEvent, :count).by(1)
      end

      it "creates form field if it doesn't exist" do
        expect {
          post :field_event, params: params
        }.to change(FormField, :count).by(1)
      end

      it "returns success response" do
        post :field_event, params: params
        json_response = JSON.parse(response.body)
        expect(json_response["success"]).to be true
        expect(json_response).to have_key("event_id")
      end
    end

    context "with invalid session" do
      let(:params) do
        {
          tracking_id: website.tracking_id,
          form_id: form.id,
          session_id: "invalid_session",
          field_name: "email"
        }
      end

      it "returns unprocessable entity" do
        post :field_event, params: params
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "POST #submit" do
    let(:form) { create(:form, website: website) }
    let(:form_session) { create(:form_session, form: form) }

    context "with valid params" do
      let(:params) do
        {
          tracking_id: website.tracking_id,
          form_id: form.id,
          session_id: form_session.session_id,
          form_data: { name: "John", email: "<EMAIL>" },
          visitor_id: "visitor_123"
        }
      end

      it "creates a form submission" do
        expect {
          post :submit, params: params
        }.to change(FormSubmission, :count).by(1)
      end

      it "marks session as completed" do
        post :submit, params: params
        form_session.reload
        expect(form_session.status).to eq("completed")
      end

      it "returns success response" do
        post :submit, params: params
        json_response = JSON.parse(response.body)
        expect(json_response["success"]).to be true
        expect(json_response).to have_key("submission_id")
      end
    end
  end

  describe "POST #abandon" do
    let(:form) { create(:form, website: website) }
    let(:form_session) { create(:form_session, form: form) }

    context "with valid params" do
      let(:params) do
        {
          tracking_id: website.tracking_id,
          form_id: form.id,
          session_id: form_session.session_id
        }
      end

      it "marks session as abandoned" do
        post :abandon, params: params
        form_session.reload
        expect(form_session.status).to eq("abandoned")
      end

      it "returns success response" do
        post :abandon, params: params
        json_response = JSON.parse(response.body)
        expect(json_response["success"]).to be true
      end
    end
  end

  describe "POST #batch" do
    let(:form) { create(:form, website: website) }
    let(:form_session) { create(:form_session, form: form) }

    context "with valid events" do
      let(:events) do
        [
          {
            type: "field_focus",
            form_id: form.id,
            session_id: form_session.session_id,
            field: "email",
            timestamp: Time.current.to_i * 1000
          },
          {
            type: "field_blur",
            form_id: form.id,
            session_id: form_session.session_id,
            field: "email",
            duration: 5000,
            timestamp: Time.current.to_i * 1000
          },
          {
            type: "page_view",
            title: "Contact Page",
            path: "/contact",
            timestamp: Time.current.to_i * 1000
          }
        ]
      end

      let(:params) do
        {
          tracking_id: website.tracking_id,
          events: events
        }
      end

      it "processes multiple events" do
        post :batch, params: params
        json_response = JSON.parse(response.body)
        expect(json_response["success"]).to be true
        expect(json_response["processed"]).to eq(3)
        expect(json_response["total"]).to eq(3)
      end

      it "creates field events" do
        expect {
          post :batch, params: params
        }.to change(FieldEvent, :count).by(2)
      end

      it "handles errors gracefully" do
        events << { type: "invalid_event" }
        params[:events] = events

        post :batch, params: params
        json_response = JSON.parse(response.body)
        expect(json_response["success"]).to be true
        expect(json_response["processed"]).to eq(3)
        expect(json_response["errors"]).not_to be_empty
      end
    end
  end

  describe "GET #script" do
    it "returns JavaScript tracking code" do
      get :script, params: { id: website.tracking_id }, format: :js
      expect(response).to be_successful
      expect(response.content_type).to include("text/javascript")
      expect(response.body).to include("FormFlowPro")
      expect(response.body).to include(website.tracking_id)
    end

    it "returns error for invalid tracking ID" do
      get :script, params: { id: "invalid" }, format: :js
      expect(response.body).to include("console.error")
    end
  end
end
