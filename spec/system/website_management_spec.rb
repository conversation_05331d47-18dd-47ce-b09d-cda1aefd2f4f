require 'rails_helper'

RSpec.describe "Website Management", type: :system do
  let(:account) { create(:account) }
  let(:user) { create(:user, account: account) }

  before do
    driven_by(:selenium_chrome_headless)
    sign_in user
  end

  describe "Creating a new website" do
    it "allows user to add a new website" do
      visit websites_path
      click_link "Add Website"

      fill_in "Name", with: "My Blog"
      fill_in "Domain", with: "myblog.com"
      click_button "Create Website"

      expect(page).to have_content("Website was successfully created")
      expect(page).to have_content("My Blog")
      expect(page).to have_content("myblog.com")
      expect(page).to have_content("Add the tracking code to your site")
    end

    it "shows validation errors for invalid input" do
      visit new_website_path

      click_button "Create Website"

      expect(page).to have_content("can't be blank")
    end
  end

  describe "Managing existing websites" do
    let!(:website) { create(:website, account: account, name: "Test Site") }

    it "displays list of websites" do
      visit websites_path

      expect(page).to have_content("Test Site")
      expect(page).to have_content(website.domain)
    end

    it "allows editing website details" do
      visit websites_path
      click_link "Edit"

      fill_in "Name", with: "Updated Site"
      click_button "Update Website"

      expect(page).to have_content("Website was successfully updated")
      expect(page).to have_content("Updated Site")
    end

    it "allows deleting a website" do
      visit websites_path

      accept_confirm do
        click_link "Delete"
      end

      expect(page).to have_content("Website was successfully removed")
      expect(page).not_to have_content("Test Site")
    end
  end

  describe "Viewing website analytics" do
    let!(:website) { create(:website, account: account) }
    let!(:form) { create(:form, website: website,
                         total_sessions: 150,
                         total_submissions: 45,
                         conversion_rate: 30.0) }

    it "displays analytics dashboard" do
      visit website_path(website)

      expect(page).to have_content(website.name)
      expect(page).to have_content("Total Sessions")
      expect(page).to have_content("150")
      expect(page).to have_content("Total Submissions")
      expect(page).to have_content("45")
      expect(page).to have_content("Conversion Rate")
      expect(page).to have_content("30")
    end

    it "shows tracking code" do
      visit website_path(website)

      expect(page).to have_content("Tracking Code")
      expect(page).to have_content(website.tracking_id)
    end

    context "with unverified website" do
      let!(:unverified_website) { create(:website, :unverified, account: account) }

      it "shows verification prompt" do
        visit website_path(unverified_website)

        expect(page).to have_content("Website not verified")
        expect(page).to have_button("Verify Website")
      end

      it "allows verifying the website" do
        visit website_path(unverified_website)

        click_button "Verify Website"

        expect(page).to have_content("Website has been verified")
        expect(page).not_to have_content("Website not verified")
      end
    end
  end
end
