require 'rails_helper'

RSpec.describe "Authentication", type: :system do
  before do
    driven_by(:selenium_chrome_headless)
  end

  describe "User registration" do
    let(:account) { create(:account) }

    it "allows new user to sign up" do
      visit new_user_registration_path

      fill_in "Email", with: "<EMAIL>"
      fill_in "Password", with: "password123"
      fill_in "Password confirmation", with: "password123"
      click_button "Sign up"

      expect(page).to have_content("Welcome! You have signed up successfully")
      expect(page).to have_content("Dashboard")
    end

    it "shows validation errors for invalid input" do
      visit new_user_registration_path

      fill_in "Email", with: "invalid-email"
      fill_in "Password", with: "short"
      fill_in "Password confirmation", with: "different"
      click_button "Sign up"

      expect(page).to have_content("Email is invalid")
      expect(page).to have_content("Password is too short")
      expect(page).to have_content("Password confirmation doesn't match")
    end
  end

  describe "User sign in" do
    let(:account) { create(:account) }
    let(:user) { create(:user, account: account, email: "<EMAIL>", password: "password123") }

    before { user } # Ensure user is created

    it "allows existing user to sign in" do
      visit new_user_session_path

      fill_in "Email", with: "<EMAIL>"
      fill_in "Password", with: "password123"
      click_button "Sign in"

      expect(page).to have_content("Signed in successfully")
      expect(page).to have_content("Dashboard")
    end

    it "shows error for invalid credentials" do
      visit new_user_session_path

      fill_in "Email", with: "<EMAIL>"
      fill_in "Password", with: "wrongpassword"
      click_button "Sign in"

      expect(page).to have_content("Invalid Email or password")
    end

    it "remembers user when remember me is checked" do
      visit new_user_session_path

      fill_in "Email", with: "<EMAIL>"
      fill_in "Password", with: "password123"
      check "Remember me"
      click_button "Sign in"

      expect(page).to have_content("Signed in successfully")
    end
  end

  describe "User sign out" do
    let(:account) { create(:account) }
    let(:user) { create(:user, account: account) }

    before do
      sign_in user
    end

    it "allows user to sign out" do
      visit dashboard_path

      click_button "Sign out"

      expect(page).to have_content("Signed out successfully")
      expect(page).to have_content("Sign in")
    end
  end

  describe "Password reset" do
    let(:account) { create(:account) }
    let(:user) { create(:user, account: account, email: "<EMAIL>") }

    before { user } # Ensure user is created

    it "allows user to request password reset" do
      visit new_user_session_path
      click_link "Forgot your password?"

      fill_in "Email", with: "<EMAIL>"
      click_button "Send me reset password instructions"

      expect(page).to have_content("You will receive an email with instructions")
    end

    it "shows error for non-existent email" do
      visit new_user_password_path

      fill_in "Email", with: "<EMAIL>"
      click_button "Send me reset password instructions"

      expect(page).to have_content("Email not found")
    end
  end
end
