require 'rails_helper'

RSpec.describe Account, type: :model do
  describe "associations" do
    it { should have_many(:users).dependent(:destroy) }
    it { should have_many(:websites).dependent(:destroy) }
    it { should have_many(:forms).through(:websites) }
  end

  describe "validations" do
    it { should validate_presence_of(:name) }
    it { should validate_presence_of(:slug) }
    it { should validate_inclusion_of(:plan).in_array(%w[free starter growth enterprise]) }

    it "validates uniqueness of slug" do
      create(:account, slug: "test-account")
      account = build(:account, slug: "test-account")
      expect(account).not_to be_valid
      expect(account.errors[:slug]).to include("has already been taken")
    end
  end

  describe "factory" do
    it "creates valid account" do
      account = build(:account)
      expect(account).to be_valid
    end
  end

  describe "trial methods" do
    context "active trial" do
      let(:account) { create(:account, :trial) }

      it "returns true for trial?" do
        expect(account.trial?).to be true
      end

      it "returns false for trial_expired?" do
        expect(account.trial_expired?).to be false
      end
    end
  end

  describe "tracking limits" do
    let(:account) { create(:account) }

    it "has proper events limit for starter plan" do
      expect(account.events_limit).to eq(100_000)
    end

    it "can track events when active" do
      expect(account.can_track_events?).to be true
    end
  end
end
