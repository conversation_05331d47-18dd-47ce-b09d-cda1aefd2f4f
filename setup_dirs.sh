#!/bin/bash

# Create Rails directory structure
directories=(
  "app/controllers"
  "app/controllers/api"
  "app/controllers/api/v1"
  "app/models"
  "app/models/concerns"
  "app/views"
  "app/views/layouts"
  "app/views/dashboard"
  "app/views/forms"
  "app/jobs"
  "app/mailers"
  "app/services"
  "app/javascript"
  "app/javascript/controllers"
  "app/assets"
  "app/assets/stylesheets"
  "config"
  "config/environments"
  "config/initializers"
  "config/locales"
  "db"
  "db/migrate"
  "lib"
  "lib/tasks"
  "public"
  "public/assets"
  "spec"
  "spec/models"
  "spec/controllers"
  "spec/requests"
  "spec/services"
  "spec/support"
  "spec/factories"
  "tmp"
  "tmp/cache"
  "tmp/pids"
  "log"
  "vendor"
  "storage"
)

for dir in "${directories[@]}"; do
  mkdir -p "$dir"
done

echo "Directory structure created successfully!"
