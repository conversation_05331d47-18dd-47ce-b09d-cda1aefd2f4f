# FormFlow Pro 🚀

![FormFlow Pro Logo](https://img.shields.io/badge/FormFlow-Pro-blue)
![Rails](https://img.shields.io/badge/Rails-8.0.2-red)
![PostgreSQL](https://img.shields.io/badge/PostgreSQL-14+-blue)
![License](https://img.shields.io/badge/License-MIT-green)
![Tests](https://img.shields.io/badge/Tests-Passing-brightgreen)

**Stop losing customers at the finish line.** FormFlow Pro is an intelligent form analytics platform that helps businesses understand and fix form abandonment issues. With our lightweight tracking script and AI-powered insights, you can see exactly where users drop off and get actionable recommendations to improve conversion rates.

## 🎯 Key Features

### Core Analytics
- **📊 Real-time Form Tracking** - Automatic detection and tracking of all forms on your website
- **🔍 Field-Level Analytics** - Detailed insights into how users interact with each field
- **📹 Session Replay** - Watch exactly how users navigate through your forms
- **📈 Conversion Funnels** - Visualize drop-off points field by field
- **⚡ Performance Metrics** - Track completion times, error rates, and correction patterns

### Intelligence & Insights
- **🤖 AI-Powered Recommendations** - Get specific suggestions to improve each form
- **🚨 Smart Alerts** - Proactive notifications when forms have issues
- **📊 Problem Detection** - Automatic identification of high-friction fields
- **💡 A/B Testing Suggestions** - Data-driven optimization recommendations

### Business Tools
- **📧 Weekly Reports** - Automated email summaries of form performance
- **🔗 Integrations** - Connect with your existing tools (Slack, Zapier, webhooks)
- **👥 Team Collaboration** - Share insights with your team
- **🏷️ White-Label Options** - Custom branding for agencies

## 🛠️ Tech Stack

### Backend
- **Framework:** Ruby on Rails 8.0.2
- **Database:** PostgreSQL 14+
- **Cache:** Solid Cache (database-backed)
- **Background Jobs:** Solid Queue (database-backed)
- **Authentication:** Devise + Devise-JWT
- **Payments:** Stripe

### Frontend
- **Framework:** Hotwire (Turbo + Stimulus)
- **CSS:** TailwindCSS
- **Charts:** Chart.js
- **Real-time:** ActionCable (Solid Cable)

### Infrastructure
- **Deployment:** Kamal 2 (zero-dependency deployment)
- **Hosting:** VPS/Cloud (Heroku/Render/AWS compatible)
- **CDN:** CloudFlare
- **Monitoring:** Sentry + NewRelic
- **CI/CD:** GitHub Actions

## 🚀 Quick Start

### Prerequisites
- Ruby 3.2.2+
- PostgreSQL 14+
- Node.js 18+
- Yarn or npm
- Redis 6+ (optional, for advanced features)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/formflowpro.git
   cd formflowpro
   ```

2. **Install dependencies**
   ```bash
   bundle install
   yarn install
   ```

3. **Setup environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Setup database**
   ```bash
   rails db:create
   rails db:migrate
   rails db:seed
   ```

5. **Start the servers**
   ```bash
   ./bin/dev
   ```

   This starts:
   - Rails server on http://localhost:3000
   - Solid Queue for background jobs
   - TailwindCSS watcher
   - JavaScript build watcher

## 🔧 Development Setup

### Running with Docker

```bash
docker-compose up
```

### Database Setup

```bash
# Create and migrate database
rails db:setup

# Run migrations
rails db:migrate

# Seed with sample data
rails db:seed

# Reset database (careful!)
rails db:reset
```

### Running Tests

```bash
# Run all tests
bundle exec rspec

# Run specific test file
bundle exec rspec spec/models/form_spec.rb

# Run with coverage
COVERAGE=true bundle exec rspec

# Run system tests
bundle exec rspec spec/system

# Run JavaScript tests
yarn test
```

### Code Quality

```bash
# Run RuboCop
bundle exec rubocop

# Auto-fix RuboCop issues
bundle exec rubocop -a

# Run Brakeman security scan
bundle exec brakeman

# Run bundler-audit
bundle audit
```

### Background Jobs

```bash
# Start Solid Queue
bin/rails solid_queue:start

# Monitor Solid Queue
# Visit http://localhost:3000/solid_queue (requires authentication)
```

## 📦 Tracking Script Integration

Add the FormFlow Pro tracking script to any website:

```html
<!-- FormFlow Pro Tracking Script -->
<script async 
        src="https://app.formflowpro.com/track.js" 
        data-tracking-id="YOUR_TRACKING_ID">
</script>
```

Or install via npm:

```bash
npm install @formflowpro/tracker
```

```javascript
import FormFlowPro from '@formflowpro/tracker';

FormFlowPro.init({
  trackingId: 'YOUR_TRACKING_ID',
  options: {
    autoTrack: true,
    respectDoNotTrack: true,
    debug: false
  }
});
```

## 🔌 API Documentation

### Authentication

All API requests require authentication using JWT tokens:

```bash
curl -X POST https://api.formflowpro.com/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'
```

### Core Endpoints

#### Forms
```bash
# Get all forms
GET /api/v1/forms

# Get specific form
GET /api/v1/forms/:id

# Get form sessions
GET /api/v1/forms/:id/sessions

# Get form insights
GET /api/v1/forms/:id/insights
```

#### Analytics
```bash
# Get overview
GET /api/v1/analytics/overview

# Get form analytics
GET /api/v1/analytics/forms/:id

# Export data
GET /api/v1/analytics/export?format=csv
```

#### Tracking (Public)
```bash
# Send tracking event
POST /api/v1/track/event

# Batch events
POST /api/v1/track/batch
```

### Webhooks

Configure webhooks to receive real-time notifications:

```json
{
  "url": "https://your-app.com/webhooks/formflow",
  "events": ["form.abandoned", "insight.critical", "conversion.completed"],
  "secret": "your_webhook_secret"
}
```

## 🚢 Deployment

### Deployment with Kamal

```bash
# Initial setup
kamal setup

# Deploy application
kamal deploy

# Run migrations
kamal app exec 'rails db:migrate'

# Check logs
kamal app logs

# Open console
kamal app exec 'rails console'
```

### Alternative: Heroku Deployment

```bash
# Create Heroku app
heroku create your-app-name

# Add PostgreSQL
heroku addons:create heroku-postgresql:standard-0

# Configure environment
heroku config:set RAILS_MASTER_KEY=$(cat config/master.key)
heroku config:set RAILS_ENV=production

# Deploy
git push heroku main

# Run migrations
heroku run rails db:migrate
```

### Docker Deployment

```bash
# Build image
docker build -t formflowpro .

# Run with docker-compose
docker-compose -f docker-compose.production.yml up -d
```

### Environment Variables

Required environment variables for production:

```env
# Rails
RAILS_ENV=production
RAILS_MASTER_KEY=your_master_key
DATABASE_URL=postgresql://...
# Redis (optional, for advanced features)
REDIS_URL=redis://...

# Application
APP_HOST=app.formflowpro.com
APP_PROTOCOL=https

# Services
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
SENDGRID_API_KEY=SG...
SENTRY_DSN=https://...

# Storage
AWS_ACCESS_KEY_ID=...
AWS_SECRET_ACCESS_KEY=...
AWS_BUCKET=formflowpro-production
AWS_REGION=us-east-1
```

## 📊 Monitoring & Performance

### Application Monitoring

- **Sentry:** Error tracking and performance monitoring
- **NewRelic:** APM and infrastructure monitoring
- **Sidekiq Dashboard:** Background job monitoring
- **PgHero:** Database query analysis

### Key Metrics

- **Response Time:** < 200ms (p95)
- **Uptime:** 99.9% SLA
- **Background Jobs:** < 1 minute processing time
- **Database Queries:** < 50ms average

## 🧪 Testing Strategy

### Test Coverage Requirements
- **Models:** 100%
- **Controllers:** 95%
- **Services:** 95%
- **Overall:** 95%

### Test Types
- **Unit Tests:** Models, services, helpers
- **Integration Tests:** API endpoints, controllers
- **System Tests:** User flows, JavaScript interactions
- **Performance Tests:** Load testing, query optimization

### Continuous Integration

GitHub Actions workflow runs on every push:

```yaml
name: CI
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
      - name: Run tests
        run: |
          bundle install
          bundle exec rspec
      - name: Run security checks
        run: |
          bundle exec brakeman
          bundle audit
```

## 🤝 Contributing

We love contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Write tests for your changes
4. Implement your feature
5. Ensure all tests pass
6. Commit your changes (`git commit -m 'Add amazing feature'`)
7. Push to the branch (`git push origin feature/amazing-feature`)
8. Open a Pull Request

### Code Style

- Follow Ruby Style Guide (enforced by RuboCop)
- Use meaningful variable and method names
- Write self-documenting code
- Add comments only when necessary
- Keep methods small and focused
- Follow RESTful conventions

## 📝 Database Schema

```ruby
# Core models structure
Account
  ├── Subscription
  ├── Websites
  │   └── Forms
  │       ├── FormFields
  │       ├── Sessions
  │       │   └── FieldEvents
  │       └── Insights
  ├── TeamMembers
  └── ApiKeys
```

## 🔐 Security

### Security Features
- **Encryption:** All data encrypted at rest and in transit
- **Authentication:** JWT tokens with secure rotation
- **Authorization:** Role-based access control (RBAC)
- **Rate Limiting:** API rate limiting with Rack::Attack
- **CORS:** Properly configured CORS policies
- **CSP:** Content Security Policy headers
- **SQL Injection:** Parameterized queries
- **XSS Protection:** Input sanitization

### Reporting Security Issues

Please report security <NAME_EMAIL>

## 📚 Documentation

- [Product Requirements Document](PRD.md)
- [API Documentation](docs/api.md)
- [Tracking Script Guide](docs/tracking.md)
- [Deployment Guide](docs/deployment.md)
- [Contributing Guide](CONTRIBUTING.md)

## 🎯 Roadmap

### Q1 2025
- [x] Core tracking functionality
- [x] Basic analytics dashboard
- [x] AI-powered insights
- [ ] Stripe payment integration
- [ ] Public beta launch

### Q2 2025
- [ ] A/B testing framework
- [ ] Advanced segmentation
- [ ] Slack integration
- [ ] WordPress plugin
- [ ] Shopify app

### Q3 2025
- [ ] Machine learning insights
- [ ] Custom report builder
- [ ] White-label platform
- [ ] Enterprise features
- [ ] SAML SSO

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 💰 Pricing

| Plan | Price | Sessions | Forms | Features |
|------|-------|----------|-------|----------|
| **Starter** | $29/mo | 5,000 | 3 | Basic analytics, Email alerts |
| **Growth** | $79/mo | 25,000 | 10 | AI insights, API access, Priority support |
| **Scale** | $199/mo | 100,000 | Unlimited | White-label, Custom insights, SLA |

## 🙏 Acknowledgments

- Rails community for the amazing framework
- Our beta testers for invaluable feedback
- Open source contributors
- You, for checking out our project!

## 📞 Support

- **Documentation:** [docs.formflowpro.com](https://docs.formflowpro.com)
- **Email:** <EMAIL>
- **Twitter:** [@formflowpro](https://twitter.com/formflowpro)
- **Discord:** [Join our community](https://discord.gg/formflowpro)

---

**Built with ❤️ by developers who hate losing customers to bad forms.**

*FormFlow Pro - Stop losing customers at the finish line.*
