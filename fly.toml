# fly.toml app configuration file generated for formflowpro on 2025-09-01T08:17:18Z
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'formflowpro'
primary_region = 'lhr'
console_command = '/rails/bin/rails console'

[build]

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
  memory_mb = 1024
