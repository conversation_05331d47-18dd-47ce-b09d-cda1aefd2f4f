# FormFlow Pro Environment Configuration
# Copy this file to .env and update with your values

# Rails Configuration
RAILS_ENV=development
RAILS_MASTER_KEY=your_master_key_here

# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=formflowpro_development
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=password
DATABASE_URL=postgresql://postgres:password@localhost:5432/formflowpro_development

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Application Settings
APP_HOST=localhost:3000
APP_PROTOCOL=http
SECRET_KEY_BASE=your_secret_key_base_here

# Email Configuration (SendGrid)
SENDGRID_API_KEY=SG.your_sendgrid_api_key
SENDGRID_DOMAIN=formflowpro.com
DEFAULT_FROM_EMAIL=<EMAIL>

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret

# AWS Configuration (for file storage)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_BUCKET=formflowpro-development
AWS_REGION=us-east-1

# Sentry Configuration (Error Tracking)
SENTRY_DSN=https://<EMAIL>/project_id

# NewRelic Configuration (APM)
NEW_RELIC_LICENSE_KEY=your_new_relic_license_key
NEW_RELIC_APP_NAME=FormFlow Pro

# JWT Configuration
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_EXPIRY_HOURS=24

# Tracking Script Configuration
TRACKING_SCRIPT_CDN_URL=https://cdn.formflowpro.com
TRACKING_API_ENDPOINT=http://localhost:3000/api/v1/track

# Feature Flags
ENABLE_SIGNUP=true
ENABLE_AI_INSIGHTS=true
ENABLE_WEBHOOKS=true
ENABLE_WHITE_LABEL=false

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_TRACKING_PER_MINUTE=1000

# Analytics Settings
SESSION_TIMEOUT_MINUTES=30
DATA_RETENTION_DAYS=90
MAX_SESSIONS_PER_FORM=100000

# Background Jobs
SIDEKIQ_CONCURRENCY=10
SIDEKIQ_MAX_RETRIES=3

# Development Tools
BULLET_ENABLE=true
LETTER_OPENER=true

# Testing
CAPYBARA_DRIVER=selenium_chrome_headless
COVERAGE_MINIMUM=95

# Monitoring
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url
CRITICAL_ALERT_EMAIL=<EMAIL>

# Third-party Services
GOOGLE_ANALYTICS_ID=UA-XXXXXXXXX-X
INTERCOM_APP_ID=your_intercom_app_id
HOTJAR_SITE_ID=your_hotjar_site_id

# Security
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
SESSION_TIMEOUT_HOURS=24
FORCE_SSL=false
SECURE_COOKIES=false

# Pagination
DEFAULT_PAGE_SIZE=25
MAX_PAGE_SIZE=100
