# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

FormFlow Pro is a lightweight, intelligent form analytics platform built with Rails 8.0.2+ that helps businesses identify and fix form abandonment issues. The platform provides real-time insights into user behavior, pinpoints problem areas, and offers actionable recommendations to improve conversion rates.

## Key Commands

### Authentication Setup (Devise)
```bash
# Install Devise
bundle add devise
bundle install

# Generate Devise configuration
rails generate devise:install

# Generate User model with Devise
rails generate devise User

# Run migrations
rails db:migrate

# Generate Devise views (for customization)
rails generate devise:views
```

### Development Server
```bash
# Start all development servers (Rails, Tailwind CSS, Solid Queue, JS build)
./bin/dev

# Individual servers
bin/rails server -p 3000     # Rails server on http://localhost:3000
bin/rails tailwindcss:watch   # CSS compilation
bin/rails solid_queue:start   # Background job processor (Solid Queue)
yarn build --watch            # JavaScript build watcher
```

### Testing
```bash
# Run all tests
bundle exec rspec

# Run specific test file
bundle exec rspec spec/models/form_spec.rb

# Run with coverage report
COVERAGE=true bundle exec rspec

# Run system tests (browser tests)
bundle exec rspec spec/system

# Run JavaScript tests
yarn test
```

### Code Quality & Security
```bash
# Run RuboCop linter
bundle exec rubocop

# Auto-fix RuboCop issues
bundle exec rubocop -a

# Security scan
bundle exec brakeman

# Check for vulnerable dependencies
bundle audit
```

### Database Management
```bash
# Setup database (create, migrate, seed)
rails db:setup

# Run migrations
rails db:migrate

# Rollback migrations
rails db:rollback

# Seed sample data
rails db:seed

# Drop and recreate database (CAUTION: destroys all data)
rails db:reset
```

### Deployment
```bash
# Deploy with Kamal
kamal setup  # Initial setup
kamal deploy # Deploy updates

# Run production migrations
kamal app exec 'rails db:migrate'
```

## Architecture Overview

### Core Business Models
The application follows a multi-tenant SaaS architecture with these key relationships:
```
Account (tenant)
  ├── Users (Devise authentication, team members)
  │   └── Roles (owner, admin, member, viewer)
  ├── Subscription (Stripe integration)
  ├── Websites
  │   └── Forms
  │       ├── FormFields
  │       ├── Sessions (user interactions)
  │       │   └── FieldEvents (granular tracking)
  │       └── Insights (AI-generated recommendations)
  └── ApiKeys
```

### Rails 8 Stack Configuration
- **Authentication**: Devise gem for secure user authentication
- **Solid Queue**: Database-backed job processing (replaces Redis for jobs)
- **Solid Cache**: Disk-based caching with encryption
- **Solid Cable**: Database-backed WebSockets
- **Propshaft**: Simplified asset pipeline
- **Hotwire**: Turbo + Stimulus for interactive UIs without heavy JavaScript
- **Kamal**: Zero-dependency deployment solution

### Key Application Patterns

#### Service Objects
Business logic is encapsulated in service objects located in `app/services/`. These follow the pattern:
- Single responsibility per service
- `call` class method for execution
- Return result objects with success/failure states

#### Background Jobs
- Located in `app/jobs/`
- Processed by Solid Queue (database-backed, no Redis needed for jobs)
- Recurring jobs configured in `config/recurring.yml`
- Queue configuration in `config/queue.yml`
- Common jobs: insight generation, email reports, data aggregation
- Monitor at http://localhost:3000/solid_queue (requires authentication)

#### API Structure
- RESTful API endpoints in `app/controllers/api/v1/`
- Devise + JWT authentication for API access (devise-jwt gem)
- Tracking endpoints are public (with rate limiting)
- Business endpoints require authentication
- Multi-tenant scoping through current_account

#### Real-time Features
- ActionCable for WebSocket connections
- Solid Cable for database-backed pub/sub
- Real-time dashboard updates for form analytics
- Session replay functionality

### Frontend Architecture
- **Hotwire/Turbo**: SPA-like navigation without JavaScript framework
- **Stimulus Controllers**: Located in `app/javascript/controllers/`
- **TailwindCSS**: Utility-first CSS framework
- **Chart.js**: Data visualization for analytics

### Tracking Script
The tracking script (`public/track.js` or equivalent) is a lightweight JavaScript module that:
- Auto-detects forms on client websites
- Batches events for efficient transmission
- Respects privacy settings (no PII storage)
- Works with SPAs and dynamic forms

## Important Considerations

### Security & Privacy
- Devise for web authentication (with confirmable, lockable modules)
- Devise-JWT for API authentication
- Rack::Attack for rate limiting
- CORS configuration for tracking script
- No PII storage in tracking events
- GDPR/CCPA compliance in design
- Multi-tenant data isolation

### Performance Optimization
- Solid Cache for aggressive caching
- Counter caches for analytics queries
- Database indexes on frequently queried fields
- Batch processing for tracking events
- CDN for tracking script delivery

### Multi-tenancy
- Account-based data isolation
- Scoped queries through `current_account`
- Subscription-based feature gating
- Usage tracking for billing

### Testing Strategy
- RSpec for backend testing
- System tests with Capybara for UI flows
- Factory Bot for test data generation
- VCR for external API testing
- Target: 95% test coverage

## Deployment Notes

The application uses Kamal 2 for deployment, configured in `config/deploy.yml`. Key features:
- Thruster HTTP/2 proxy with X-Sendfile acceleration
- Zero-downtime deployments
- Built-in SSL via Let's Encrypt
- Database migrations run separately from deployment

## External Integrations

- **Stripe**: Payment processing and subscription management
- **SendGrid**: Transactional email delivery
- **Sentry**: Error tracking and monitoring
- **AWS S3**: File storage (if needed)
- **CloudFlare**: CDN and DDoS protection

## Development Workflow

1. Create a feature branch for each task
2. Write tests before implementation (TDD approach)
3. Ensure all tests pass
4. Run linters and fix issues
5. Create PR with clear description
6. Deploy after merge to main