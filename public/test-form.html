<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Form - FormFlow Pro Tracking</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: 500;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }
        button {
            background: #4f46e5;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
        }
        button:hover {
            background: #4338ca;
        }
        .required {
            color: #dc2626;
        }
        .info {
            background: #eff6ff;
            border-left: 4px solid #3b82f6;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Contact Form Test</h1>
        
        <div class="info">
            <strong>FormFlow Pro Tracking Test</strong><br>
            This form is being tracked by FormFlow Pro. Open the browser console to see tracking events.
        </div>

        <form id="contact-form" name="contact-form">
            <div class="form-group">
                <label for="name">Name <span class="required">*</span></label>
                <input type="text" id="name" name="name" required>
            </div>

            <div class="form-group">
                <label for="email">Email <span class="required">*</span></label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="phone">Phone</label>
                <input type="tel" id="phone" name="phone">
            </div>

            <div class="form-group">
                <label for="subject">Subject <span class="required">*</span></label>
                <select id="subject" name="subject" required>
                    <option value="">Select a subject</option>
                    <option value="general">General Inquiry</option>
                    <option value="support">Support</option>
                    <option value="sales">Sales</option>
                    <option value="feedback">Feedback</option>
                </select>
            </div>

            <div class="form-group">
                <label for="message">Message <span class="required">*</span></label>
                <textarea id="message" name="message" rows="5" required></textarea>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" name="newsletter" value="yes">
                    Subscribe to newsletter
                </label>
            </div>

            <button type="submit">Send Message</button>
        </form>
    </div>

    <!-- FormFlow Pro Tracking Code -->
    <script>
      (function() {
        var script = document.createElement('script');
        script.src = 'http://localhost:3000/api/v1/tracking/script/FFP-EC6852AF6A0C4663.js';
        script.async = true;
        script.setAttribute('data-tracking-id', 'FFP-EC6852AF6A0C4663');
        document.head.appendChild(script);
      })();
    </script>
    <!-- End FormFlow Pro Tracking Code -->

    <script>
        // Prevent actual form submission for testing
        document.getElementById('contact-form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Form submitted! Check the FormFlow Pro dashboard for tracking data.');
            // Reset form after a delay
            setTimeout(() => {
                this.reset();
            }, 1000);
        });
    </script>
</body>
</html>