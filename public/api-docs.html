<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FormFlow Pro - Tracking API Documentation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        h1, h2, h3 { color: #2563eb; }
        h1 { border-bottom: 3px solid #2563eb; padding-bottom: 10px; }
        h2 { margin-top: 40px; border-left: 4px solid #2563eb; padding-left: 15px; }
        pre {
            background: #1e293b;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 14px;
        }
        .endpoint {
            background: #f1f5f9;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .method {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
            margin-right: 10px;
        }
        .post { background: #059669; color: white; }
        .get { background: #0284c7; color: white; }
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        .required { color: #dc2626; }
        .optional { color: #059669; }
        .code-inline {
            background: #f3f4f6;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            font-size: 0.9em;
        }
        .warning {
            background: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 6px 6px 0;
        }
        .info {
            background: #dbeafe;
            border-left: 4px solid #3b82f6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 6px 6px 0;
        }
        .toc {
            background: #f8fafc;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 30px;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            padding: 5px 0;
        }
        .toc a {
            color: #2563eb;
            text-decoration: none;
        }
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>FormFlow Pro - Tracking API Documentation</h1>
        <p><strong>Version:</strong> 1.0.0 | <strong>Base URL:</strong> <code class="code-inline">https://your-domain.com/api/v1</code></p>
        
        <div class="toc">
            <h3>Table of Contents</h3>
            <ul>
                <li><a href="#overview">Overview</a></li>
                <li><a href="#authentication">Authentication</a></li>
                <li><a href="#quick-start">Quick Start</a></li>
                <li><a href="#endpoints">API Endpoints</a>
                    <ul style="margin-left: 20px;">
                        <li><a href="#session-start">Session Start</a></li>
                        <li><a href="#field-event">Field Event</a></li>
                        <li><a href="#form-submit">Form Submit</a></li>
                        <li><a href="#form-abandon">Form Abandon</a></li>
                        <li><a href="#batch">Batch Events</a></li>
                        <li><a href="#tracking-script">Tracking Script</a></li>
                    </ul>
                </li>
                <li><a href="#javascript-sdk">JavaScript SDK</a></li>
                <li><a href="#error-handling">Error Handling</a></li>
                <li><a href="#rate-limiting">Rate Limiting</a></li>
            </ul>
        </div>

        <h2 id="overview">Overview</h2>
        <p>The FormFlow Pro Tracking API allows you to collect detailed analytics about form interactions on your website. Track user behavior, field-level events, form submissions, and abandonment patterns to optimize your conversion rates.</p>

        <div class="info">
            <strong>Key Features:</strong>
            <ul>
                <li>Real-time form session tracking</li>
                <li>Field-level interaction analytics</li>
                <li>Conversion and abandonment tracking</li>
                <li>Device and browser detection</li>
                <li>Batch event processing</li>
                <li>Privacy-focused (no PII storage)</li>
            </ul>
        </div>

        <h2 id="authentication">Authentication</h2>
        <p>All tracking requests require a valid tracking ID. Include your tracking ID in the request headers:</p>
        <pre><code>X-Tracking-ID: your-tracking-id-here</code></pre>
        
        <div class="warning">
            <strong>Important:</strong> Your tracking ID should be kept secure and only used on verified domains. Invalid or unverified tracking IDs will result in a 401 Unauthorized response.
        </div>

        <h2 id="quick-start">Quick Start</h2>
        <p>The easiest way to integrate FormFlow Pro is to include our JavaScript tracking library:</p>
        
        <pre><code>&lt;!-- Add this before closing &lt;/body&gt; tag --&gt;
&lt;script src="https://your-domain.com/track.js" 
        data-ffp-tracking-id="YOUR_TRACKING_ID"
        data-ffp-debug="false"&gt;&lt;/script&gt;</code></pre>

        <p>For manual integration or custom implementations, use the API endpoints directly:</p>
        
        <pre><code>// Initialize tracking
FormFlowPro.init('YOUR_TRACKING_ID', {
  debug: true,
  apiUrl: 'https://your-domain.com/api/v1'
});</code></pre>

        <h2 id="endpoints">API Endpoints</h2>

        <div class="endpoint" id="session-start">
            <h3><span class="method post">POST</span> /tracking/session_start</h3>
            <p>Start a new form session when a user first interacts with a form.</p>
            
            <h4>Request Body</h4>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Required</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>tracking_id</code></td>
                            <td>string</td>
                            <td><span class="required">Required</span></td>
                            <td>Your website's tracking ID</td>
                        </tr>
                        <tr>
                            <td><code>form_identifier</code></td>
                            <td>string</td>
                            <td><span class="required">Required</span></td>
                            <td>Unique identifier for the form (ID, name, or custom identifier)</td>
                        </tr>
                        <tr>
                            <td><code>form_name</code></td>
                            <td>string</td>
                            <td><span class="optional">Optional</span></td>
                            <td>Human-readable form name</td>
                        </tr>
                        <tr>
                            <td><code>url</code></td>
                            <td>string</td>
                            <td><span class="required">Required</span></td>
                            <td>Page URL where the form is located</td>
                        </tr>
                        <tr>
                            <td><code>visitor_id</code></td>
                            <td>string</td>
                            <td><span class="optional">Optional</span></td>
                            <td>Unique visitor identifier (auto-generated if not provided)</td>
                        </tr>
                        <tr>
                            <td><code>referrer</code></td>
                            <td>string</td>
                            <td><span class="optional">Optional</span></td>
                            <td>Referrer URL</td>
                        </tr>
                        <tr>
                            <td><code>screen_resolution</code></td>
                            <td>string</td>
                            <td><span class="optional">Optional</span></td>
                            <td>Screen resolution (e.g., "1920x1080")</td>
                        </tr>
                        <tr>
                            <td><code>viewport_size</code></td>
                            <td>string</td>
                            <td><span class="optional">Optional</span></td>
                            <td>Viewport size (e.g., "1920x900")</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h4>Response</h4>
            <pre><code>{
  "session_id": "uuid-string",
  "form_id": 123
}</code></pre>
        </div>

        <div class="endpoint" id="field-event">
            <h3><span class="method post">POST</span> /tracking/field_event</h3>
            <p>Track field-level interactions such as focus, blur, change, and validation errors.</p>
            
            <h4>Request Body</h4>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Required</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>tracking_id</code></td>
                            <td>string</td>
                            <td><span class="required">Required</span></td>
                            <td>Your website's tracking ID</td>
                        </tr>
                        <tr>
                            <td><code>form_id</code></td>
                            <td>integer</td>
                            <td><span class="required">Required</span></td>
                            <td>Form ID from session_start response</td>
                        </tr>
                        <tr>
                            <td><code>session_id</code></td>
                            <td>string</td>
                            <td><span class="required">Required</span></td>
                            <td>Session ID from session_start response</td>
                        </tr>
                        <tr>
                            <td><code>field_name</code></td>
                            <td>string</td>
                            <td><span class="required">Required</span></td>
                            <td>Field name or identifier</td>
                        </tr>
                        <tr>
                            <td><code>field_type</code></td>
                            <td>string</td>
                            <td><span class="optional">Optional</span></td>
                            <td>Field type (text, email, select, etc.)</td>
                        </tr>
                        <tr>
                            <td><code>field_label</code></td>
                            <td>string</td>
                            <td><span class="optional">Optional</span></td>
                            <td>Field label text</td>
                        </tr>
                        <tr>
                            <td><code>event_type</code></td>
                            <td>string</td>
                            <td><span class="required">Required</span></td>
                            <td>Event type: focus, blur, change, input, error</td>
                        </tr>
                        <tr>
                            <td><code>timestamp</code></td>
                            <td>integer</td>
                            <td><span class="required">Required</span></td>
                            <td>Event timestamp in milliseconds</td>
                        </tr>
                        <tr>
                            <td><code>duration</code></td>
                            <td>integer</td>
                            <td><span class="optional">Optional</span></td>
                            <td>Time spent in field (for blur events)</td>
                        </tr>
                        <tr>
                            <td><code>value</code></td>
                            <td>string</td>
                            <td><span class="optional">Optional</span></td>
                            <td>Field value (truncated for privacy)</td>
                        </tr>
                        <tr>
                            <td><code>error_message</code></td>
                            <td>string</td>
                            <td><span class="optional">Optional</span></td>
                            <td>Validation error message</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h4>Response</h4>
            <pre><code>{
  "success": true,
  "event_id": 456
}</code></pre>
        </div>

        <div class="endpoint" id="form-submit">
            <h3><span class="method post">POST</span> /tracking/submit</h3>
            <p>Track successful form submissions.</p>
            
            <h4>Request Body</h4>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Required</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>tracking_id</code></td>
                            <td>string</td>
                            <td><span class="required">Required</span></td>
                            <td>Your website's tracking ID</td>
                        </tr>
                        <tr>
                            <td><code>form_id</code></td>
                            <td>integer</td>
                            <td><span class="required">Required</span></td>
                            <td>Form ID from session_start response</td>
                        </tr>
                        <tr>
                            <td><code>session_id</code></td>
                            <td>string</td>
                            <td><span class="required">Required</span></td>
                            <td>Session ID from session_start response</td>
                        </tr>
                        <tr>
                            <td><code>visitor_id</code></td>
                            <td>string</td>
                            <td><span class="required">Required</span></td>
                            <td>Visitor identifier</td>
                        </tr>
                        <tr>
                            <td><code>form_data</code></td>
                            <td>object</td>
                            <td><span class="optional">Optional</span></td>
                            <td>Non-PII form data (field presence, not values)</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h4>Response</h4>
            <pre><code>{
  "success": true,
  "submission_id": 789
}</code></pre>
        </div>

        <div class="endpoint" id="form-abandon">
            <h3><span class="method post">POST</span> /tracking/abandon</h3>
            <p>Track form abandonment when users leave without completing the form.</p>
            
            <h4>Request Body</h4>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Required</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>tracking_id</code></td>
                            <td>string</td>
                            <td><span class="required">Required</span></td>
                            <td>Your website's tracking ID</td>
                        </tr>
                        <tr>
                            <td><code>form_id</code></td>
                            <td>integer</td>
                            <td><span class="required">Required</span></td>
                            <td>Form ID from session_start response</td>
                        </tr>
                        <tr>
                            <td><code>session_id</code></td>
                            <td>string</td>
                            <td><span class="required">Required</span></td>
                            <td>Session ID from session_start response</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h4>Response</h4>
            <pre><code>{
  "success": true
}</code></pre>
        </div>

        <div class="endpoint" id="batch">
            <h3><span class="method post">POST</span> /tracking/batch</h3>
            <p>Process multiple events in a single request for improved performance.</p>
            
            <h4>Request Body</h4>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Type</th>
                            <th>Required</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>tracking_id</code></td>
                            <td>string</td>
                            <td><span class="required">Required</span></td>
                            <td>Your website's tracking ID</td>
                        </tr>
                        <tr>
                            <td><code>events</code></td>
                            <td>array</td>
                            <td><span class="required">Required</span></td>
                            <td>Array of event objects (max 50 events per batch)</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h4>Event Object Structure</h4>
            <pre><code>{
  "type": "field_focus|field_blur|field_change|form_submit|form_abandon",
  "form_id": 123,
  "session_id": "uuid-string",
  "timestamp": 1640995200000,
  "field": "email",
  "value": "user input",
  "duration": 5000
}</code></pre>

            <h4>Response</h4>
            <pre><code>{
  "success": true,
  "processed": 15,
  "total": 15,
  "errors": []
}</code></pre>
        </div>

        <div class="endpoint" id="tracking-script">
            <h3><span class="method get">GET</span> /tracking/script/{tracking_id}.js</h3>
            <p>Get the dynamic tracking script for your website.</p>
            
            <h4>Response</h4>
            <p>Returns JavaScript tracking code configured for your tracking ID.</p>
            <pre><code>// JavaScript tracking code
(function() {
  var FormFlowPro = {
    trackingId: 'YOUR_TRACKING_ID',
    // ... tracking implementation
  };
  // ... rest of tracking script
})();</code></pre>
        </div>

        <h2 id="javascript-sdk">JavaScript SDK</h2>
        <p>The FormFlow Pro JavaScript SDK provides automatic form detection and tracking:</p>

        <h3>Initialization</h3>
        <pre><code>// Initialize with options
FormFlowPro.init('YOUR_TRACKING_ID', {
  debug: false,              // Enable debug logging
  apiUrl: '/api/v1',        // Custom API endpoint
  batchTimeout: 2000,       // Batch timeout in ms
  maxBatchSize: 50,         // Max events per batch
  enableHeatmaps: true,     // Enable heatmap tracking
  enableReplay: false       // Enable session replay
});</code></pre>

        <h3>Manual Tracking</h3>
        <pre><code>// Track custom events
FormFlowPro.trackEvent({
  type: 'custom_event',
  data: { key: 'value' }
});

// Track specific form
FormFlowPro.trackForm(document.getElementById('myForm'), 'custom_form_id');</code></pre>

        <h3>Event Listeners</h3>
        <pre><code>// Listen for tracking events
window.addEventListener('ffp:session_start', function(event) {
  console.log('Form session started:', event.detail);
});

window.addEventListener('ffp:form_submit', function(event) {
  console.log('Form submitted:', event.detail);
});</code></pre>

        <h2 id="error-handling">Error Handling</h2>
        <p>The API returns standard HTTP status codes and detailed error messages:</p>

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Status Code</th>
                        <th>Description</th>
                        <th>Response</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>200</code></td>
                        <td>Success</td>
                        <td>Request processed successfully</td>
                    </tr>
                    <tr>
                        <td><code>400</code></td>
                        <td>Bad Request</td>
                        <td>Invalid or missing parameters</td>
                    </tr>
                    <tr>
                        <td><code>401</code></td>
                        <td>Unauthorized</td>
                        <td>Invalid tracking ID or unverified website</td>
                    </tr>
                    <tr>
                        <td><code>422</code></td>
                        <td>Unprocessable Entity</td>
                        <td>Invalid form or session ID</td>
                    </tr>
                    <tr>
                        <td><code>429</code></td>
                        <td>Too Many Requests</td>
                        <td>Rate limit exceeded</td>
                    </tr>
                    <tr>
                        <td><code>500</code></td>
                        <td>Internal Server Error</td>
                        <td>Server error occurred</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h4>Error Response Format</h4>
        <pre><code>{
  "error": "Invalid tracking ID or website not verified",
  "code": "UNAUTHORIZED",
  "details": {
    "tracking_id": "FFP-12345678"
  }
}</code></pre>

        <h2 id="rate-limiting">Rate Limiting</h2>
        <p>API requests are rate limited to prevent abuse:</p>
        
        <ul>
            <li><strong>Session Start:</strong> 100 requests per minute per tracking ID</li>
            <li><strong>Field Events:</strong> 1000 requests per minute per tracking ID</li>
            <li><strong>Batch Events:</strong> 50 requests per minute per tracking ID</li>
            <li><strong>Other Endpoints:</strong> 200 requests per minute per tracking ID</li>
        </ul>

        <div class="info">
            <strong>Rate Limit Headers:</strong> All responses include rate limit information in the headers:
            <ul>
                <li><code>X-RateLimit-Limit</code>: Request limit per window</li>
                <li><code>X-RateLimit-Remaining</code>: Remaining requests in current window</li>
                <li><code>X-RateLimit-Reset</code>: Time when rate limit resets (Unix timestamp)</li>
            </ul>
        </div>

        <div class="warning">
            <strong>Privacy Note:</strong> FormFlow Pro is designed with privacy in mind. We do not store personally identifiable information (PII) in form values. Only form structure, interaction patterns, and completion metrics are tracked.
        </div>

        <hr style="margin: 40px 0; border: none; border-top: 1px solid #e5e7eb;">
        
        <p><small>FormFlow Pro API Documentation - Last updated: January 2025</small></p>
    </div>
</body>
</html>