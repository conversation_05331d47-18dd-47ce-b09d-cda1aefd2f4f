/**
 * FormFlow Pro Tracking Script
 * Version: 1.0.0
 * 
 * This script automatically tracks form interactions and sends analytics data
 * to the FormFlow Pro platform.
 */

(function() {
  'use strict';

  // Configuration
  const FORMFLOW_CONFIG = {
    apiEndpoint: window.FORMFLOW_ENDPOINT || 'https://formflowpro.fly.dev/api/v1/track',
    websiteToken: window.FORMFLOW_TOKEN || null,
    batchSize: 10,
    flushInterval: 5000, // 5 seconds
    sessionTimeout: 30 * 60 * 1000, // 30 minutes
    debug: window.FORMFLOW_DEBUG || false
  };

  // State management
  const state = {
    sessionId: null,
    events: [],
    forms: new Map(),
    fieldInteractions: new Map(),
    lastActivity: Date.now()
  };

  // Utility functions
  const log = (...args) => {
    if (FORMFLOW_CONFIG.debug) {
      console.log('[FormFlow]', ...args);
    }
  };

  const generateId = () => {
    return Math.random().toString(36).substr(2, 9);
  };

  const getSessionId = () => {
    if (!state.sessionId || Date.now() - state.lastActivity > FORMFLOW_CONFIG.sessionTimeout) {
      state.sessionId = generateId();
      state.lastActivity = Date.now();
    }
    return state.sessionId;
  };

  const getFormIdentifier = (form) => {
    return form.id || form.name || form.action || generateId();
  };

  const getFieldIdentifier = (field) => {
    return field.id || field.name || field.type + '_' + generateId();
  };

  // Event tracking
  const trackEvent = (eventType, data) => {
    if (!FORMFLOW_CONFIG.websiteToken) {
      log('Warning: No website token configured');
      return;
    }

    const event = {
      type: eventType,
      timestamp: new Date().toISOString(),
      sessionId: getSessionId(),
      websiteToken: FORMFLOW_CONFIG.websiteToken,
      url: window.location.href,
      userAgent: navigator.userAgent,
      ...data
    };

    state.events.push(event);
    state.lastActivity = Date.now();
    log('Event tracked:', event);

    // Flush if batch size reached
    if (state.events.length >= FORMFLOW_CONFIG.batchSize) {
      flushEvents();
    }
  };

  // Send events to server
  const flushEvents = async () => {
    if (state.events.length === 0) return;

    const eventsToSend = [...state.events];
    state.events = [];

    try {
      const response = await fetch(FORMFLOW_CONFIG.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Website-Token': FORMFLOW_CONFIG.websiteToken
        },
        body: JSON.stringify({
          events: eventsToSend
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      log(`Flushed ${eventsToSend.length} events`);
    } catch (error) {
      log('Error flushing events:', error);
      // Re-add events to queue on failure
      state.events = eventsToSend.concat(state.events);
    }
  };

  // Form tracking handlers
  const handleFormStart = (form) => {
    const formId = getFormIdentifier(form);
    
    if (!state.forms.has(formId)) {
      state.forms.set(formId, {
        startTime: Date.now(),
        fields: new Set(),
        submitted: false
      });

      trackEvent('form_start', {
        formId: formId,
        formName: form.name || null,
        formAction: form.action || null,
        fieldCount: form.elements.length
      });
    }
  };

  const handleFieldFocus = (field, form) => {
    const formId = getFormIdentifier(form);
    const fieldId = getFieldIdentifier(field);
    const fieldKey = `${formId}_${fieldId}`;

    handleFormStart(form);

    if (!state.fieldInteractions.has(fieldKey)) {
      state.fieldInteractions.set(fieldKey, {
        focusTime: Date.now(),
        blurTime: null,
        changes: 0
      });

      trackEvent('field_focus', {
        formId: formId,
        fieldId: fieldId,
        fieldName: field.name || null,
        fieldType: field.type || null,
        fieldRequired: field.required || false
      });
    }
  };

  const handleFieldBlur = (field, form) => {
    const formId = getFormIdentifier(form);
    const fieldId = getFieldIdentifier(field);
    const fieldKey = `${formId}_${fieldId}`;

    const interaction = state.fieldInteractions.get(fieldKey);
    if (interaction && !interaction.blurTime) {
      interaction.blurTime = Date.now();
      const timeSpent = (interaction.blurTime - interaction.focusTime) / 1000;

      trackEvent('field_blur', {
        formId: formId,
        fieldId: fieldId,
        fieldName: field.name || null,
        timeSpent: timeSpent,
        hasValue: field.value && field.value.length > 0,
        changes: interaction.changes
      });
    }
  };

  const handleFieldChange = (field, form) => {
    const formId = getFormIdentifier(form);
    const fieldId = getFieldIdentifier(field);
    const fieldKey = `${formId}_${fieldId}`;

    const interaction = state.fieldInteractions.get(fieldKey);
    if (interaction) {
      interaction.changes++;
    }
  };

  const handleFormSubmit = (form) => {
    const formId = getFormIdentifier(form);
    const formData = state.forms.get(formId);

    if (formData && !formData.submitted) {
      formData.submitted = true;
      const timeSpent = (Date.now() - formData.startTime) / 1000;

      trackEvent('form_submit', {
        formId: formId,
        formName: form.name || null,
        timeSpent: timeSpent,
        fieldsCompleted: Array.from(form.elements).filter(field => 
          field.value && field.value.length > 0
        ).length,
        totalFields: form.elements.length
      });

      // Flush immediately on submit
      flushEvents();
    }
  };

  const handleFormAbandon = () => {
    state.forms.forEach((formData, formId) => {
      if (!formData.submitted) {
        const timeSpent = (Date.now() - formData.startTime) / 1000;
        
        trackEvent('form_abandon', {
          formId: formId,
          timeSpent: timeSpent,
          lastField: Array.from(state.fieldInteractions.keys())
            .filter(key => key.startsWith(formId))
            .pop()
        });
      }
    });

    flushEvents();
  };

  // Initialize tracking
  const initTracking = () => {
    // Track all forms on the page
    document.addEventListener('focusin', (event) => {
      const field = event.target;
      const form = field.closest('form');
      
      if (form && (field.tagName === 'INPUT' || field.tagName === 'TEXTAREA' || field.tagName === 'SELECT')) {
        handleFieldFocus(field, form);
      }
    });

    document.addEventListener('focusout', (event) => {
      const field = event.target;
      const form = field.closest('form');
      
      if (form && (field.tagName === 'INPUT' || field.tagName === 'TEXTAREA' || field.tagName === 'SELECT')) {
        handleFieldBlur(field, form);
      }
    });

    document.addEventListener('change', (event) => {
      const field = event.target;
      const form = field.closest('form');
      
      if (form && (field.tagName === 'INPUT' || field.tagName === 'TEXTAREA' || field.tagName === 'SELECT')) {
        handleFieldChange(field, form);
      }
    });

    document.addEventListener('submit', (event) => {
      const form = event.target;
      if (form.tagName === 'FORM') {
        handleFormSubmit(form);
      }
    });

    // Track page unload
    window.addEventListener('beforeunload', handleFormAbandon);
    
    // Periodic flush
    setInterval(flushEvents, FORMFLOW_CONFIG.flushInterval);

    // Track page view
    trackEvent('page_view', {
      title: document.title,
      referrer: document.referrer
    });

    log('FormFlow tracking initialized');
  };

  // Wait for DOM ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initTracking);
  } else {
    initTracking();
  }

  // Expose API for manual tracking
  window.FormFlow = {
    trackEvent: trackEvent,
    flushEvents: flushEvents,
    config: FORMFLOW_CONFIG
  };
})();