/**
 * FormFlow Pro Tracking Script
 * Intelligent form analytics and tracking
 * Version: 1.0.0
 */

(function(window, document, undefined) {
  'use strict';

  // Check if FormFlowPro is already initialized
  if (window.FormFlowPro) {
    console.warn('FormFlow Pro is already initialized');
    return;
  }

  const FormFlowPro = {
    // Configuration
    config: {
      apiUrl: null,
      trackingId: null,
      debug: false,
      batchTimeout: 2000,
      maxBatchSize: 50,
      sessionTimeout: 30 * 60 * 1000, // 30 minutes
      scrollThreshold: 25,
      enableHeatmaps: true,
      enableReplay: true
    },

    // State
    state: {
      visitorId: null,
      sessionId: null,
      sessions: {},
      eventQueue: [],
      batchTimer: null,
      formInteractions: {},
      pageLoadTime: Date.now(),
      lastActivity: Date.now()
    },

    // Initialize the tracker
    init: function(trackingId, options = {}) {
      if (!trackingId) {
        console.error('FormFlow Pro: Tracking ID is required');
        return;
      }

      this.config.trackingId = trackingId;
      this.config.apiUrl = options.apiUrl || this.detectApiUrl();
      Object.assign(this.config, options);

      this.state.visitorId = this.getOrCreateVisitorId();
      this.setupEventListeners();
      this.trackPageView();
      this.startHeartbeat();

      if (this.config.debug) {
        console.log('FormFlow Pro initialized', {
          trackingId: this.config.trackingId,
          visitorId: this.state.visitorId,
          apiUrl: this.config.apiUrl
        });
      }

      // Auto-detect and track forms
      this.autoDetectForms();
      this.observeNewForms();
    },

    // Detect API URL from script tag
    detectApiUrl: function() {
      const scripts = document.getElementsByTagName('script');
      for (let script of scripts) {
        if (script.src && script.src.includes('track.js')) {
          const url = new URL(script.src);
          return `${url.protocol}//${url.host}/api/v1`;
        }
      }
      return '/api/v1';
    },

    // Get or create visitor ID
    getOrCreateVisitorId: function() {
      let visitorId = this.getCookie('ffp_visitor_id');
      if (!visitorId) {
        visitorId = localStorage.getItem('ffp_visitor_id');
      }
      if (!visitorId) {
        visitorId = 'v_' + this.generateUUID();
        this.setCookie('ffp_visitor_id', visitorId, 365);
        localStorage.setItem('ffp_visitor_id', visitorId);
      }
      return visitorId;
    },

    // Auto-detect forms on the page
    autoDetectForms: function() {
      const forms = document.querySelectorAll('form');
      forms.forEach((form, index) => {
        const formId = this.getFormIdentifier(form, index);
        this.trackForm(form, formId);
      });
    },

    // Observe DOM for new forms (for SPAs)
    observeNewForms: function() {
      if (!window.MutationObserver) return;

      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeName === 'FORM') {
              const formId = this.getFormIdentifier(node);
              this.trackForm(node, formId);
            } else if (node.querySelectorAll) {
              const forms = node.querySelectorAll('form');
              forms.forEach((form) => {
                const formId = this.getFormIdentifier(form);
                this.trackForm(form, formId);
              });
            }
          });
        });
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    },

    // Get form identifier
    getFormIdentifier: function(form, index) {
      return form.id || 
             form.name || 
             form.getAttribute('data-form-name') || 
             `form_${index || this.generateHash(form.outerHTML)}`;
    },

    // Track a specific form
    trackForm: function(form, formId) {
      if (form.hasAttribute('data-ffp-tracked')) return;
      form.setAttribute('data-ffp-tracked', 'true');

      const formData = {
        formId: formId,
        formName: form.name || formId,
        url: window.location.href,
        fields: [],
        startTime: null,
        sessionId: null,
        interactions: {},
        errorCount: 0
      };

      // Analyze form fields
      const fields = form.querySelectorAll('input, textarea, select');
      fields.forEach((field, index) => {
        const fieldData = {
          name: field.name || field.id || `field_${index}`,
          type: field.type || field.tagName.toLowerCase(),
          required: field.required,
          label: this.getFieldLabel(field),
          position: index
        };
        formData.fields.push(fieldData);

        // Track field-level events
        this.trackFieldEvents(field, formId, fieldData);
      });

      // Track form-level events
      this.trackFormEvents(form, formId, formData);

      this.state.formInteractions[formId] = formData;
    },

    // Track field events
    trackFieldEvents: function(field, formId, fieldData) {
      const events = ['focus', 'blur', 'change', 'input', 'invalid'];
      const fieldInteractions = {
        focusCount: 0,
        blurCount: 0,
        changeCount: 0,
        totalTime: 0,
        lastFocus: null,
        errors: [],
        corrections: 0
      };

      events.forEach(eventType => {
        field.addEventListener(eventType, (e) => {
          const timestamp = Date.now();
          
          // Start form session on first interaction
          if (!this.state.formInteractions[formId].sessionId && eventType === 'focus') {
            this.startFormSession(formId);
          }

          switch(eventType) {
            case 'focus':
              fieldInteractions.focusCount++;
              fieldInteractions.lastFocus = timestamp;
              this.queueEvent({
                type: 'field_focus',
                formId: formId,
                sessionId: this.state.formInteractions[formId].sessionId,
                field: fieldData.name,
                timestamp: timestamp
              });
              break;

            case 'blur':
              fieldInteractions.blurCount++;
              if (fieldInteractions.lastFocus) {
                const duration = timestamp - fieldInteractions.lastFocus;
                fieldInteractions.totalTime += duration;
                this.queueEvent({
                  type: 'field_blur',
                  formId: formId,
                  sessionId: this.state.formInteractions[formId].sessionId,
                  field: fieldData.name,
                  duration: duration,
                  value: field.value ? field.value.substring(0, 50) : '', // Truncate for privacy
                  timestamp: timestamp
                });
              }
              break;

            case 'change':
              fieldInteractions.changeCount++;
              if (fieldInteractions.changeCount > 1) {
                fieldInteractions.corrections++;
              }
              this.queueEvent({
                type: 'field_change',
                formId: formId,
                sessionId: this.state.formInteractions[formId].sessionId,
                field: fieldData.name,
                hasValue: !!field.value,
                timestamp: timestamp
              });
              break;

            case 'invalid':
              const errorMessage = field.validationMessage;
              fieldInteractions.errors.push({
                message: errorMessage,
                timestamp: timestamp
              });
              this.state.formInteractions[formId].errorCount++;
              this.queueEvent({
                type: 'field_error',
                formId: formId,
                sessionId: this.state.formInteractions[formId].sessionId,
                field: fieldData.name,
                error: errorMessage,
                timestamp: timestamp
              });
              break;
          }

          this.state.formInteractions[formId].interactions[fieldData.name] = fieldInteractions;
        });
      });
    },

    // Track form events
    trackFormEvents: function(form, formId, formData) {
      // Form submission
      form.addEventListener('submit', (e) => {
        const sessionId = this.state.formInteractions[formId].sessionId;
        if (!sessionId) return;

        const completionTime = Date.now() - this.state.formInteractions[formId].startTime;
        
        // Collect non-PII form data
        const fields = form.querySelectorAll('input, textarea, select');
        const formValues = {};
        fields.forEach(field => {
          if (field.name && !this.isPIIField(field)) {
            formValues[field.name] = field.type === 'checkbox' ? field.checked :
                                    field.type === 'radio' ? field.checked :
                                    field.value ? 'filled' : 'empty';
          }
        });

        this.queueEvent({
          type: 'form_submit',
          formId: formId,
          sessionId: sessionId,
          completionTime: completionTime,
          errorCount: formData.errorCount,
          fieldCount: formData.fields.length,
          filledFields: Object.values(formValues).filter(v => v === 'filled' || v === true).length,
          timestamp: Date.now()
        }, true); // Force immediate send
      });

      // Form abandonment (page unload)
      window.addEventListener('beforeunload', () => {
        if (this.state.formInteractions[formId].sessionId && 
            this.state.formInteractions[formId].startTime) {
          this.queueEvent({
            type: 'form_abandon',
            formId: formId,
            sessionId: this.state.formInteractions[formId].sessionId,
            timeSpent: Date.now() - this.state.formInteractions[formId].startTime,
            lastField: this.getLastInteractedField(formId),
            timestamp: Date.now()
          }, true); // Force immediate send
        }
      });
    },

    // Start form session
    startFormSession: function(formId) {
      const sessionData = {
        tracking_id: this.config.trackingId,
        form_identifier: formId,
        form_name: this.state.formInteractions[formId].formName,
        url: window.location.href,
        referrer: document.referrer,
        visitor_id: this.state.visitorId,
        screen_resolution: `${window.screen.width}x${window.screen.height}`,
        viewport_size: `${window.innerWidth}x${window.innerHeight}`,
        color_depth: window.screen.colorDepth,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        language: navigator.language
      };

      this.sendRequest('/tracking/session_start', sessionData, (response) => {
        if (response.session_id) {
          this.state.formInteractions[formId].sessionId = response.session_id;
          this.state.formInteractions[formId].startTime = Date.now();
          this.state.sessions[response.session_id] = formId;
        }
      });
    },

    // Queue event for batch sending
    queueEvent: function(event, immediate = false) {
      event.visitor_id = this.state.visitorId;
      event.tracking_id = this.config.trackingId;
      event.page_url = window.location.href;
      
      this.state.eventQueue.push(event);

      if (immediate || this.state.eventQueue.length >= this.config.maxBatchSize) {
        this.flushEvents();
      } else {
        this.scheduleBatch();
      }
    },

    // Schedule batch sending
    scheduleBatch: function() {
      if (this.state.batchTimer) return;

      this.state.batchTimer = setTimeout(() => {
        this.flushEvents();
      }, this.config.batchTimeout);
    },

    // Flush events to server
    flushEvents: function() {
      if (this.state.eventQueue.length === 0) return;

      const events = [...this.state.eventQueue];
      this.state.eventQueue = [];

      if (this.state.batchTimer) {
        clearTimeout(this.state.batchTimer);
        this.state.batchTimer = null;
      }

      this.sendRequest('/tracking/batch', { events: events }, (response) => {
        if (this.config.debug) {
          console.log('FormFlow Pro: Batch sent', { count: events.length, response });
        }
      });
    },

    // Send request to API
    sendRequest: function(endpoint, data, callback) {
      const xhr = new XMLHttpRequest();
      xhr.open('POST', this.config.apiUrl + endpoint, true);
      xhr.setRequestHeader('Content-Type', 'application/json');
      xhr.setRequestHeader('X-Tracking-ID', this.config.trackingId);

      xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
          if (xhr.status === 200 || xhr.status === 201) {
            try {
              const response = JSON.parse(xhr.responseText);
              if (callback) callback(response);
            } catch (e) {
              console.error('FormFlow Pro: Failed to parse response', e);
            }
          } else if (this.config.debug) {
            console.error('FormFlow Pro: Request failed', {
              endpoint,
              status: xhr.status,
              response: xhr.responseText
            });
          }
        }
      }.bind(this);

      xhr.send(JSON.stringify(data));
    },

    // Track page view
    trackPageView: function() {
      this.queueEvent({
        type: 'page_view',
        title: document.title,
        path: window.location.pathname,
        timestamp: Date.now()
      });
    },

    // Setup global event listeners
    setupEventListeners: function() {
      // Track rage clicks
      let clickCount = 0;
      let lastClickTime = 0;
      document.addEventListener('click', (e) => {
        const now = Date.now();
        if (now - lastClickTime < 500) {
          clickCount++;
          if (clickCount >= 3) {
            this.queueEvent({
              type: 'rage_click',
              target: e.target.tagName,
              x: e.clientX,
              y: e.clientY,
              timestamp: now
            });
            clickCount = 0;
          }
        } else {
          clickCount = 1;
        }
        lastClickTime = now;
      });

      // Track scroll depth
      let maxScroll = 0;
      let scrollTimer;
      window.addEventListener('scroll', () => {
        clearTimeout(scrollTimer);
        scrollTimer = setTimeout(() => {
          const scrollPercent = Math.round((window.scrollY / 
            (document.documentElement.scrollHeight - window.innerHeight)) * 100);
          
          if (scrollPercent > maxScroll && scrollPercent % this.config.scrollThreshold === 0) {
            maxScroll = scrollPercent;
            this.queueEvent({
              type: 'scroll_depth',
              depth: scrollPercent,
              timestamp: Date.now()
            });
          }
        }, 150);
      });

      // Track time on page
      let hiddenTime = 0;
      let hiddenStart = null;
      document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
          hiddenStart = Date.now();
        } else if (hiddenStart) {
          hiddenTime += Date.now() - hiddenStart;
          hiddenStart = null;
        }
      });

      // Send time on page before unload
      window.addEventListener('beforeunload', () => {
        const totalTime = Date.now() - this.state.pageLoadTime;
        const activeTime = totalTime - hiddenTime;
        this.queueEvent({
          type: 'page_unload',
          total_time: totalTime,
          active_time: activeTime,
          timestamp: Date.now()
        }, true);
      });
    },

    // Start heartbeat for session tracking
    startHeartbeat: function() {
      setInterval(() => {
        const now = Date.now();
        if (now - this.state.lastActivity > this.config.sessionTimeout) {
          // Session expired, create new session on next interaction
          this.state.sessionId = null;
          Object.keys(this.state.formInteractions).forEach(formId => {
            this.state.formInteractions[formId].sessionId = null;
          });
        }
      }, 60000); // Check every minute
    },

    // Helper functions
    getFieldLabel: function(field) {
      // Try to find associated label
      let label = field.getAttribute('aria-label');
      if (!label && field.id) {
        const labelElement = document.querySelector(`label[for="${field.id}"]`);
        if (labelElement) {
          label = labelElement.textContent.trim();
        }
      }
      if (!label) {
        label = field.placeholder || field.name || field.id || 'Unnamed field';
      }
      return label;
    },

    getLastInteractedField: function(formId) {
      const interactions = this.state.formInteractions[formId].interactions;
      let lastField = null;
      let lastTime = 0;

      Object.keys(interactions).forEach(fieldName => {
        const fieldData = interactions[fieldName];
        if (fieldData.lastFocus && fieldData.lastFocus > lastTime) {
          lastTime = fieldData.lastFocus;
          lastField = fieldName;
        }
      });

      return lastField;
    },

    isPIIField: function(field) {
      const piiPatterns = [
        'ssn', 'social', 'tax', 'tin', 'credit', 'card', 'cvv', 
        'account', 'routing', 'license', 'passport', 'password'
      ];
      const fieldName = (field.name || field.id || '').toLowerCase();
      return piiPatterns.some(pattern => fieldName.includes(pattern));
    },

    generateUUID: function() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    },

    generateHash: function(str) {
      let hash = 0;
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
      }
      return Math.abs(hash).toString(36);
    },

    setCookie: function(name, value, days) {
      const expires = new Date();
      expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
      document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Strict`;
    },

    getCookie: function(name) {
      const nameEQ = name + '=';
      const ca = document.cookie.split(';');
      for (let i = 0; i < ca.length; i++) {
        let c = ca[i].trim();
        if (c.indexOf(nameEQ) === 0) {
          return c.substring(nameEQ.length);
        }
      }
      return null;
    }
  };

  // Expose FormFlowPro globally
  window.FormFlowPro = FormFlowPro;

  // Auto-initialize if tracking ID is in script tag
  document.addEventListener('DOMContentLoaded', function() {
    const scripts = document.getElementsByTagName('script');
    for (let script of scripts) {
      const trackingId = script.getAttribute('data-ffp-tracking-id');
      if (trackingId) {
        const options = {
          debug: script.getAttribute('data-ffp-debug') === 'true',
          apiUrl: script.getAttribute('data-ffp-api-url')
        };
        FormFlowPro.init(trackingId, options);
        break;
      }
    }
  });

})(window, document);