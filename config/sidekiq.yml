# Sidekiq Configuration

:verbose: false
:concurrency: 10
:timeout: 25

# <PERSON><PERSON><PERSON> will run this file through ERB when reading it so you can
# even put in dynamic logic, like a host-specific queue.
#

:queues:
  - critical
  - default
  - low
  - mailers

# Configure Sidekiq-specific session middleware
Sidekiq.configure_server do |config|
  config.redis = { url: ENV.fetch('REDIS_URL', 'redis://localhost:6379/0') }
end

Sidekiq.configure_client do |config|
  config.redis = { url: ENV.fetch('REDIS_URL', 'redis://localhost:6379/0') }
end
