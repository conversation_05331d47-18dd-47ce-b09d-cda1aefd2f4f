Rails.application.routes.draw do
  devise_for :users, controllers: {
    sessions: "users/sessions",
    registrations: "users/registrations",
    passwords: "users/passwords",
    confirmations: "users/confirmations"
  }

  # Authenticated routes
  authenticated :user do
    root "dashboard#index", as: :authenticated_root
  end

  # Public root
  root "home#index"

  # Dashboard
  get "dashboard", to: "dashboard#index", as: :dashboard
  get "dashboard/monitoring", to: "dashboard#monitoring", as: :monitoring_dashboard

  # Analytics
  resources :analytics, only: [ :index ] do
    collection do
      get :export
    end
  end

  # Insights
  resources :insights, only: [ :index, :show ] do
    member do
      patch :resolve
      patch :dismiss
    end
    collection do
      patch :bulk_resolve
      get :export
    end
  end

  # Settings
  get "settings", to: "settings#index"
  get "settings/profile", to: "settings#profile"
  patch "settings/profile", to: "settings#update_profile"
  get "settings/account", to: "settings#account"
  patch "settings/account", to: "settings#update_account"
  get "settings/billing", to: "settings#billing"
  get "settings/team", to: "settings#team"
  post "settings/team/invite", to: "settings#invite_member"
  delete "settings/team/:member_id", to: "settings#remove_member", as: :remove_team_member
  get "settings/notifications", to: "settings#notifications"
  patch "settings/notifications", to: "settings#update_notifications"
  get "settings/security", to: "settings#security"
  get "settings/api_keys", to: "settings#api_keys"
  post "settings/api_keys", to: "settings#create_api_key"
  delete "settings/api_keys/:id", to: "settings#revoke_api_key", as: :revoke_api_key
  get "settings/integrations", to: "settings#integrations"
  post "settings/export", to: "settings#export_data"
  delete "settings/account", to: "settings#delete_account"

  # Resources
  resources :websites do
    member do
      post :verify
    end
  end

  resources :forms, only: [ :show ] do
    member do
      get :heatmap
      get :session_replay
    end

    resources :session_replays, only: [ :index, :show ], path: "sessions"
  end

  # API routes
  namespace :api do
    namespace :v1 do
      # Tracking endpoints
      post "tracking/session_start", to: "tracking#session_start"
      post "tracking/field_event", to: "tracking#field_event"
      post "tracking/submit", to: "tracking#submit"
      post "tracking/abandon", to: "tracking#abandon"
      post "tracking/batch", to: "tracking#batch"
      get "tracking/script/:id", to: "tracking#script", as: :tracking_script

      # CORS preflight for all tracking endpoints
      match "tracking/*path", to: "tracking#options", via: :options
    end
  end

  # API Documentation
  get "api/documentation", to: "api#documentation", as: :api_documentation

  # Legal pages
  get "terms", to: "legal#terms", as: :terms
  get "privacy", to: "legal#privacy", as: :privacy

  # Company pages
  get "about", to: "about#index", as: :about
  get "contact", to: "contact#index", as: :contact

  # Health check
  get "up" => "rails/health#show", as: :rails_health_check
end
