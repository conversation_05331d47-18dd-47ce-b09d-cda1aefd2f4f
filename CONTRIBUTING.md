# Contributing to FormFlow Pro

First off, thank you for considering contributing to FormFlow Pro! It's people like you that make FormFlow Pro such a great tool for helping businesses improve their form conversion rates.

## Code of Conduct

This project and everyone participating in it is governed by our Code of Conduct. By participating, you are expected to uphold this code. Please report unacceptable <NAME_EMAIL>.

## How Can I Contribute?

### Reporting Bugs

Before creating bug reports, please check existing issues as you might find out that you don't need to create one. When you are creating a bug report, please include as many details as possible:

* **Use a clear and descriptive title** for the issue to identify the problem
* **Describe the exact steps which reproduce the problem** in as many details as possible
* **Provide specific examples to demonstrate the steps**
* **Describe the behavior you observed after following the steps**
* **Explain which behavior you expected to see instead and why**
* **Include screenshots and animated GIFs** if possible
* **Include your environment details** (OS, Ruby version, browser, etc.)

### Suggesting Enhancements

Enhancement suggestions are tracked as GitHub issues. When creating an enhancement suggestion, please include:

* **Use a clear and descriptive title** for the issue to identify the suggestion
* **Provide a step-by-step description of the suggested enhancement**
* **Provide specific examples to demonstrate the steps**
* **Describe the current behavior** and **explain which behavior you expected to see instead**
* **Explain why this enhancement would be useful** to most FormFlow Pro users

### Pull Requests

* Fill in the required template
* Do not include issue numbers in the PR title
* Include screenshots and animated GIFs in your pull request whenever possible
* Follow the Ruby style guide enforced by RuboCop
* Include thoughtfully-worded, well-structured tests
* Document new code with YARD comments
* End all files with a newline

## Development Process

1. **Fork & Clone**
   ```bash
   git clone https://github.com/your-username/formflowpro.git
   cd formflowpro
   ```

2. **Setup Development Environment**
   ```bash
   bundle install
   yarn install
   rails db:setup
   ```

3. **Create a Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

4. **Write Tests First (TDD)**
   ```ruby
   # spec/models/your_model_spec.rb
   RSpec.describe YourModel do
     it "does something useful" do
       # Write your test
     end
   end
   ```

5. **Implement Your Feature**
   - Follow existing patterns and conventions
   - Keep methods small and focused
   - Use descriptive variable names
   - Add comments only when necessary

6. **Run Tests & Quality Checks**
   ```bash
   # Run tests
   bundle exec rspec
   
   # Check code style
   bundle exec rubocop
   
   # Security check
   bundle exec brakeman
   
   # Check test coverage
   COVERAGE=true bundle exec rspec
   ```

7. **Commit Your Changes**
   ```bash
   git add .
   git commit -m "Add feature: brief description
   
   Longer explanation of what changed and why
   
   Closes #123"
   ```

8. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

## Style Guide

### Ruby Style Guide

We use RuboCop with the Rails Omakase configuration. Key points:

```ruby
# Good
class FormAnalyzer
  def analyze(form)
    return unless form.valid?
    
    insights = generate_insights(form)
    store_insights(insights)
    notify_if_critical(insights)
  end
  
  private
  
  def generate_insights(form)
    # Implementation
  end
end

# Bad
class form_analyzer
  def Analyze(f)
    if f.valid? == true
      insights = self.generateInsights(f)
      self.storeInsights(insights)
      self.notifyIfCritical(insights)
    end
  end
end
```

### JavaScript Style Guide

```javascript
// Good
class FormTracker {
  constructor(options) {
    this.options = options;
    this.events = [];
  }
  
  trackEvent(event) {
    this.events.push(event);
    this.sendIfBatchFull();
  }
}

// Bad
function form_tracker(opts) {
  var self = this;
  self.opts = opts;
  self.events = new Array();
}
```

### Git Commit Messages

* Use the present tense ("Add feature" not "Added feature")
* Use the imperative mood ("Move cursor to..." not "Moves cursor to...")
* Limit the first line to 72 characters or less
* Reference issues and pull requests liberally after the first line
* Consider starting the commit message with an applicable emoji:
  * 🎨 `:art:` when improving the format/structure of the code
  * 🐎 `:racehorse:` when improving performance
  * 📝 `:memo:` when writing docs
  * 🐛 `:bug:` when fixing a bug
  * 🔥 `:fire:` when removing code or files
  * 💚 `:green_heart:` when fixing the CI build
  * ✅ `:white_check_mark:` when adding tests
  * 🔒 `:lock:` when dealing with security
  * ⬆️ `:arrow_up:` when upgrading dependencies
  * ⬇️ `:arrow_down:` when downgrading dependencies

## Testing Guidelines

### Test Structure

```ruby
RSpec.describe Form do
  describe "validations" do
    it { should validate_presence_of(:url) }
  end
  
  describe "#abandonment_rate" do
    context "with completed sessions" do
      let(:form) { create(:form) }
      
      before do
        create_list(:session, 3, form: form, status: 'completed')
        create_list(:session, 7, form: form, status: 'abandoned')
      end
      
      it "calculates the correct rate" do
        expect(form.abandonment_rate).to eq(70.0)
      end
    end
    
    context "with no sessions" do
      let(:form) { create(:form) }
      
      it "returns zero" do
        expect(form.abandonment_rate).to eq(0.0)
      end
    end
  end
end
```

### Test Coverage

We maintain a minimum of 95% test coverage. Check coverage with:

```bash
COVERAGE=true bundle exec rspec
open coverage/index.html
```

## Documentation

* Use YARD for Ruby documentation
* Document all public methods
* Include examples in documentation
* Keep README.md up to date

```ruby
# Good documentation
##
# Calculates the abandonment rate for a form
#
# @return [Float] the abandonment rate as a percentage (0-100)
# @example Calculate abandonment rate
#   form.abandonment_rate #=> 35.5
#
def abandonment_rate
  # Implementation
end
```

## Project Structure

```
formflowpro/
├── app/
│   ├── controllers/      # Request handling
│   ├── models/           # Business logic
│   ├── services/         # Complex operations
│   ├── jobs/            # Background tasks
│   └── javascript/      # Frontend code
├── config/              # Configuration
├── db/                  # Database files
├── lib/                 # Custom libraries
├── public/              # Static files
├── spec/                # Tests
└── docs/                # Documentation
```

## Questions?

Feel free to open an issue with the "question" label or reach out on our Discord server.

## Recognition

Contributors who submit accepted PRs will be:
* Added to our Contributors list
* Receive FormFlow Pro swag (for significant contributions)
* Get a free FormFlow Pro Growth account

Thank you for contributing! 🎉
